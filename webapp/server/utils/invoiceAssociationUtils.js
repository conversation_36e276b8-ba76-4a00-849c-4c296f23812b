import Invoice from '../models/Invoice.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import InvoiceItem from '../models/InvoiceItem.js';

/**
 * Safely query an invoice with customer associations
 * This function handles association errors gracefully and provides fallback mechanisms
 * 
 * @param {string} invoiceId - The invoice ID to query
 * @param {Object} options - Query options
 * @returns {Promise<Object|null>} The invoice with associations or null if not found
 */
export const safeGetInvoiceWithCustomer = async (invoiceId, options = {}) => {
  try {
    // First, try the normal query with associations
    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: Customer,
          as: 'customer',
          required: false,
          attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        },
        {
          model: Farm,
          as: 'recipientFarm',
          required: false,
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
        },
        {
          model: Farm,
          as: 'issuingFarm',
          required: false,
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
        },
        {
          model: InvoiceItem,
          as: 'invoiceItems',
          required: false,
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount', 'taxable']
        }
      ],
      ...options
    });

    // If the invoice has an issuingFarm, map it to Farm for frontend compatibility
    if (invoice && invoice.issuingFarm) {
      invoice.Farm = invoice.issuingFarm;
    }

    // Ensure recipientFarm is properly set even if it's null in the database
    if (invoice && invoice.recipient_farm_id && !invoice.recipientFarm) {
      try {
        invoice.recipientFarm = await Farm.findByPk(invoice.recipient_farm_id, {
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
        });
      } catch (error) {
        console.error('Error fetching recipient farm:', error.message);
      }
    }

    return invoice;

  } catch (associationError) {
    console.error('Association error in safeGetInvoiceWithCustomer:', associationError.message);

    // Fallback: Get invoice without associations and manually fetch related data
    const invoice = await Invoice.findByPk(invoiceId, options);

    if (!invoice) {
      return null;
    }

    // Manually fetch related data
    try {
      if (invoice.customer_id) {
        invoice.customer = await Customer.findByPk(invoice.customer_id, {
          attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        });
      }

      if (invoice.farm_id) {
        invoice.issuingFarm = await Farm.findByPk(invoice.farm_id, {
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
        });
        // Map issuingFarm to Farm for frontend compatibility
        invoice.Farm = invoice.issuingFarm;
      }

      if (invoice.recipient_farm_id) {
        invoice.recipientFarm = await Farm.findByPk(invoice.recipient_farm_id, {
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
        });
      }

      // Manually fetch invoice items
      invoice.invoiceItems = await InvoiceItem.findAll({
        where: { invoice_id: invoiceId },
        attributes: ['id', 'description', 'quantity', 'unit_price', 'amount', 'taxable']
      });

    } catch (fallbackError) {
      console.error('Error in fallback data fetching:', fallbackError.message);
      // Set empty arrays/objects for missing associations
      if (!invoice.customer && invoice.customer_id) invoice.customer = null;
      if (!invoice.issuingFarm && invoice.farm_id) invoice.issuingFarm = null;
      if (!invoice.recipientFarm && invoice.recipient_farm_id) invoice.recipientFarm = null;
      if (!invoice.invoiceItems) invoice.invoiceItems = [];

      // Map issuingFarm to Farm for frontend compatibility in fallback case
      if (invoice.issuingFarm) {
        invoice.Farm = invoice.issuingFarm;
      }
    }

    return invoice;
  }
};

/**
 * Safely query multiple invoices with customer associations
 * 
 * @param {Object} whereClause - Sequelize where clause
 * @param {Object} options - Query options
 * @returns {Promise<Array>} Array of invoices with associations
 */
export const safeGetInvoicesWithCustomer = async (whereClause = {}, options = {}) => {
  try {
    // First, try the normal query with associations
    const invoices = await Invoice.findAll({
      where: whereClause,
      include: [
        {
          model: Customer,
          as: 'customer',
          required: false,
          attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        },
        {
          model: Farm,
          as: 'recipientFarm',
          required: false,
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        },
        {
          model: Farm,
          as: 'issuingFarm',
          required: false,
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        },
        {
          model: InvoiceItem,
          as: 'invoiceItems',
          required: false,
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount', 'taxable']
        }
      ],
      ...options
    });

    // Map issuingFarm to Farm for frontend compatibility for each invoice
    for (const invoice of invoices) {
      if (invoice.issuingFarm) {
        invoice.Farm = invoice.issuingFarm;
      }
    }

    return invoices;

  } catch (associationError) {
    console.error('Association error in safeGetInvoicesWithCustomer:', associationError.message);

    // Fallback: Get invoices without associations
    const invoices = await Invoice.findAll({
      where: whereClause,
      ...options
    });

    // For each invoice, try to manually fetch related data
    for (const invoice of invoices) {
      try {
        // Fetch customer data if needed
        if (invoice.customer_id) {
          invoice.customer = await Customer.findByPk(invoice.customer_id, {
            attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
          });
        }

        // Fetch issuing farm data if needed
        if (invoice.farm_id) {
          invoice.issuingFarm = await Farm.findByPk(invoice.farm_id, {
            attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
          });
          // Map issuingFarm to Farm for frontend compatibility
          invoice.Farm = invoice.issuingFarm;
        }

        // Fetch recipient farm data if needed
        if (invoice.recipient_farm_id) {
          invoice.recipientFarm = await Farm.findByPk(invoice.recipient_farm_id, {
            attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
          });
        }

        // Fetch invoice items
        invoice.invoiceItems = await InvoiceItem.findAll({
          where: { invoice_id: invoice.id },
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount', 'taxable']
        });
      } catch (error) {
        console.error(`Error fetching related data for invoice ${invoice.id}:`, error.message);
        if (invoice.customer_id && !invoice.customer) invoice.customer = null;
        if (invoice.farm_id && !invoice.issuingFarm) invoice.issuingFarm = null;
        if (invoice.recipient_farm_id && !invoice.recipientFarm) invoice.recipientFarm = null;
        if (!invoice.invoiceItems) invoice.invoiceItems = [];
      }
    }

    return invoices;
  }
};

/**
 * Check if Customer-Invoice associations are properly set up
 * 
 * @returns {boolean} True if associations are working, false otherwise
 */
export const checkCustomerInvoiceAssociations = () => {
  try {
    const invoiceHasCustomer = Invoice.associations && Invoice.associations.customer;
    const customerHasInvoices = Customer.associations && Customer.associations.invoices;

    console.log('Invoice -> Customer association:', invoiceHasCustomer ? '✅' : '❌');
    console.log('Customer -> Invoices association:', customerHasInvoices ? '✅' : '❌');

    return invoiceHasCustomer && customerHasInvoices;
  } catch (error) {
    console.error('Error checking associations:', error.message);
    return false;
  }
};
