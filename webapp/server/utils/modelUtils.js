import { sequelize } from '../config/database.js';
import { getSchema } from './schemaUtils.js';

/**
 * Creates a Sequelize model with dynamic schema support
 * 
 * @param {string} modelName - The name of the model
 * @param {Object} attributes - The model attributes
 * @param {Object} options - Additional model options
 * @returns {Object} The Sequelize model
 */
export const defineModel = (modelName, attributes, options = {}) => {
  // Get the current schema name
  const schema = getSchema(options.schemaOptions || {});

  // Merge the schema into the options
  const modelOptions = {
    ...options,
    schema,
    // Don't override tableName if it's already set
    tableName: options.tableName || modelName.toLowerCase() + 's',
  };

  // Define the model with Sequelize
  const model = sequelize.define(modelName, attributes, modelOptions);

  // Add a method to get the current schema
  model.getSchema = () => getSchema(options.schemaOptions || {});

  return model;
};
