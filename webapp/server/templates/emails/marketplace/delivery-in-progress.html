<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Your Delivery is on the Way</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #4CAF50;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #4CAF50;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .order-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .delivery-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .alert-message {
      background-color: #fff3cd;
      border: 1px solid #ffeeba;
      color: #856404;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 20px;
    }
    .truck-icon {
      text-align: center;
      margin: 20px 0;
      font-size: 48px;
    }
    .map-placeholder {
      width: 100%;
      height: 200px;
      background-color: #e9ecef;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20px 0;
    }
    .eta {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      margin: 20px 0;
      padding: 10px;
      background-color: #e9f7ef;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Your Delivery is on the Way</h1>
    </div>
    <div class="content">
      <div class="alert-message">
        <p><strong>Heads up!</strong> Your order from {{farmName}} is out for delivery.</p>
      </div>
      
      <p>Hello {{customerName}},</p>
      
      <p>Good news! Your order is now on its way to you.</p>
      
      <div class="truck-icon">
        🚚
      </div>
      
      <div class="eta">
        <p>Estimated Time of Arrival: {{estimatedArrival}}</p>
      </div>
      
      {{#if driverTrackingEnabled}}
      <p><strong>Track Your Delivery:</strong></p>
      <p>You can track your delivery in real-time by clicking the button below:</p>
      <a href="{{trackingUrl}}" class="button">Track Delivery</a>
      
      <div class="map-placeholder">
        <p>Click "Track Delivery" to view live location</p>
      </div>
      
      <p><strong>Driver Information:</strong></p>
      <p>Driver Name: {{driverName}}</p>
      <p>Vehicle: {{vehicleDescription}}</p>
      {{/if}}
      
      <div class="delivery-details">
        <p><strong>Delivery Address:</strong><br>
          {{deliveryAddress}}<br>
          {{deliveryCity}}, {{deliveryState}} {{deliveryZipCode}}
        </p>
        
        {{#if deliveryInstructions}}
        <p><strong>Delivery Instructions:</strong></p>
        <p>{{deliveryInstructions}}</p>
        {{/if}}
      </div>
      
      <p><strong>Order Details:</strong></p>
      <div class="order-details">
        <p><strong>Order ID:</strong> {{orderId}}</p>
        <p><strong>Items:</strong></p>
        {{#each items}}
        <p>{{quantity}} x {{productName}}</p>
        {{/each}}
      </div>
      
      <p>If you have any questions or need to provide additional delivery instructions, please contact the driver directly or {{farmName}}.</p>
      
      <p>Thank you for your order!</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>