import express from 'express';
import { 
  createRecurringInvoice, 
  getRecurringInvoice, 
  getFarmRecurringInvoices, 
  updateRecurringInvoice, 
  deleteRecurringInvoice,
  generateRecurringInvoices
} from '../controllers/recurringInvoiceController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Create a recurring invoice
router.post('/', createRecurringInvoice);

// Get a recurring invoice by ID
router.get('/:recurringInvoiceId', getRecurringInvoice);

// Get all recurring invoices for a farm
router.get('/farm/:farmId', getFarmRecurringInvoices);

// Get recurring invoice by invoice ID
router.get('/invoice/:invoiceId', async (req, res) => {
  try {
    const { invoiceId } = req.params;

    // Import models here to avoid circular dependencies
    const RecurringInvoice = (await import('../models/RecurringInvoice.js')).default;
    const Invoice = (await import('../models/Invoice.js')).default;
    const Customer = (await import('../models/Customer.js')).default;
    const Farm = (await import('../models/Farm.js')).default;

    const recurringInvoice = await RecurringInvoice.findOne({
      where: { invoice_id: invoiceId },
      include: [
        {
          model: Invoice,
          as: 'invoice',
          include: [
            {
              model: Customer,
              as: 'customer',
              required: false
            },
            {
              model: Farm,
              as: 'recipientFarm',
              required: false
            }
          ]
        }
      ]
    });

    if (!recurringInvoice) {
      return res.status(404).json({ error: 'Recurring invoice not found for this invoice' });
    }

    // Check if user has permission to view this recurring invoice
    const userFarmId = req.farmId || req.user.activeFarmId;

    if (!req.user.is_global_admin && recurringInvoice.invoice.farm_id !== userFarmId) {
      return res.status(403).json({
        error: 'You can only view recurring invoices for your farm.'
      });
    }

    return res.status(200).json({ recurringInvoice });
  } catch (error) {
    console.error('Error getting recurring invoice by invoice ID:', error);
    return res.status(500).json({ error: error.message });
  }
});

// Update a recurring invoice
router.put('/:recurringInvoiceId', updateRecurringInvoice);

// Delete a recurring invoice
router.delete('/:recurringInvoiceId', deleteRecurringInvoice);

// Generate invoices for all due recurring invoices
router.post('/generate', generateRecurringInvoices);

export default router;
