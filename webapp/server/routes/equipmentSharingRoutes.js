import express from 'express';
import {
  getFarmEquipmentSharing,
  getOwnedEquipmentSharing,
  getRentedEquipmentSharing,
  getEquipmentSharingById,
  createEquipmentSharing,
  updateEquipmentSharing,
  updateSharingStatus,
  deleteEquipmentSharing
} from '../controllers/equipmentSharingController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Get all equipment sharing records for a farm (both as owner and renter)
router.get('/farm/:farmId', authenticate, getFarmEquipmentSharing);

// Get equipment sharing records where farm is the owner
router.get('/owned/:farmId', authenticate, getOwnedEquipmentSharing);

// Get equipment sharing records where farm is the renter
router.get('/rented/:farmId', authenticate, getRentedEquipmentSharing);

// Get a single equipment sharing record by ID
router.get('/:sharingId', authenticate, getEquipmentSharingById);

// Create a new equipment sharing record
router.post('/', authenticate, createEquipmentSharing);

// Update an equipment sharing record
router.put('/:sharingId', authenticate, updateEquipmentSharing);

// Update the status of an equipment sharing record
router.patch('/:sharingId/status', authenticate, updateSharingStatus);

// Delete an equipment sharing record
router.delete('/:sharingId', authenticate, deleteEquipmentSharing);

export default router;
