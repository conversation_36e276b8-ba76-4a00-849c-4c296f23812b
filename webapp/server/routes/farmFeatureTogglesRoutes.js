import express from 'express';
const router = express.Router();
import { authenticate as authMiddleware, authorize as farmAccessMiddleware } from '../middleware/authMiddleware.js';
import * as farmFeatureTogglesController from '../controllers/farmFeatureTogglesController.js';

// GET /api/farms/:farmId/feature-toggles
// Get feature toggles for a farm
// Private access: Farm Owner, Farm Admin
router.get(
  '/:farmId/feature-toggles',
  authMiddleware,
  farmAccessMiddleware(['owner', 'farm_owner', 'farm_admin']),
  farmFeatureTogglesController.getFarmFeatureToggles
);

// PUT /api/farms/:farmId/feature-toggles
// Update feature toggles for a farm
// Private access: Farm Owner, Farm Admin
router.put(
  '/:farmId/feature-toggles',
  authMiddleware,
  farmAccessMiddleware(['owner', 'farm_owner', 'farm_admin']),
  farmFeatureTogglesController.updateFarmFeatureToggles
);

export default router;
