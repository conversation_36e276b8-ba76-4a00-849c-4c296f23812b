import express from 'express';
import {
  getFarmIoTDevices,
  getIoTDevice,
  createIoTDevice,
  updateIoTDevice,
  deleteIoTDevice,
  getDeviceIoTData,
  getLatestIoTData,
  createIoTData,
  receiveExternalIoTData,
  getAggregatedIoTData
} from '../controllers/iotController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// IoT Device routes
// Get all IoT devices for a farm
router.get('/farm/:farmId', authenticate, getFarmIoTDevices);

// Get a specific IoT device
router.get('/device/:deviceId', authenticate, getIoTDevice);

// Create a new IoT device
router.post('/device', authenticate, createIoTDevice);

// Update an IoT device
router.put('/device/:deviceId', authenticate, updateIoTDevice);

// Delete an IoT device
router.delete('/device/:deviceId', authenticate, deleteIoTDevice);

// IoT Data routes
// Get IoT data for a specific device
router.get('/device/:deviceId/data', authenticate, getDeviceIoTData);

// Get the latest IoT data for a device
router.get('/device/:deviceId/data/latest', authenticate, getLatestIoTData);

// Create new IoT data record
router.post('/data', authenticate, createIoTData);

// Get aggregated IoT data for reporting
router.get('/device/:deviceId/data/aggregated', authenticate, getAggregatedIoTData);

// Receive IoT data from external systems
// This endpoint is typically not authenticated with the same mechanism
// as it's used by external systems
router.post('/data/external', receiveExternalIoTData);

export default router;
