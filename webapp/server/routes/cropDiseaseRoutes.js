import express from 'express';
import {
  getCropDiseasePredictions,
  getCropDiseasePredictionById,
  createCropDiseasePrediction,
  generateCropDiseasePrediction,
  deleteCropDiseasePrediction
} from '../controllers/cropDiseaseController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Get all crop disease predictions for a farm
router.get('/', getCropDiseasePredictions);

// Get a single crop disease prediction by ID
router.get('/:predictionId', getCropDiseasePredictionById);

// Create a new crop disease prediction
router.post('/', createCropDiseasePrediction);

// Generate a crop disease prediction using AI
router.post('/generate', generateCropDiseasePrediction);

// Delete a crop disease prediction
router.delete('/:predictionId', deleteCropDiseasePrediction);

export default router;