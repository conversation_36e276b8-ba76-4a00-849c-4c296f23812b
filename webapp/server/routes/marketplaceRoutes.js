import express from 'express';
import {
  getMarketplaceProducts,
  getFarmMarketplaceProducts,
  getMarketplaceCategories,
  getMarketplaceProduct
} from '../controllers/marketplaceController.js';
import {
  getMarketplaceFarmFulfillmentOptions,
  setCartFulfillmentMethod
} from '../controllers/fulfillmentOptionsController.js';
import { optionalAuthenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// The marketplace is public, so we use optionalAuthenticate to allow both authenticated and unauthenticated access
// If the user is authenticated, we can personalize the experience

// Get all products visible in the marketplace
router.get('/products', optionalAuthenticate, getMarketplaceProducts);

// Get all products for a specific farm in the marketplace
router.get('/farms/:farmId/products', optionalAuthenticate, getFarmMarketplaceProducts);

// Get product categories for marketplace filtering
router.get('/products/categories', optionalAuthenticate, getMarketplaceCategories);

// Get a single product from the marketplace
router.get('/products/:productId', optionalAuthenticate, getMarketplaceProduct);

// Get farm fulfillment options for marketplace
router.get('/farms/:farmId/fulfillment-options', optionalAuthenticate, getMarketplaceFarmFulfillmentOptions);

// Set fulfillment method for cart
router.post('/cart/fulfillment-method', optionalAuthenticate, setCartFulfillmentMethod);

export default router;
