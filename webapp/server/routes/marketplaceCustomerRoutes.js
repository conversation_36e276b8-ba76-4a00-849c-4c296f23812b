import express from 'express';
import * as marketplaceCustomerController from '../controllers/marketplaceCustomerController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Public routes (no authentication required)
router.post('/register', marketplaceCustomerController.registerCustomer);
router.post('/login', marketplaceCustomerController.loginCustomer);

// Protected routes (authentication required)
router.get('/profile', authenticate, marketplaceCustomerController.getCustomerProfile);
router.put('/profile', authenticate, marketplaceCustomerController.updateCustomerProfile);
router.post('/addresses', authenticate, marketplaceCustomerController.addCustomerAddress);
router.put('/addresses/:addressId', authenticate, marketplaceCustomerController.updateCustomerAddress);
router.delete('/addresses/:addressId', authenticate, marketplaceCustomerController.deleteCustomerAddress);
router.get('/orders', authenticate, marketplaceCustomerController.getCustomerOrders);

export default router;