import axios from 'axios';

/**
 * Test script for market prices API
 * 
 * To run this test:
 * 1. Start the server in one terminal:
 *    cd webapp
 *    npm run dev:server
 * 
 * 2. Run this test script in another terminal:
 *    cd webapp/server
 *    node test-market-prices.js
 * 
 * This will test the market prices API and show whether it's using real data or fallback data.
 */
async function testMarketPricesAPI() {
  try {
    console.log('Testing market prices API...');

    // Test parameters
    const params = {
      lat: 37.7749,
      lon: -122.4194,
      commodities: 'corn,wheat,soybeans'
    };

    console.log(`Requesting market prices for: ${params.commodities}`);
    console.log(`Location: Lat ${params.lat}, Lon ${params.lon}`);

    // Make the API request
    const response = await axios.get('http://localhost:3002/api/market-prices', { params });

    console.log('\nAPI Response:');
    console.log('Status:', response.status);

    if (response.data) {
      console.log('\nCurrent Prices:');
      response.data.currentPrices.forEach(price => {
        console.log(`- ${price.commodity}: $${price.price} per ${price.unit} (${price.location})`);
        console.log(`  Source: ${price.api_source}${price.isFallback ? ' (FALLBACK DATA)' : ''}`);
      });

      console.log('\nFuture Prices:');
      if (response.data.futurePrices && response.data.futurePrices.length > 0) {
        response.data.futurePrices.slice(0, 5).forEach(future => {
          console.log(`- ${future.commodity}: $${future.price} per ${future.unit} (${future.month} ${future.year})`);
        });

        if (response.data.futurePrices.length > 5) {
          console.log(`  ... and ${response.data.futurePrices.length - 5} more`);
        }
      } else {
        console.log('  No future prices available');
      }

      console.log('\nLast Updated:', response.data.lastUpdated);
    }
  } catch (error) {
    console.error('Error testing market prices API:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

// Run the test
testMarketPricesAPI();
