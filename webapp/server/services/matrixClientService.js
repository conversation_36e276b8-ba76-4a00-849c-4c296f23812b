import { createClient } from 'matrix-js-sdk';
import { getNxtAcreUserIdToMatrixUserId, getMatrixUserIdToNxtAcreUserId, registerMatrixUser, getMatrixUser } from '../utils/matrixUtils.js';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import axios from 'axios';
import WebSocket from 'ws';

dotenv.config();

// Create crypto store directory if it doesn't exist
const cryptoStoreDir = path.join(process.cwd(), 'crypto-store');
if (!fs.existsSync(cryptoStoreDir)) {
  fs.mkdirSync(cryptoStoreDir, { recursive: true });
}

/**
 * Matrix Client Service
 * Provides a clean API for interacting with Matrix Synapse
 */
class MatrixClientService {
  constructor() {
    this.matrixServerUrl = process.env.MATRIX_SERVER_URL || 'https://chat.nxtacre.com';
    this.matrixDomain = process.env.MATRIX_DOMAIN || 'chat.nxtacre.com';
    this.adminClient = null;
    this.userClients = new Map(); // Cache of user clients
    this.successfulWebSocketUrl = null; // Store successful WebSocket URL

    // Log the Matrix server URL and domain
    console.log(`Matrix Client Service initialized with server URL: ${this.matrixServerUrl}`);
    console.log(`Matrix Client Service initialized with domain: ${this.matrixDomain}`);

    // Fallback URLs to try if the primary URL fails
    this.fallbackUrls = [
      'http://synapse:8008',
      'http://localhost:8008',
      'https://matrix.chat.nxtacre.com'
    ].filter(url => url !== this.matrixServerUrl);

    // Test WebSocket connections on initialization
    this.testWebSocketConnections();
  }

  /**
   * Test WebSocket connections to find a working URL format
   * @returns {Promise<string|null>} The successful WebSocket URL or null if none work
   */
  async testWebSocketConnections() {
    // Different WebSocket URL formats to try
    const wsUrlFormats = [
      this.matrixServerUrl.replace(/^https?:\/\//, 'wss://') + '/_matrix/client/ws',
      this.matrixServerUrl.replace(/^https?:\/\//, 'ws://') + '/_matrix/client/ws',
      // Try without the /ws suffix as some deployments might not need it
      this.matrixServerUrl.replace(/^https?:\/\//, 'wss://') + '/_matrix/client',
      // Try with explicit port 443 for wss
      this.matrixServerUrl.replace(/^https?:\/\//, 'wss://').replace(/\/$/, '') + ':443/_matrix/client/ws'
    ];

    // Also try fallback URLs with the same formats
    for (const fallbackUrl of this.fallbackUrls) {
      wsUrlFormats.push(fallbackUrl.replace(/^https?:\/\//, 'wss://') + '/_matrix/client/ws');
      wsUrlFormats.push(fallbackUrl.replace(/^https?:\/\//, 'ws://') + '/_matrix/client/ws');
    }

    console.log('Testing WebSocket connections to find a working URL format...');

    for (const wsUrl of wsUrlFormats) {
      try {
        console.log(`Testing WebSocket connection to: ${wsUrl}`);

        const success = await new Promise((resolve) => {
          try {
            const ws = new WebSocket(wsUrl);

            ws.on('open', () => {
              console.log(`WebSocket connection to ${wsUrl} successful`);
              ws.close();
              resolve(true);
            });

            ws.on('error', (error) => {
              console.error(`WebSocket connection to ${wsUrl} failed:`, error.message);
              resolve(false);
            });

            // Set a timeout in case the connection hangs
            setTimeout(() => {
              if (ws.readyState !== WebSocket.OPEN && ws.readyState !== WebSocket.CLOSED) {
                console.warn(`WebSocket connection to ${wsUrl} timed out`);
                ws.terminate();
                resolve(false);
              }
            }, 5000);
          } catch (error) {
            console.error(`Error creating WebSocket to ${wsUrl}:`, error.message);
            resolve(false);
          }
        });

        if (success) {
          console.log(`Found working WebSocket URL: ${wsUrl}`);
          this.successfulWebSocketUrl = wsUrl;
          return wsUrl;
        }
      } catch (error) {
        console.error(`Error testing WebSocket URL ${wsUrl}:`, error.message);
      }
    }

    console.warn('No working WebSocket URL found. Matrix real-time functionality may be limited.');
    return null;
  }

  /**
   * Get an admin token for Matrix Synapse
   * @returns {Promise<string>} The admin access token
   */
  async getAdminToken() {
    // Check if MATRIX_ADMIN_TOKEN is set in environment variables
    if (process.env.MATRIX_ADMIN_TOKEN) {
      console.log('Using MATRIX_ADMIN_TOKEN from environment variables');
      return process.env.MATRIX_ADMIN_TOKEN;
    }

    // If MATRIX_ADMIN_TOKEN is not set, try to login with username and password
    const adminUsername = process.env.MATRIX_ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.MATRIX_ADMIN_PASSWORD;

    if (!adminPassword) {
      throw new Error('Neither MATRIX_ADMIN_TOKEN nor MATRIX_ADMIN_PASSWORD is set');
    }

    let lastError = null;
    // Try the primary URL first, then fallback URLs if needed
    const urlsToTry = [this.matrixServerUrl, ...this.fallbackUrls];

    for (const url of urlsToTry) {
      try {
        console.log(`Attempting to login as admin at: ${url}`);

        // Login as admin
        const response = await axios.post(`${url}/_matrix/client/r0/login`, {
          type: 'm.login.password',
          identifier: {
            type: 'm.id.user',
            user: adminUsername
          },
          password: adminPassword
        });

        if (response.data && response.data.access_token) {
          console.log(`Successfully logged in as admin at: ${url}`);

          // Update the primary URL if we're using a fallback
          if (url !== this.matrixServerUrl) {
            console.log(`Updating primary Matrix server URL from ${this.matrixServerUrl} to ${url}`);
            this.matrixServerUrl = url;
          }

          return response.data.access_token;
        }

        throw new Error('No access token in login response');
      } catch (error) {
        console.error(`Error logging in as admin at ${url}:`, error.message);
        lastError = error;
        continue; // Try the next URL
      }
    }

    // If we get here, all URLs failed
    console.error('Failed to login as admin at all Matrix server URLs. Last error:', lastError);
    throw new Error('Failed to login as admin at any Matrix server URL');
  }

  /**
   * Get an admin client for Matrix Synapse
   * @returns {Promise<Object>} The Matrix admin client
   */
  async getAdminClient() {
    if (this.adminClient) {
      return this.adminClient;
    }

    let lastError = null;
    // Try the primary URL first, then fallback URLs if needed
    const urlsToTry = [this.matrixServerUrl, ...this.fallbackUrls];

    for (const url of urlsToTry) {
      try {
        console.log(`Attempting to connect to Matrix server at: ${url}`);
        const adminToken = await this.getAdminToken();
        const adminUserId = `@${process.env.MATRIX_ADMIN_USERNAME || 'admin'}:${this.matrixDomain}`;

        // Create client with encryption enabled
        const client = createClient({
          baseUrl: url,
          accessToken: adminToken,
          userId: adminUserId,
          cryptoStore: {
            // Use a unique store name for the admin
            indexedDBName: 'matrix-js-sdk',
            storePrefix: `admin-crypto-store`,
          },
          deviceId: `ADMIN_DEVICE_${Date.now()}`,
          // Add WebSocket URL for real-time communication with multiple format options
          websocketUrl: this.successfulWebSocketUrl || (url.replace(/^https?:\/\//, 'wss://') + '/_matrix/client/ws'),
          // Enable detailed logging
          timelineSupport: true,
          useAuthorizationHeader: false,
          syncParams: {
            timeout: 30000,
          },
        });

        // Log the WebSocket URL being used
        console.log(`Matrix admin client created with WebSocket URL: ${this.successfulWebSocketUrl || (url.replace(/^https?:\/\//, 'wss://') + '/_matrix/client/ws')}`);

        // Add event listeners for WebSocket events
        client.on('WebSocket.open', () => {
          console.log('Admin client: WebSocket connection opened successfully');
        });

        client.on('WebSocket.error', (error) => {
          console.error('Admin client: WebSocket connection error:', error);
        });

        client.on('WebSocket.close', (code, reason) => {
          console.warn(`Admin client: WebSocket connection closed: Code ${code}, Reason: ${reason}`);
        });

        // Initialize crypto
        await client.initCrypto();

        // Test the connection by making a simple API call
        try {
          await client.getVersions();
          console.log(`Successfully connected to Matrix server at: ${url}`);

          // If we get here, the connection was successful
          this.adminClient = client;

          // Update the primary URL if we're using a fallback
          if (url !== this.matrixServerUrl) {
            console.log(`Updating primary Matrix server URL from ${this.matrixServerUrl} to ${url}`);
            this.matrixServerUrl = url;
          }

          return this.adminClient;
        } catch (testError) {
          console.error(`Failed to connect to Matrix server at ${url}:`, testError);
          lastError = testError;
          continue; // Try the next URL
        }
      } catch (error) {
        console.error(`Error creating admin client with URL ${url}:`, error);
        lastError = error;
        continue; // Try the next URL
      }
    }

    // If we get here, all URLs failed
    console.error('All Matrix server URLs failed. Last error:', lastError);
    throw new Error('Failed to connect to any Matrix server URL');
  }

  /**
   * Register a new user in Matrix Synapse
   * @param {string} username - The username to register (without the @ prefix or domain)
   * @param {string} password - The password for the user (optional if using shared secret)
   * @param {boolean} admin - Whether the user should be an admin (default: false)
   * @returns {Promise<Object>} The response from the Matrix server
   */
  async registerMatrixUser(username, password = null, admin = false) {
    const registrationSharedSecret = process.env.REGISTRATION_SHARED_SECRET;

    if (!registrationSharedSecret) {
      throw new Error('REGISTRATION_SHARED_SECRET is not set');
    }

    let lastError = null;
    // Try the primary URL first, then fallback URLs if needed
    const urlsToTry = [this.matrixServerUrl, ...this.fallbackUrls];

    for (const url of urlsToTry) {
      try {
        console.log(`Attempting to register user ${username} at: ${url}`);

        // Generate a random nonce
        let nonce;
        try {
          const nonceResponse = await axios.get(`${url}/_synapse/admin/v1/register`);
          nonce = nonceResponse.data.nonce;
        } catch (nonceError) {
          console.error(`Error getting nonce from ${url}:`, nonceError.message);
          lastError = nonceError;
          continue; // Try the next URL
        }

        // Create the HMAC
        const crypto = require('crypto');
        const hmac = crypto.createHmac('sha1', registrationSharedSecret);
        hmac.update(nonce);
        hmac.update('\0');
        hmac.update(username);
        hmac.update('\0');
        hmac.update(password || '');
        hmac.update('\0');
        hmac.update(admin ? 'admin' : 'notadmin');
        const mac = hmac.digest('hex');

        // Register the user
        const response = await axios.post(`${url}/_synapse/admin/v1/register`, {
          nonce,
          username,
          password: password || undefined,
          admin,
          mac
        });

        if (response.data) {
          console.log(`Successfully registered user ${username} at: ${url}`);

          // Update the primary URL if we're using a fallback
          if (url !== this.matrixServerUrl) {
            console.log(`Updating primary Matrix server URL from ${this.matrixServerUrl} to ${url}`);
            this.matrixServerUrl = url;
          }

          return response.data;
        }
      } catch (error) {
        console.error(`Error registering user ${username} at ${url}:`, error.message);
        lastError = error;
        continue; // Try the next URL
      }
    }

    // If we get here, all URLs failed
    console.error(`Failed to register user ${username} at all Matrix server URLs. Last error:`, lastError);
    throw new Error(`Failed to register user ${username} at any Matrix server URL`);
  }

  /**
   * Ensure a user exists in Matrix and has a token
   * This function implements the auto-registration functionality:
   * 1. Checks if the user already has a Matrix token in the database
   * 2. If not, checks if the user exists in Matrix
   * 3. If the user doesn't exist in Matrix, creates them
   * 4. If the user exists in Matrix but doesn't have a token in the database, resets their password and gets a new token
   * @param {string} userId - The NxtAcre user ID
   * @returns {Promise<string>} The Matrix access token for the user
   */
  async ensureMatrixUser(userId) {
    try {
      // Get the user from the database
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error(`User ${userId} not found in database`);
      }

      // If the user already has a Matrix token, return it
      if (user.matrix_token) {
        return user.matrix_token;
      }

      // Get the user's Matrix ID
      const matrixUserId = getNxtAcreUserIdToMatrixUserId(userId);

      // Check if the user exists in Matrix
      let matrixUser = null;
      let userExists = false;
      let lastError = null;

      // Try the primary URL first, then fallback URLs if needed
      const urlsToTry = [this.matrixServerUrl, ...this.fallbackUrls];

      for (const url of urlsToTry) {
        try {
          console.log(`Checking if user ${matrixUserId} exists at: ${url}`);
          const response = await axios.get(`${url}/_synapse/admin/v2/users/${encodeURIComponent(matrixUserId)}`, {
            headers: {
              Authorization: `Bearer ${await this.getAdminToken()}`
            }
          });

          if (response.data) {
            console.log(`User ${matrixUserId} exists at: ${url}`);
            matrixUser = response.data;
            userExists = true;

            // Update the primary URL if we're using a fallback
            if (url !== this.matrixServerUrl) {
              console.log(`Updating primary Matrix server URL from ${this.matrixServerUrl} to ${url}`);
              this.matrixServerUrl = url;
            }

            break;
          }
        } catch (error) {
          // If the error is a 404, the user doesn't exist
          if (error.response && error.response.status === 404) {
            console.log(`User ${matrixUserId} doesn't exist at: ${url}`);
            continue; // Try the next URL
          }

          console.error(`Error checking if user ${matrixUserId} exists at ${url}:`, error.message);
          lastError = error;
          continue; // Try the next URL
        }
      }

      // If we tried all URLs and got errors other than 404, throw the last error
      if (!userExists && lastError && !(lastError.response && lastError.response.status === 404)) {
        throw lastError;
      }

      // If the user doesn't exist in Matrix, create them
      if (!matrixUser) {
        console.log(`User ${userId} doesn't exist in Matrix, creating...`);

        // Create a username from email (remove @ and domain, replace dots with underscores)
        const email = user.email.toLowerCase();
        const username = email.split('@')[0].replace(/\./g, '_');

        // Generate a random password
        const password = Math.random().toString(36).substring(2, 15);

        // Create display name from first and last name
        const displayname = `${user.first_name} ${user.last_name}`;

        // Register the user in Matrix
        const registerResponse = await this.registerMatrixUser(username, password);

        // Store the access token in the user record
        await user.update({ matrix_token: registerResponse.access_token });

        console.log(`Created Matrix user ${username} with display name ${displayname}`);

        return registerResponse.access_token;
      }

      // If the user exists in Matrix but doesn't have a token in our database,
      // reset their password and get a new token
      console.log(`User exists in Matrix but has no token in database, resetting password...`);

      // Generate a random password
      const password = Math.random().toString(36).substring(2, 15);

      // Reset the user's password
      lastError = null;
      // Try the primary URL first, then fallback URLs if needed
      const resetUrlsToTry = [this.matrixServerUrl, ...this.fallbackUrls];
      let success = false;

      for (const url of resetUrlsToTry) {
        try {
          console.log(`Attempting to reset password for ${matrixUserId} at: ${url}`);
          await axios.post(`${url}/_synapse/admin/v1/reset_password/${encodeURIComponent(matrixUserId)}`, {
            new_password: password,
            logout_devices: false
          }, {
            headers: {
              Authorization: `Bearer ${await this.getAdminToken()}`
            }
          });

          console.log(`Successfully reset password for ${matrixUserId} at: ${url}`);

          // Update the primary URL if we're using a fallback
          if (url !== this.matrixServerUrl) {
            console.log(`Updating primary Matrix server URL from ${this.matrixServerUrl} to ${url}`);
            this.matrixServerUrl = url;
          }

          success = true;
          break;
        } catch (error) {
          console.error(`Error resetting password for ${matrixUserId} at ${url}:`, error.message);
          lastError = error;
          continue; // Try the next URL
        }
      }

      if (!success) {
        console.error(`Failed to reset password for ${matrixUserId} at all Matrix server URLs. Last error:`, lastError);
        throw new Error(`Failed to reset password for ${matrixUserId} at any Matrix server URL`);
      }

      // Log in to get a new access token
      lastError = null;
      success = false;
      let accessToken = null;

      // Define login URLs to try (avoid redeclaring urlsToTry)
      const loginUrlsToTry = [this.matrixServerUrl, ...this.fallbackUrls];

      for (const url of loginUrlsToTry) {
        try {
          console.log(`Attempting to login for ${matrixUserId} at: ${url}`);
          const loginResponse = await axios.post(`${url}/_matrix/client/v3/login`, {
            type: 'm.login.password',
            identifier: {
              type: 'm.id.user',
              user: matrixUserId.split(':')[0].substring(1) // Remove @ and domain
            },
            password: password
          });

          // Store the access token in the user record
          if (loginResponse.data && loginResponse.data.access_token) {
            accessToken = loginResponse.data.access_token;
            await user.update({ matrix_token: accessToken });
            console.log(`Updated matrix_token for user ${userId}`);

            // Update the primary URL if we're using a fallback
            if (url !== this.matrixServerUrl) {
              console.log(`Updating primary Matrix server URL from ${this.matrixServerUrl} to ${url}`);
              this.matrixServerUrl = url;
            }

            success = true;
            break;
          } else {
            console.error(`No access token in login response for ${matrixUserId} at ${url}`);
            continue; // Try the next URL
          }
        } catch (error) {
          console.error(`Error logging in for ${matrixUserId} at ${url}:`, error.message);
          lastError = error;
          continue; // Try the next URL
        }
      }

      if (!success) {
        console.error(`Failed to login for ${matrixUserId} at all Matrix server URLs. Last error:`, lastError);
        throw new Error(`Failed to login for ${matrixUserId} at any Matrix server URL`);
      }

      return accessToken;
    } catch (error) {
      console.error(`Error ensuring Matrix user for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get a client for a specific user
   * @param {string} userId - The NxtAcre user ID
   * @param {string} accessToken - The user's access token (optional, will use auto-registration if not provided)
   * @returns {Promise<Object>} The Matrix client for the user
   */
  async getUserClient(userId, accessToken = null) {
    // Check if we already have a client for this user
    if (this.userClients.has(userId)) {
      return this.userClients.get(userId);
    }

    // Get the user's Matrix ID
    const matrixUserId = getNxtAcreUserIdToMatrixUserId(userId);

    // Generate a device ID for this user
    const deviceId = `DEVICE_${userId}_${Date.now()}`;

    // If no access token is provided, ensure the user exists in Matrix and get their token
    if (!accessToken) {
      try {
        // Ensure the user exists in Matrix and get their token
        accessToken = await this.ensureMatrixUser(userId);
      } catch (error) {
        console.error(`Error ensuring Matrix user for ${userId}, falling back to admin token:`, error);
        // If there's an error, fall back to using the admin token
        accessToken = await this.getAdminToken();
      }
    }

    let lastError = null;
    // Try the primary URL first, then fallback URLs if needed
    const serverUrlsToTry = [this.matrixServerUrl, ...this.fallbackUrls];

    for (const url of serverUrlsToTry) {
      try {
        console.log(`Attempting to connect to Matrix server at: ${url} for user ${userId}`);

        // Create a client with the access token and encryption enabled
        const client = createClient({
          baseUrl: url,
          accessToken,
          userId: matrixUserId,
          cryptoStore: {
            indexedDBName: 'matrix-js-sdk',
            storePrefix: `user-${userId}-crypto-store`,
          },
          deviceId,
          // Add WebSocket URL for real-time communication with multiple format options
          websocketUrl: this.successfulWebSocketUrl || (url.replace(/^https?:\/\//, 'wss://') + '/_matrix/client/ws'),
          // Enable detailed logging
          timelineSupport: true,
          useAuthorizationHeader: false,
          syncParams: {
            timeout: 30000,
          },
        });

        // Log the WebSocket URL being used
        console.log(`Matrix user client created for user ${userId} with WebSocket URL: ${this.successfulWebSocketUrl || (url.replace(/^https?:\/\//, 'wss://') + '/_matrix/client/ws')}`);

        // Add event listeners for WebSocket events
        client.on('WebSocket.open', () => {
          console.log(`User client (${userId}): WebSocket connection opened successfully`);
        });

        client.on('WebSocket.error', (error) => {
          console.error(`User client (${userId}): WebSocket connection error:`, error);
        });

        client.on('WebSocket.close', (code, reason) => {
          console.warn(`User client (${userId}): WebSocket connection closed: Code ${code}, Reason: ${reason}`);
        });

        // Initialize crypto
        await client.initCrypto();

        // Test the connection by making a simple API call
        try {
          await client.getVersions();
          console.log(`Successfully connected to Matrix server at: ${url} for user ${userId}`);

          // If we get here, the connection was successful
          // Cache the client
          this.userClients.set(userId, client);

          // Update the primary URL if we're using a fallback
          if (url !== this.matrixServerUrl) {
            console.log(`Updating primary Matrix server URL from ${this.matrixServerUrl} to ${url}`);
            this.matrixServerUrl = url;
          }

          return client;
        } catch (testError) {
          console.error(`Failed to connect to Matrix server at ${url} for user ${userId}:`, testError);
          lastError = testError;
          continue; // Try the next URL
        }
      } catch (error) {
        console.error(`Error creating client for user ${userId} with URL ${url}:`, error);
        lastError = error;
        continue; // Try the next URL
      }
    }

    // If we get here, all URLs failed
    console.error(`All Matrix server URLs failed for user ${userId}. Last error:`, lastError);
    throw new Error(`Failed to connect to any Matrix server URL for user ${userId}`);
  }

  /**
   * Create a new room (conversation)
   * @param {string} name - The name of the room
   * @param {string} topic - The topic of the room (optional)
   * @param {boolean} isPublic - Whether the room is public (default: false)
   * @param {string} creatorUserId - The NxtAcre user ID of the room creator
   * @param {string} farmId - The farm ID (optional)
   * @returns {Promise<Object>} The created room
   */
  async createRoom(name, topic = '', isPublic = false, creatorUserId, farmId = null) {
    try {
      const client = await this.getAdminClient();

      // Create the room with encryption enabled
      const response = await client.createRoom({
        name,
        topic,
        visibility: isPublic ? 'public' : 'private',
        preset: isPublic ? 'public_chat' : 'private_chat',
        creation_content: {
          'm.federate': false
        },
        initial_state: [
          {
            type: 'm.room.history_visibility',
            state_key: '',
            content: {
              history_visibility: 'shared'
            }
          },
          // Enable encryption for the room
          {
            type: 'm.room.encryption',
            state_key: '',
            content: {
              algorithm: 'm.megolm.v1.aes-sha2',
              rotation_period_ms: 604800000, // 1 week
              rotation_period_msgs: 100
            }
          },
          // Add farm_id as a custom state event if provided
          ...(farmId ? [{
            type: 'nxtacre.farm_id',
            state_key: '',
            content: {
              farm_id: farmId
            }
          }] : [])
        ]
      });

      // Get the room ID
      const roomId = response.room_id;

      // Invite the creator if they're not the admin
      if (creatorUserId) {
        const matrixUserId = getNxtAcreUserIdToMatrixUserId(creatorUserId);
        await client.invite(roomId, matrixUserId);

        // Make the creator an admin of the room
        await client.setPowerLevel(roomId, matrixUserId, 100);
      }

      return response;
    } catch (error) {
      console.error('Error creating room:', error);
      throw error;
    }
  }

  /**
   * Invite a user to a room
   * @param {string} roomId - The Matrix room ID
   * @param {string} userId - The NxtAcre user ID to invite
   * @returns {Promise<Object>} The response from the Matrix server
   */
  async inviteToRoom(roomId, userId) {
    try {
      const client = await this.getAdminClient();
      const matrixUserId = getNxtAcreUserIdToMatrixUserId(userId);

      const response = await client.invite(roomId, matrixUserId);
      return response;
    } catch (error) {
      console.error(`Error inviting user ${userId} to room ${roomId}:`, error);
      throw error;
    }
  }

  /**
   * Send a message to a room
   * @param {string} roomId - The Matrix room ID
   * @param {string} content - The message content
   * @param {string} messageType - The message type (default: 'm.room.message')
   * @param {string} userId - The NxtAcre user ID sending the message
   * @returns {Promise<Object>} The response from the Matrix server
   */
  async sendMessage(roomId, content, messageType = 'm.room.message', userId) {
    try {
      // Get the client for the user
      const client = userId 
        ? await this.getUserClient(userId) 
        : await this.getAdminClient();

      // Create the message content
      const messageContent = {
        msgtype: 'm.text',
        body: content
      };

      // Send the message
      const response = await client.sendEvent(roomId, messageType, messageContent);
      return response;
    } catch (error) {
      console.error(`Error sending message to room ${roomId}:`, error);
      throw error;
    }
  }

  /**
   * Get messages from a room
   * @param {string} roomId - The Matrix room ID
   * @param {string} userId - The NxtAcre user ID requesting the messages
   * @param {string} from - The pagination token to start from (optional)
   * @param {number} limit - The maximum number of messages to return (default: 50)
   * @returns {Promise<Object>} The messages from the room
   */
  async getMessages(roomId, userId, from = null, limit = 50) {
    try {
      // Get the client for the user
      const client = userId 
        ? await this.getUserClient(userId) 
        : await this.getAdminClient();

      // Get the messages
      const response = await client.scrollback(roomId, from, limit);
      return response;
    } catch (error) {
      console.error(`Error getting messages from room ${roomId}:`, error);
      throw error;
    }
  }

  /**
   * Get all rooms for a user
   * @param {string} userId - The NxtAcre user ID
   * @returns {Promise<Array>} The rooms the user is in
   */
  async getRooms(userId) {
    try {
      // Get the client for the user
      const client = userId 
        ? await this.getUserClient(userId) 
        : await this.getAdminClient();

      // Get the rooms
      const rooms = client.getRooms();
      return rooms;
    } catch (error) {
      console.error(`Error getting rooms for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get a room by ID
   * @param {string} roomId - The Matrix room ID
   * @param {string} userId - The NxtAcre user ID requesting the room
   * @returns {Promise<Object>} The room
   */
  async getRoom(roomId, userId) {
    try {
      // Get the client for the user
      const client = userId 
        ? await this.getUserClient(userId) 
        : await this.getAdminClient();

      // Get the room
      const room = client.getRoom(roomId);
      return room;
    } catch (error) {
      console.error(`Error getting room ${roomId}:`, error);
      throw error;
    }
  }

  /**
   * Leave a room
   * @param {string} roomId - The Matrix room ID
   * @param {string} userId - The NxtAcre user ID leaving the room
   * @returns {Promise<Object>} The response from the Matrix server
   */
  async leaveRoom(roomId, userId) {
    try {
      // Get the client for the user
      const client = userId 
        ? await this.getUserClient(userId) 
        : await this.getAdminClient();

      // Leave the room
      const response = await client.leave(roomId);
      return response;
    } catch (error) {
      console.error(`Error leaving room ${roomId}:`, error);
      throw error;
    }
  }

  /**
   * Set the typing status for a user in a room
   * @param {string} roomId - The Matrix room ID
   * @param {string} userId - The NxtAcre user ID
   * @param {boolean} isTyping - Whether the user is typing
   * @returns {Promise<Object>} The response from the Matrix server
   */
  async setTyping(roomId, userId, isTyping) {
    try {
      // Get the client for the user
      const client = await this.getUserClient(userId);

      // Set typing status
      const response = await client.sendTyping(roomId, isTyping);
      return response;
    } catch (error) {
      console.error(`Error setting typing status for user ${userId} in room ${roomId}:`, error);
      throw error;
    }
  }

  /**
   * Add a reaction to a message
   * @param {string} roomId - The Matrix room ID
   * @param {string} eventId - The event ID to react to
   * @param {string} reaction - The reaction to add
   * @param {string} userId - The NxtAcre user ID adding the reaction
   * @returns {Promise<Object>} The response from the Matrix server
   */
  async addReaction(roomId, eventId, reaction, userId) {
    try {
      // Get the client for the user
      const client = userId 
        ? await this.getUserClient(userId) 
        : await this.getAdminClient();

      // Add the reaction
      const response = await client.sendEvent(roomId, 'm.reaction', {
        'm.relates_to': {
          rel_type: 'm.annotation',
          event_id: eventId,
          key: reaction
        }
      });
      return response;
    } catch (error) {
      console.error(`Error adding reaction to event ${eventId} in room ${roomId}:`, error);
      throw error;
    }
  }

  /**
   * Get Matrix users for a specific farm
   * @param {string} farmId - The farm ID
   * @returns {Promise<Array>} Array of Matrix users associated with the farm
   */
  async getMatrixUsersForFarm(farmId) {
    try {
      // Get the admin client
      const client = await this.getAdminClient();

      // Get all Matrix users
      const adminToken = await this.getAdminToken();
      const response = await axios.get(`${this.matrixServerUrl}/_synapse/admin/v2/users?limit=1000`, {
        headers: {
          Authorization: `Bearer ${adminToken}`
        }
      });

      const matrixUsers = response.data.users || [];

      // Get all approved users for this farm
      const farmUsers = await UserFarm.findAll({
        where: {
          farm_id: farmId,
          is_approved: true
        },
        attributes: ['user_id']
      });

      const farmUserIds = farmUsers.map(user => user.user_id);

      // Filter Matrix users to only include those associated with the farm
      const farmMatrixUsers = matrixUsers.filter(matrixUser => {
        // Extract NxtAcre user ID from Matrix user ID
        const nxtAcreUserId = getMatrixUserIdToNxtAcreUserId(matrixUser.name);
        return farmUserIds.includes(nxtAcreUserId);
      });

      // Add farm-specific information to each user
      const enhancedUsers = await Promise.all(farmMatrixUsers.map(async (user) => {
        const nxtAcreUserId = getMatrixUserIdToNxtAcreUserId(user.name);

        // Get the user's role in this farm
        const userFarm = await UserFarm.findOne({
          where: {
            user_id: nxtAcreUserId,
            farm_id: farmId
          },
          attributes: ['role', 'role_id']
        });

        return {
          ...user,
          nxtacre_user_id: nxtAcreUserId,
          farm_id: farmId,
          farm_role: userFarm ? userFarm.role : null,
          farm_role_id: userFarm ? userFarm.role_id : null
        };
      }));

      return enhancedUsers;
    } catch (error) {
      console.error(`Error getting Matrix users for farm ${farmId}:`, error);
      throw error;
    }
  }

  /**
   * Get Matrix users for associated farms
   * @param {string} farmId - The farm ID
   * @returns {Promise<Array>} Array of Matrix users from associated farms
   */
  async getMatrixUsersForAssociatedFarms(farmId) {
    try {
      // Get the admin client
      const client = await this.getAdminClient();

      // Get all Matrix users
      const adminToken = await this.getAdminToken();
      const response = await axios.get(`${this.matrixServerUrl}/_synapse/admin/v2/users?limit=1000`, {
        headers: {
          Authorization: `Bearer ${adminToken}`
        }
      });

      const matrixUsers = response.data.users || [];

      // Get all associated farms
      const query = `
        SELECT 
          associated_farm_id as farm_id
        FROM farm_associations
        WHERE initiator_farm_id = $1 AND status = 'active'
        UNION
        SELECT 
          initiator_farm_id as farm_id
        FROM farm_associations
        WHERE associated_farm_id = $1 AND status = 'active'
      `;

      const { rows: associatedFarms } = await (await import('../db.js')).default.query(query, [farmId]);
      const associatedFarmIds = associatedFarms.map(farm => farm.farm_id);

      // Get all approved users for associated farms
      const associatedFarmUsers = [];
      for (const associatedFarmId of associatedFarmIds) {
        const farmUsers = await UserFarm.findAll({
          where: {
            farm_id: associatedFarmId,
            is_approved: true
          },
          attributes: ['user_id']
        });

        associatedFarmUsers.push(...farmUsers.map(user => ({
          user_id: user.user_id,
          farm_id: associatedFarmId
        })));
      }

      // Filter Matrix users to only include those associated with the associated farms
      const associatedFarmMatrixUsers = [];
      for (const matrixUser of matrixUsers) {
        // Extract NxtAcre user ID from Matrix user ID
        const nxtAcreUserId = getMatrixUserIdToNxtAcreUserId(matrixUser.name);

        // Check if this user is in any of the associated farms
        const associatedFarmUser = associatedFarmUsers.find(user => user.user_id === nxtAcreUserId);
        if (associatedFarmUser) {
          // Get the user's role in this farm
          const userFarm = await UserFarm.findOne({
            where: {
              user_id: nxtAcreUserId,
              farm_id: associatedFarmUser.farm_id
            },
            attributes: ['role', 'role_id']
          });

          associatedFarmMatrixUsers.push({
            ...matrixUser,
            nxtacre_user_id: nxtAcreUserId,
            farm_id: associatedFarmUser.farm_id,
            farm_role: userFarm ? userFarm.role : null,
            farm_role_id: userFarm ? userFarm.role_id : null
          });
        }
      }

      return associatedFarmMatrixUsers;
    } catch (error) {
      console.error(`Error getting Matrix users for associated farms of ${farmId}:`, error);
      throw error;
    }
  }
}

// Create a singleton instance
const matrixClientService = new MatrixClientService();

export default matrixClientService;
