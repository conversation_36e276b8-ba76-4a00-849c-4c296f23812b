import CustomerNotification from '../models/CustomerNotification.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import { Op } from 'sequelize';

/**
 * Get notifications for a customer
 */
export const getCustomerNotifications = async (req, res) => {
  try {
    // The customer is attached to the request by the authenticateCustomer middleware
    const customer = req.customer;
    const farmId = req.farmId;
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;
    const read = req.query.read !== undefined ? req.query.read === 'true' : undefined;

    // Build query conditions
    const where = { 
      customer_id: customer.id,
      farm_id: farmId
    };
    if (read !== undefined) where.read = read;

    // Get notifications with pagination
    const notifications = await CustomerNotification.findAndCountAll({
      where,
      limit,
      offset,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        },
        {
          model: Customer,
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    return res.status(200).json({
      notifications: notifications.rows,
      total: notifications.count,
      limit,
      offset,
      unreadCount: await CustomerNotification.count({
        where: {
          customer_id: customer.id,
          farm_id: farmId,
          read: false
        }
      })
    });
  } catch (error) {
    console.error('Error getting customer notifications:', error);
    return res.status(500).json({ error: 'Failed to fetch notifications' });
  }
};

/**
 * Create a new notification for a customer
 */
export const createCustomerNotification = async (req, res) => {
  try {
    const {
      title,
      message,
      type,
      customerId,
      farmId,
      relatedEntityType,
      relatedEntityId,
      actionUrl
    } = req.body;

    // Validate required fields
    if (!title || !message || !customerId || !farmId) {
      return res.status(400).json({ error: 'Title, message, customerId, and farmId are required' });
    }

    // Create the notification
    const notification = await CustomerNotification.create({
      title,
      message,
      type: type || 'info',
      customer_id: customerId,
      farm_id: farmId,
      related_entity_type: relatedEntityType,
      related_entity_id: relatedEntityId,
      action_url: actionUrl,
      read: false
    });

    return res.status(201).json({ notification });
  } catch (error) {
    console.error('Error creating customer notification:', error);
    return res.status(500).json({ error: 'Failed to create notification' });
  }
};

/**
 * Mark a notification as read
 */
export const markNotificationAsRead = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const customer = req.customer;

    if (!notificationId) {
      return res.status(400).json({ error: 'Notification ID is required' });
    }

    // Find the notification, ensuring it belongs to this customer
    const notification = await CustomerNotification.findOne({
      where: {
        id: notificationId,
        customer_id: customer.id
      }
    });

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    // Update the notification
    notification.read = true;
    await notification.save();

    return res.status(200).json({ message: 'Notification marked as read' });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    return res.status(500).json({ error: 'Failed to mark notification as read' });
  }
};

/**
 * Mark all notifications as read for a customer
 */
export const markAllNotificationsAsRead = async (req, res) => {
  try {
    const customer = req.customer;
    const farmId = req.farmId;

    // Update all unread notifications for the customer
    await CustomerNotification.update(
      { read: true },
      { 
        where: { 
          customer_id: customer.id,
          farm_id: farmId,
          read: false 
        } 
      }
    );

    return res.status(200).json({ message: 'All notifications marked as read' });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    return res.status(500).json({ error: 'Failed to mark all notifications as read' });
  }
};

/**
 * Delete a notification
 */
export const deleteNotification = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const customer = req.customer;

    if (!notificationId) {
      return res.status(400).json({ error: 'Notification ID is required' });
    }

    // Find the notification, ensuring it belongs to this customer
    const notification = await CustomerNotification.findOne({
      where: {
        id: notificationId,
        customer_id: customer.id
      }
    });

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    // Delete the notification
    await notification.destroy();

    return res.status(200).json({ message: 'Notification deleted successfully' });
  } catch (error) {
    console.error('Error deleting notification:', error);
    return res.status(500).json({ error: 'Failed to delete notification' });
  }
};