import { stripe, STRIPE_PUBLISHABLE_KEY } from '../config/stripe.js';
import { sequelize } from '../config/database.js';
import StripeFinancialConnection from '../models/StripeFinancialConnection.js';
import Transaction from '../models/Transaction.js';
import dotenv from 'dotenv';
import { validate as uuidValidate } from 'uuid';

dotenv.config();

// Create a Financial Connections Session
export const createFinancialConnectionsSession = async (req, res) => {
  try {
    const { farmId, userId, returnUrl } = req.body;

    if (!farmId || !userId) {
      return res.status(400).json({ error: 'Farm ID and User ID are required' });
    }

    // Get the farm to retrieve its Stripe customer ID
    const Farm = (await import('../models/Farm.js')).default;
    const farm = await Farm.findByPk(farmId);

    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    if (!farm.stripe_customer_id) {
      return res.status(400).json({ error: 'Farm does not have a Stripe customer ID' });
    }

    // Create a Financial Connections Session
    const session = await stripe.financialConnections.sessions.create({
      account_holder: {
        type: 'customer',
        customer: farm.stripe_customer_id, // Using the farm's Stripe customer ID
      },
      permissions: ['payment_method', 'balances', 'transactions'],
      filters: { countries: ['US'] },
      return_url: returnUrl || `${process.env.FRONTEND_URL}/financial-connections/callback`,
    });

    return res.json({
      success: true,
      client_secret: session.client_secret,
      session_id: session.id,
      publishable_key: STRIPE_PUBLISHABLE_KEY,
    });
  } catch (error) {
    console.error('Error creating Financial Connections Session:', error);
    return res.status(500).json({ error: error.message });
  }
};

  // Handle Financial Connections Session completion
export const handleFinancialConnectionsCallback = async (req, res) => {
  try {
    const { sessionId, farmId } = req.body;

    if (!sessionId || !farmId) {
      return res.status(400).json({ error: 'Session ID and Farm ID are required' });
    }

    // Retrieve the session to get the linked account
    const session = await stripe.financialConnections.sessions.retrieve(sessionId);

    if (!session.accounts || session.accounts.data.length === 0) {
      return res.status(400).json({ error: 'No accounts were linked in this session' });
    }

    // Get the first account from the session
    const account = session.accounts.data[0];

    // Get the Farm model to update the farm with the financial connection
    const Farm = (await import('../models/Farm.js')).default;
    const farm = await Farm.findByPk(farmId);

    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Store the account information in the database
    const transaction = await sequelize.transaction();

    try {
      // Retrieve detailed account information before creating the record
      let accountDetails;
      try {
        accountDetails = await stripe.financialConnections.accounts.retrieve(
          account.id
        );
        console.log('Account details retrieved:', accountDetails);
      } catch (stripeError) {
        console.error('Error retrieving account details from Stripe:', stripeError);
        // Don't throw here, we'll still create the connection with the information we have
      }

      // Create a new StripeFinancialConnection record
      const financialConnection = await StripeFinancialConnection.create({
        farm_id: farmId,
        stripe_session_id: sessionId,
        stripe_account_id: account.id,
        institution_name: account.institution_name || (accountDetails?.institution_name) || 'Unknown Institution',
        status: 'active',
        last_successful_update: new Date(),
      }, { transaction });

      // Verify that the financial connection was created successfully
      if (!financialConnection || !financialConnection.id) {
        throw new Error('Failed to create financial connection record');
      }

      console.log('Financial connection created successfully:', financialConnection.id);

      // Update the farm with the financial connection ID
      await farm.update({
        financial_connection_id: financialConnection.id
      }, { transaction });

      // Verify that the farm was updated successfully
      const updatedFarm = await Farm.findByPk(farmId, { transaction });
      if (!updatedFarm || updatedFarm.financial_connection_id !== financialConnection.id) {
        throw new Error('Failed to update farm with financial connection ID');
      }

      console.log('Farm updated with financial connection ID:', financialConnection.id);

      // Commit the transaction to ensure database changes are saved
      await transaction.commit();

      return res.json({
        success: true,
        message: 'Bank account successfully linked',
        connectionId: financialConnection.id,
      });
    } catch (dbError) {
      await transaction.rollback();
      console.error('Database error:', dbError);
      return res.status(500).json({ error: 'Failed to store account information: ' + dbError.message });
    }
  } catch (error) {
    console.error('Error handling Financial Connections callback:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get transactions for a specific account
export const getTransactions = async (req, res) => {
  try {
    const { connectionId, startDate, endDate } = req.query;

    if (!connectionId) {
      return res.status(400).json({ error: 'Connection ID is required' });
    }

    // Check if connectionId is a valid UUID
    if (!uuidValidate(connectionId)) {
      console.warn(`Invalid UUID format for connectionId: ${connectionId}`);
      return res.json({
        success: true,
        transactions: [],
      });
    }

    // Get the financial connection from the database
    const financialConnection = await StripeFinancialConnection.findByPk(connectionId);

    if (!financialConnection) {
      return res.status(404).json({ error: 'Financial connection not found' });
    }

    // Set default date range if not provided
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(end);
    start.setDate(start.getDate() - 30); // Default to 30 days if no start date

    // Format dates for Stripe API
    const formattedStartDate = Math.floor(start.getTime() / 1000); // Convert to Unix timestamp
    const formattedEndDate = Math.floor(end.getTime() / 1000); // Convert to Unix timestamp

    // Get transactions from Stripe
    const stripeTransactions = await stripe.financialConnections.transactions.list({
      account: financialConnection.stripe_account_id,
      limit: 100,
      start_date: formattedStartDate,
      end_date: formattedEndDate,
    });

    // Process and store transactions in the database
    const processedTransactions = await processAndStoreTransactions(
      stripeTransactions.data,
      financialConnection.farm_id,
      financialConnection.id
    );

    return res.json({
      success: true,
      transactions: processedTransactions,
    });
  } catch (error) {
    console.error('Error getting transactions:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Helper function to process and store transactions
const processAndStoreTransactions = async (transactions, farmId, connectionId) => {
  const transaction = await sequelize.transaction();

  try {
    const processedTransactions = [];

    for (const stripeTransaction of transactions) {
      // Check if transaction already exists
      let existingTransaction = await Transaction.findOne({
        where: {
          stripe_transaction_id: stripeTransaction.id,
        },
        transaction,
      });

      if (!existingTransaction) {
        // Create new transaction
        existingTransaction = await Transaction.create({
          farm_id: farmId,
          financial_account_id: null, // Will be updated when financial accounts are implemented
          transaction_date: new Date(stripeTransaction.transaction_date * 1000).toISOString().split('T')[0],
          post_date: stripeTransaction.posted_date ? new Date(stripeTransaction.posted_date * 1000).toISOString().split('T')[0] : null,
          description: stripeTransaction.description,
          amount: stripeTransaction.amount / 100, // Convert from cents to dollars
          transaction_type: stripeTransaction.amount < 0 ? 'withdrawal' : 'deposit',
          category: stripeTransaction.category || null,
          is_reconciled: false,
          stripe_transaction_id: stripeTransaction.id,
        }, { transaction });
      }

      processedTransactions.push(existingTransaction);
    }

    await transaction.commit();
    return processedTransactions;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

// Get account balances
export const getAccountBalances = async (req, res) => {
  try {
    const { connectionId } = req.params;

    if (!connectionId) {
      return res.status(400).json({ error: 'Connection ID is required' });
    }

    // Check if connectionId is a valid UUID
    if (!uuidValidate(connectionId)) {
      console.warn(`Invalid UUID format for connectionId: ${connectionId}`);
      return res.status(400).json({ error: 'Invalid Connection ID format' });
    }

    // Get the financial connection from the database
    const financialConnection = await StripeFinancialConnection.findByPk(connectionId);

    if (!financialConnection) {
      return res.status(404).json({ error: 'Financial connection not found' });
    }

    // Get account balances from Stripe
    const account = await stripe.financialConnections.accounts.retrieve(
      financialConnection.stripe_account_id
    );

    // Get balance data
    const balanceData = await stripe.financialConnections.accounts.retrieveBalance(
      financialConnection.stripe_account_id
    );

    // Format the account data to match the expected structure
    const formattedAccount = {
      account_id: account.id,
      name: account.display_name || 'Account',
      type: account.category,
      subtype: account.subcategory,
      balances: {
        available: balanceData.available ? balanceData.available / 100 : null,
        current: balanceData.cash ? balanceData.cash.amount / 100 : null,
        limit: null,
        iso_currency_code: balanceData.cash ? balanceData.cash.currency : 'USD',
      }
    };

    return res.json({
      success: true,
      accounts: [formattedAccount],
    });
  } catch (error) {
    console.error('Error getting account balances:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Sync transactions for all financial connections
export const syncAllTransactions = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // Get all financial connections for the farm
    const financialConnections = await StripeFinancialConnection.findAll({
      where: {
        farm_id: farmId,
        status: 'active',
      },
    });

    if (financialConnections.length === 0) {
      return res.status(404).json({ error: 'No active financial connections found for this farm' });
    }

    const results = [];

    // Set date range for transaction sync (last 30 days)
    const end = new Date();
    const start = new Date(end);
    start.setDate(start.getDate() - 30);

    // Format dates for Stripe API
    const formattedStartDate = Math.floor(start.getTime() / 1000); // Convert to Unix timestamp
    const formattedEndDate = Math.floor(end.getTime() / 1000); // Convert to Unix timestamp

    // Sync transactions for each financial connection
    for (const connection of financialConnections) {
      try {
        const stripeTransactions = await stripe.financialConnections.transactions.list({
          account: connection.stripe_account_id,
          limit: 100,
          start_date: formattedStartDate,
          end_date: formattedEndDate,
        });

        // Process and store transactions
        const processedTransactions = await processAndStoreTransactions(
          stripeTransactions.data,
          farmId,
          connection.id
        );

        // Update last successful update timestamp
        await connection.update({
          last_successful_update: new Date(),
        });

        results.push({
          connectionId: connection.id,
          institutionName: connection.institution_name,
          transactionCount: processedTransactions.length,
          success: true,
        });
      } catch (error) {
        console.error(`Error syncing transactions for connection ${connection.id}:`, error);
        results.push({
          connectionId: connection.id,
          institutionName: connection.institution_name,
          success: false,
          error: error.message,
        });
      }
    }

    return res.json({
      success: true,
      results,
    });
  } catch (error) {
    console.error('Error syncing all transactions:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all financial connections for a farm
export const getFinancialConnections = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!farmId) {
      return res.status(400).json({ error: 'Farm ID is required' });
    }

    // First, check if the farm has a financial_connection_id
    const Farm = (await import('../models/Farm.js')).default;
    const farm = await Farm.findByPk(farmId);

    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    let financialConnections = [];

    // If the farm has a financial_connection_id, get that specific connection
    if (farm.financial_connection_id) {
      const connection = await StripeFinancialConnection.findOne({
        where: {
          id: farm.financial_connection_id,
          status: 'active',
        },
      });

      if (connection) {
        financialConnections = [connection];
      }
    }

    // If no connection was found through financial_connection_id, get all connections for the farm
    if (financialConnections.length === 0) {
      financialConnections = await StripeFinancialConnection.findAll({
        where: {
          farm_id: farmId,
          status: 'active',
        },
      });

      // If we found connections but the farm doesn't have a financial_connection_id set,
      // update the farm with the first connection's ID
      if (financialConnections.length > 0 && !farm.financial_connection_id) {
        await farm.update({
          financial_connection_id: financialConnections[0].id
        });
      }
    }

    return res.json({
      success: true,
      connections: financialConnections.map(conn => ({
        id: conn.id,
        institutionName: conn.institution_name,
        lastUpdate: conn.last_successful_update,
        status: conn.status,
      })),
    });
  } catch (error) {
    console.error('Error getting financial connections:', error);
    return res.status(500).json({ error: error.message });
  }
};
