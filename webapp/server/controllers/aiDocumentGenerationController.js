import { validationResult } from 'express-validator';
import { sequelize } from '../config/database.js';
import { handleServerError } from '../utils/errorHandlers.js';
import { withDatabaseRetry } from '../utils/retryUtils.js';
import { getSchema } from '../utils/schemaUtils.js';
import aiService from '../services/aiService.js';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Generate a document using AI based on user prompt
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const generateDocument = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { prompt, documentType, farmId } = req.body;
    const userId = req.user.id;

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      console.error('OpenAI API key is not configured');
      return res.status(500).json({ message: 'AI service is not properly configured' });
    }

    try {
      // Get farm information to provide context to the AI
      const schema = getSchema();
      const farmResult = await withDatabaseRetry(async () => {
        // Set search_path to use the specified schema
        await sequelize.query(`SET search_path TO ${schema};`);

        return sequelize.query(
          'SELECT name, farm_type, location FROM farms WHERE id = $1',
          { 
            replacements: [farmId],
            type: sequelize.QueryTypes.SELECT
          }
        );
      });

      const farmInfo = farmResult.length > 0 ? farmResult[0] : { name: 'Unknown', farm_type: 'Unknown', location: 'Unknown' };

      // Determine the task type based on the document type
      let taskType = 'document_generation';

      // Check if document is related to financial topics
      if (documentType && (
          documentType.toLowerCase().includes('financ') || 
          documentType.toLowerCase().includes('tax') || 
          documentType.toLowerCase().includes('budget') || 
          documentType.toLowerCase().includes('invoice') || 
          documentType.toLowerCase().includes('contract') || 
          documentType.toLowerCase().includes('agreement'))) {
        taskType = 'financial_document';
      }

      // Create context for the document generation
      const documentContext = `You are currently assisting a user who manages a farm named "${farmInfo.name}" of type "${farmInfo.farm_type}" 
located in "${farmInfo.location}". 

Your task is to generate a complete ${documentType || 'document'} based on the user's prompt.
The document should be well-structured, professionally written, and include all necessary sections.
Include appropriate legal language, terms, and conditions that would typically be found in this type of document.
Format the document with proper headings, sections, and spacing.
Include placeholders for signatures, dates, and other fields that would need to be filled in.

Generate the document content only, without any explanations or notes.`;

      // Generate document using the AI service
      const documentContent = await aiService.generateCompletion(taskType, documentContext + "\n\n" + prompt, {
        modelIdentifier: "gpt-4", // Use GPT-4 for document generation
        maxTokens: 4000,
        temperature: 0.7,
      });

      // Generate a title for the document if not provided
      let documentTitle = documentType ? `${documentType.charAt(0).toUpperCase() + documentType.slice(1)} Document` : 'Generated Document';

      try {
        // Generate title using the AI service
        const titlePrompt = "Generate a concise, professional title for this document. Respond with only the title, no additional text.";
        const generatedTitle = await aiService.generateCompletion('document_title', documentContent.substring(0, 1000), {
          modelIdentifier: "gpt-3.5-turbo", // Use GPT-3.5 for title generation (faster and cheaper)
          maxTokens: 50,
          temperature: 0.5,
        });

        documentTitle = generatedTitle.trim() || documentTitle;
      } catch (titleError) {
        console.error('Error generating document title:', titleError);
        // Continue with the default title if there's an error generating a title
      }

      // Create a temporary file with the generated content
      let documentId = uuidv4();
      let fileName = `${documentId}.txt`;
      let filePath = '';

      try {
        const tempDir = path.join(__dirname, '..', 'temp');

        // Ensure temp directory exists
        if (!fs.existsSync(tempDir)) {
          fs.mkdirSync(tempDir, { recursive: true });
        }

        filePath = path.join(tempDir, fileName);
        fs.writeFileSync(filePath, documentContent);
        console.log(`Document file created at: ${filePath}`);
      } catch (fileError) {
        console.error('Error creating document file:', fileError);
        // Continue without file creation, we'll just store the content in the database
        // This prevents the API from failing if there's an issue with file operations
      }

      // Save the document in the database
      let documentInsertResult = null;
      try {
        documentInsertResult = await withDatabaseRetry(async () => {
          // Set search_path to use the specified schema
          await sequelize.query(`SET search_path TO ${schema};`);

          // Insert into signable_documents table
          const [result] = await sequelize.query(
            `INSERT INTO signable_documents 
            (id, farm_id, created_by, title, description, document_type, status, file_path, file_type, mime_type, file_size, is_ai_generated, ai_prompt) 
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) 
            RETURNING id, title, document_type, status, file_path`,
            { 
              replacements: [
                documentId, 
                farmId, 
                userId, 
                documentTitle, 
                `AI-generated ${documentType || 'document'} based on user prompt`, 
                documentType || 'agreement', 
                'draft',
                fileName || '',
                'txt',
                'text/plain',
                Buffer.byteLength(documentContent, 'utf8'),
                true,
                prompt
              ],
              type: sequelize.QueryTypes.INSERT
            }
          );

          return result;
        });
        console.log('Document saved to database successfully:', documentId);
      } catch (dbError) {
        console.error('Error saving document to database:', dbError);
        throw new Error(`Failed to save document to database: ${dbError.message}`);
      }

      // Log the document generation for future reference
      try {
        await withDatabaseRetry(async () => {
          // Set search_path to use the specified schema
          await sequelize.query(`SET search_path TO ${schema};`);

          return sequelize.query(
            'INSERT INTO ai_document_generations (user_id, farm_id, document_id, prompt, document_type) VALUES ($1, $2, $3, $4, $5)',
            { replacements: [userId, farmId, documentId, prompt, documentType || 'agreement'] }
          );
        });
        console.log('Document generation logged successfully');
      } catch (logError) {
        // If logging fails, we still want to return the document to the user
        console.error('Error logging document generation:', logError);
        // Continue without logging
      }

      return res.status(200).json({ 
        success: true,
        document: {
          id: documentId,
          title: documentTitle,
          content: documentContent,
          documentType: documentType || 'agreement'
        }
      });
    } catch (aiError) {
      console.error('Error in AI document generation:', aiError);

      // Check if this is a database error
      if (aiError.name && aiError.name.includes('Sequelize')) {
        return handleServerError(res, aiError);
      }

      // Check for OpenAI quota exceeded error
      if (aiError.message && aiError.message.includes('quota')) {
        return res.status(503).json({
          success: false,
          message: 'AI document generation service quota has been exceeded. Please try again later or contact support.',
          error: 'AI service quota exceeded'
        });
      }

      // Check for OpenAI timeout or connection errors
      if (aiError.code === 'ETIMEDOUT' || aiError.message.includes('timeout')) {
        return res.status(503).json({
          success: false,
          message: 'AI document generation is experiencing connection issues. Please try again in a moment.',
          error: 'AI service timeout'
        });
      }

      // Check if this is an OpenAI API error
      if (aiError.name === 'APIError' || aiError.message.includes('OpenAI')) {
        return res.status(503).json({
          success: false,
          message: 'AI service is temporarily unavailable. Please try again later.',
          error: process.env.NODE_ENV === 'development' ? aiError.message : 'AI service error'
        });
      }

      // Check if this is a file system error
      if (aiError.code && ['ENOENT', 'EACCES', 'EPERM'].includes(aiError.code)) {
        return res.status(500).json({ 
          success: false,
          message: 'Failed to save document due to a file system error. Please try again later.',
          error: process.env.NODE_ENV === 'development' ? aiError.message : 'File system error'
        });
      }

      // Generic error
      return res.status(500).json({ 
        success: false,
        message: 'Failed to generate document. Please try again later.',
        error: process.env.NODE_ENV === 'development' ? aiError.message : 'An error occurred while processing your request'
      });
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Save an AI-generated document as a template
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const saveAsTemplate = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { documentId, title, description } = req.body;
    const userId = req.user.id;
    const farmId = req.body.farmId;

    try {
      const schema = getSchema();

      // Get the document details
      const documentResult = await withDatabaseRetry(async () => {
        // Set search_path to use the specified schema
        await sequelize.query(`SET search_path TO ${schema};`);

        return sequelize.query(
          'SELECT id, title, description, document_type, file_path, file_type, mime_type, file_size FROM signable_documents WHERE id = $1 AND farm_id = $2',
          { 
            replacements: [documentId, farmId],
            type: sequelize.QueryTypes.SELECT
          }
        );
      });

      if (documentResult.length === 0) {
        return res.status(404).json({ message: 'Document not found' });
      }

      const document = documentResult[0];

      // Create a template from the document
      const templateId = uuidv4();

      const templateResult = await withDatabaseRetry(async () => {
        // Set search_path to use the specified schema
        await sequelize.query(`SET search_path TO ${schema};`);

        // Insert into signable_documents table as a template
        const [templateInsertResult] = await sequelize.query(
          `INSERT INTO signable_documents 
          (id, farm_id, created_by, title, description, document_type, status, file_path, file_type, mime_type, file_size, is_template, template_source_id) 
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13) 
          RETURNING id, title, document_type, status, is_template`,
          { 
            replacements: [
              templateId, 
              farmId, 
              userId, 
              title || document.title, 
              description || document.description, 
              document.document_type, 
              'active',
              document.file_path,
              document.file_type,
              document.mime_type,
              document.file_size,
              true,
              documentId
            ],
            type: sequelize.QueryTypes.INSERT
          }
        );

        return templateInsertResult;
      });

      return res.status(200).json({ 
        success: true,
        template: {
          id: templateId,
          title: title || document.title,
          documentType: document.document_type
        }
      });
    } catch (error) {
      console.error('Error saving document as template:', error);

      // Check if this is a database error
      if (error.name && error.name.includes('Sequelize')) {
        return handleServerError(res, error);
      }

      return res.status(500).json({ 
        success: false,
        message: 'Failed to save document as template. Please try again later.',
        error: error.message
      });
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};
