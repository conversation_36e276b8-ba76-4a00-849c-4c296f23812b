import { sequelize } from '../config/database.js';
import { handleServerError } from '../utils/errorHandlers.js';
import User from '../models/User.js';

/**
 * Check if user is a global admin
 * @param {Object} req - Express request object
 * @returns {Promise<boolean>} - Whether the user is a global admin
 */
const isGlobalAdmin = async (req) => {
  const userId = req.user.id;
  const user = await User.findByPk(userId);
  return user && user.is_global_admin;
};

/**
 * Get all AI providers
 * @route GET /api/ai-configuration/providers
 * @returns {Array} providers - List of AI providers
 */
export const getProviders = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const providers = await sequelize.query(
      'SELECT * FROM site.ai_providers ORDER BY name ASC',
      { type: sequelize.QueryTypes.SELECT }
    );

    return res.status(200).json(providers);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get an AI provider by ID
 * @route GET /api/ai-configuration/providers/:id
 * @param {string} id - The provider ID
 * @returns {Object} provider - The provider
 */
export const getProviderById = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    const [provider] = await sequelize.query(
      'SELECT * FROM site.ai_providers WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!provider) {
      return res.status(404).json({ 
        error: 'Provider not found'
      });
    }

    return res.status(200).json(provider);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Create a new AI provider
 * @route POST /api/ai-configuration/providers
 * @param {string} name - The provider name
 * @param {string} description - The provider description
 * @param {string} api_base_url - The provider API base URL
 * @param {string} auth_type - The provider authentication type
 * @returns {Object} provider - The created provider
 */
export const createProvider = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { name, description, api_base_url, auth_type } = req.body;

    // Validate required parameters
    if (!name) {
      return res.status(400).json({ 
        error: 'Provider name is required'
      });
    }

    // Check if provider with this name already exists
    const [existingProvider] = await sequelize.query(
      'SELECT * FROM site.ai_providers WHERE name = $1',
      { 
        replacements: [name],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (existingProvider) {
      return res.status(400).json({ 
        error: 'A provider with this name already exists'
      });
    }

    // Create the provider
    const [result] = await sequelize.query(
      `INSERT INTO site.ai_providers (name, description, api_base_url, auth_type, is_enabled)
       VALUES ($1, $2, $3, $4, TRUE)
       RETURNING *`,
      { 
        replacements: [name, description, api_base_url, auth_type || 'api_key'],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return res.status(201).json(result);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI provider
 * @route PUT /api/ai-configuration/providers/:id
 * @param {string} id - The provider ID
 * @param {string} name - The provider name
 * @param {string} description - The provider description
 * @param {string} api_base_url - The provider API base URL
 * @param {string} auth_type - The provider authentication type
 * @param {boolean} is_enabled - Whether the provider is enabled
 * @returns {Object} provider - The updated provider
 */
export const updateProvider = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;
    const { name, description, api_base_url, auth_type, is_enabled } = req.body;

    // Validate required parameters
    if (!id || !name) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if provider exists
    const [existingProvider] = await sequelize.query(
      'SELECT * FROM site.ai_providers WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!existingProvider) {
      return res.status(404).json({ 
        error: 'Provider not found'
      });
    }

    // Check if another provider with this name already exists
    const [duplicateProvider] = await sequelize.query(
      'SELECT * FROM site.ai_providers WHERE name = $1 AND id != $2',
      { 
        replacements: [name, id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (duplicateProvider) {
      return res.status(400).json({ 
        error: 'Another provider with this name already exists'
      });
    }

    // Update the provider
    const [result] = await sequelize.query(
      `UPDATE site.ai_providers
       SET name = $1, description = $2, api_base_url = $3, auth_type = $4, is_enabled = $5
       WHERE id = $6
       RETURNING *`,
      { 
        replacements: [
          name, 
          description, 
          api_base_url, 
          auth_type || 'api_key',
          is_enabled !== undefined ? is_enabled : existingProvider.is_enabled,
          id
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return res.status(200).json(result);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Delete an AI provider
 * @route DELETE /api/ai-configuration/providers/:id
 * @param {string} id - The provider ID
 * @returns {Object} message - Success message
 */
export const deleteProvider = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      await transaction.rollback();
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if provider exists
    const [existingProvider] = await sequelize.query(
      'SELECT * FROM site.ai_providers WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT,
        transaction
      }
    );

    if (!existingProvider) {
      await transaction.rollback();
      return res.status(404).json({ 
        error: 'Provider not found'
      });
    }

    // Delete the provider (cascade will delete related models and configurations)
    await sequelize.query(
      'DELETE FROM site.ai_providers WHERE id = $1',
      { 
        replacements: [id],
        transaction
      }
    );

    await transaction.commit();
    return res.status(200).json({ 
      message: 'Provider deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    return handleServerError(res, error);
  }
};

/**
 * Get all AI models
 * @route GET /api/ai-configuration/models
 * @returns {Array} models - List of AI models
 */
export const getModels = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const models = await sequelize.query(
      `SELECT m.*, p.name as provider_name
       FROM site.ai_models m
       JOIN site.ai_providers p ON m.provider_id = p.id
       ORDER BY p.name ASC, m.name ASC`,
      { type: sequelize.QueryTypes.SELECT }
    );

    return res.status(200).json(models);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI models by provider ID
 * @route GET /api/ai-configuration/providers/:providerId/models
 * @param {string} providerId - The provider ID
 * @returns {Array} models - List of AI models for the provider
 */
export const getModelsByProvider = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { providerId } = req.params;

    // Validate required parameters
    if (!providerId) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    const models = await sequelize.query(
      `SELECT m.*, p.name as provider_name
       FROM site.ai_models m
       JOIN site.ai_providers p ON m.provider_id = p.id
       WHERE m.provider_id = $1
       ORDER BY m.name ASC`,
      { 
        replacements: [providerId],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return res.status(200).json(models);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get an AI model by ID
 * @route GET /api/ai-configuration/models/:id
 * @param {string} id - The model ID
 * @returns {Object} model - The model
 */
export const getModelById = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    const [model] = await sequelize.query(
      `SELECT m.*, p.name as provider_name
       FROM site.ai_models m
       JOIN site.ai_providers p ON m.provider_id = p.id
       WHERE m.id = $1`,
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!model) {
      return res.status(404).json({ 
        error: 'Model not found'
      });
    }

    return res.status(200).json(model);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Create a new AI model
 * @route POST /api/ai-configuration/models
 * @param {string} provider_id - The provider ID
 * @param {string} name - The model name
 * @param {string} model_identifier - The model identifier used in API calls
 * @param {string} description - The model description
 * @param {Array} capabilities - The model capabilities
 * @param {number} max_tokens - The maximum tokens the model can process
 * @returns {Object} model - The created model
 */
export const createModel = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { provider_id, name, model_identifier, description, capabilities, max_tokens } = req.body;

    // Validate required parameters
    if (!provider_id || !name || !model_identifier) {
      return res.status(400).json({ 
        error: 'Provider ID, name, and model identifier are required'
      });
    }

    // Check if provider exists
    const [provider] = await sequelize.query(
      'SELECT * FROM site.ai_providers WHERE id = $1',
      { 
        replacements: [provider_id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!provider) {
      return res.status(404).json({ 
        error: 'Provider not found'
      });
    }

    // Check if model with this identifier already exists for this provider
    const [existingModel] = await sequelize.query(
      'SELECT * FROM site.ai_models WHERE provider_id = $1 AND model_identifier = $2',
      { 
        replacements: [provider_id, model_identifier],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (existingModel) {
      return res.status(400).json({ 
        error: 'A model with this identifier already exists for this provider'
      });
    }

    // Create the model
    const [result] = await sequelize.query(
      `INSERT INTO site.ai_models (provider_id, name, model_identifier, description, capabilities, max_tokens, is_enabled)
       VALUES ($1, $2, $3, $4, $5, $6, TRUE)
       RETURNING *`,
      { 
        replacements: [
          provider_id, 
          name, 
          model_identifier, 
          description, 
          capabilities ? JSON.stringify(capabilities).replace('[', '{').replace(']', '}') : null,
          max_tokens
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Add provider name to the result
    result.provider_name = provider.name;

    return res.status(201).json(result);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI model
 * @route PUT /api/ai-configuration/models/:id
 * @param {string} id - The model ID
 * @param {string} name - The model name
 * @param {string} model_identifier - The model identifier used in API calls
 * @param {string} description - The model description
 * @param {Array} capabilities - The model capabilities
 * @param {number} max_tokens - The maximum tokens the model can process
 * @param {boolean} is_enabled - Whether the model is enabled
 * @returns {Object} model - The updated model
 */
export const updateModel = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;
    const { name, model_identifier, description, capabilities, max_tokens, is_enabled } = req.body;

    // Validate required parameters
    if (!id || !name || !model_identifier) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if model exists
    const [existingModel] = await sequelize.query(
      'SELECT * FROM site.ai_models WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!existingModel) {
      return res.status(404).json({ 
        error: 'Model not found'
      });
    }

    // Check if another model with this identifier already exists for this provider
    const [duplicateModel] = await sequelize.query(
      'SELECT * FROM site.ai_models WHERE provider_id = $1 AND model_identifier = $2 AND id != $3',
      { 
        replacements: [existingModel.provider_id, model_identifier, id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (duplicateModel) {
      return res.status(400).json({ 
        error: 'Another model with this identifier already exists for this provider'
      });
    }

    // Update the model
    const [result] = await sequelize.query(
      `UPDATE site.ai_models
       SET name = $1, model_identifier = $2, description = $3, capabilities = $4, max_tokens = $5, is_enabled = $6
       WHERE id = $7
       RETURNING *`,
      { 
        replacements: [
          name, 
          model_identifier, 
          description, 
          capabilities ? JSON.stringify(capabilities).replace('[', '{').replace(']', '}') : existingModel.capabilities,
          max_tokens,
          is_enabled !== undefined ? is_enabled : existingModel.is_enabled,
          id
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Get provider name
    const [provider] = await sequelize.query(
      'SELECT name FROM site.ai_providers WHERE id = $1',
      { 
        replacements: [result.provider_id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Add provider name to the result
    result.provider_name = provider.name;

    return res.status(200).json(result);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Delete an AI model
 * @route DELETE /api/ai-configuration/models/:id
 * @param {string} id - The model ID
 * @returns {Object} message - Success message
 */
export const deleteModel = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      await transaction.rollback();
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if model exists
    const [existingModel] = await sequelize.query(
      'SELECT * FROM site.ai_models WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT,
        transaction
      }
    );

    if (!existingModel) {
      await transaction.rollback();
      return res.status(404).json({ 
        error: 'Model not found'
      });
    }

    // Check if model is used as default in any configuration
    const [configUsingModel] = await sequelize.query(
      'SELECT * FROM site.ai_configurations WHERE default_model_id = $1 LIMIT 1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT,
        transaction
      }
    );

    if (configUsingModel) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Cannot delete model because it is used as default in one or more configurations'
      });
    }

    // Delete the model
    await sequelize.query(
      'DELETE FROM site.ai_models WHERE id = $1',
      { 
        replacements: [id],
        transaction
      }
    );

    await transaction.commit();
    return res.status(200).json({ 
      message: 'Model deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    return handleServerError(res, error);
  }
};

/**
 * Get all AI configurations
 * @route GET /api/ai-configuration/configurations
 * @returns {Array} configurations - List of AI configurations
 */
export const getConfigurations = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const configurations = await sequelize.query(
      `SELECT c.*, p.name as provider_name, m.name as model_name
       FROM site.ai_configurations c
       JOIN site.ai_providers p ON c.provider_id = p.id
       LEFT JOIN site.ai_models m ON c.default_model_id = m.id
       ORDER BY c.name ASC`,
      { type: sequelize.QueryTypes.SELECT }
    );

    // Remove sensitive information
    configurations.forEach(config => {
      if (config.api_key) {
        config.api_key = '********';
      }
    });

    return res.status(200).json(configurations);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get an AI configuration by ID
 * @route GET /api/ai-configuration/configurations/:id
 * @param {string} id - The configuration ID
 * @returns {Object} configuration - The configuration
 */
export const getConfigurationById = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    const [configuration] = await sequelize.query(
      `SELECT c.*, p.name as provider_name, m.name as model_name
       FROM site.ai_configurations c
       JOIN site.ai_providers p ON c.provider_id = p.id
       LEFT JOIN site.ai_models m ON c.default_model_id = m.id
       WHERE c.id = $1`,
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!configuration) {
      return res.status(404).json({ 
        error: 'Configuration not found'
      });
    }

    // Remove sensitive information
    if (configuration.api_key) {
      configuration.api_key = '********';
    }

    return res.status(200).json(configuration);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Create a new AI configuration
 * @route POST /api/ai-configuration/configurations
 * @param {string} name - The configuration name
 * @param {string} description - The configuration description
 * @param {string} provider_id - The provider ID
 * @param {string} default_model_id - The default model ID
 * @param {string} api_key - The API key
 * @param {Object} additional_settings - Additional settings as JSON
 * @param {boolean} is_global - Whether this is a global configuration
 * @returns {Object} configuration - The created configuration
 */
export const createConfiguration = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { name, description, provider_id, default_model_id, api_key, additional_settings, is_global } = req.body;

    // Validate required parameters
    if (!name || !provider_id) {
      return res.status(400).json({ 
        error: 'Name and provider ID are required'
      });
    }

    // Check if provider exists
    const [provider] = await sequelize.query(
      'SELECT * FROM site.ai_providers WHERE id = $1',
      { 
        replacements: [provider_id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!provider) {
      return res.status(404).json({ 
        error: 'Provider not found'
      });
    }

    // Check if model exists if provided
    if (default_model_id) {
      const [model] = await sequelize.query(
        'SELECT * FROM site.ai_models WHERE id = $1 AND provider_id = $2',
        { 
          replacements: [default_model_id, provider_id],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (!model) {
        return res.status(404).json({ 
          error: 'Model not found or does not belong to the specified provider'
        });
      }
    }

    // Check if configuration with this name already exists
    const [existingConfig] = await sequelize.query(
      'SELECT * FROM site.ai_configurations WHERE name = $1',
      { 
        replacements: [name],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (existingConfig) {
      return res.status(400).json({ 
        error: 'A configuration with this name already exists'
      });
    }

    // Create the configuration
    const [result] = await sequelize.query(
      `INSERT INTO site.ai_configurations (
        name, description, provider_id, default_model_id, api_key, api_key_encrypted,
        additional_settings, is_global, is_enabled
      )
      VALUES ($1, $2, $3, $4, $5, FALSE, $6, $7, TRUE)
      RETURNING *`,
      { 
        replacements: [
          name, 
          description, 
          provider_id, 
          default_model_id,
          api_key,
          additional_settings ? JSON.stringify(additional_settings) : null,
          is_global || false
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Get provider and model names
    const [providerInfo] = await sequelize.query(
      'SELECT name FROM site.ai_providers WHERE id = $1',
      { 
        replacements: [provider_id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    let modelName = null;
    if (default_model_id) {
      const [modelInfo] = await sequelize.query(
        'SELECT name FROM site.ai_models WHERE id = $1',
        { 
          replacements: [default_model_id],
          type: sequelize.QueryTypes.SELECT
        }
      );
      if (modelInfo) {
        modelName = modelInfo.name;
      }
    }

    // Add provider and model names to the result
    result.provider_name = providerInfo.name;
    result.model_name = modelName;

    // Remove sensitive information
    if (result.api_key) {
      result.api_key = '********';
    }

    return res.status(201).json(result);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI configuration
 * @route PUT /api/ai-configuration/configurations/:id
 * @param {string} id - The configuration ID
 * @param {string} name - The configuration name
 * @param {string} description - The configuration description
 * @param {string} default_model_id - The default model ID
 * @param {string} api_key - The API key
 * @param {Object} additional_settings - Additional settings as JSON
 * @param {boolean} is_global - Whether this is a global configuration
 * @param {boolean} is_enabled - Whether the configuration is enabled
 * @returns {Object} configuration - The updated configuration
 */
export const updateConfiguration = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;
    const { name, description, default_model_id, api_key, additional_settings, is_global, is_enabled } = req.body;

    // Validate required parameters
    if (!id || !name) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if configuration exists
    const [existingConfig] = await sequelize.query(
      'SELECT * FROM site.ai_configurations WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!existingConfig) {
      return res.status(404).json({ 
        error: 'Configuration not found'
      });
    }

    // Check if another configuration with this name already exists
    const [duplicateConfig] = await sequelize.query(
      'SELECT * FROM site.ai_configurations WHERE name = $1 AND id != $2',
      { 
        replacements: [name, id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (duplicateConfig) {
      return res.status(400).json({ 
        error: 'Another configuration with this name already exists'
      });
    }

    // Check if model exists if provided
    if (default_model_id) {
      const [model] = await sequelize.query(
        'SELECT * FROM site.ai_models WHERE id = $1 AND provider_id = $2',
        { 
          replacements: [default_model_id, existingConfig.provider_id],
          type: sequelize.QueryTypes.SELECT
        }
      );

      if (!model) {
        return res.status(404).json({ 
          error: 'Model not found or does not belong to the specified provider'
        });
      }
    }

    // Update the configuration
    const [result] = await sequelize.query(
      `UPDATE site.ai_configurations
       SET name = $1, description = $2, default_model_id = $3, 
           api_key = COALESCE($4, api_key), additional_settings = $5, 
           is_global = $6, is_enabled = $7
       WHERE id = $8
       RETURNING *`,
      { 
        replacements: [
          name, 
          description, 
          default_model_id,
          api_key || null,
          additional_settings ? JSON.stringify(additional_settings) : existingConfig.additional_settings,
          is_global !== undefined ? is_global : existingConfig.is_global,
          is_enabled !== undefined ? is_enabled : existingConfig.is_enabled,
          id
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    // Get provider and model names
    const [provider] = await sequelize.query(
      'SELECT name FROM site.ai_providers WHERE id = $1',
      { 
        replacements: [result.provider_id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    let modelName = null;
    if (result.default_model_id) {
      const [model] = await sequelize.query(
        'SELECT name FROM site.ai_models WHERE id = $1',
        { 
          replacements: [result.default_model_id],
          type: sequelize.QueryTypes.SELECT
        }
      );
      if (model) {
        modelName = model.name;
      }
    }

    // Add provider and model names to the result
    result.provider_name = provider.name;
    result.model_name = modelName;

    // Remove sensitive information
    if (result.api_key) {
      result.api_key = '********';
    }

    return res.status(200).json(result);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Delete an AI configuration
 * @route DELETE /api/ai-configuration/configurations/:id
 * @param {string} id - The configuration ID
 * @returns {Object} message - Success message
 */
export const deleteConfiguration = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      await transaction.rollback();
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if configuration exists
    const [existingConfig] = await sequelize.query(
      'SELECT * FROM site.ai_configurations WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT,
        transaction
      }
    );

    if (!existingConfig) {
      await transaction.rollback();
      return res.status(404).json({ 
        error: 'Configuration not found'
      });
    }

    // Check if this is the only global configuration
    if (existingConfig.is_global) {
      const [globalConfigCount] = await sequelize.query(
        'SELECT COUNT(*) as count FROM site.ai_configurations WHERE is_global = TRUE',
        { 
          type: sequelize.QueryTypes.SELECT,
          transaction
        }
      );

      if (globalConfigCount.count <= 1) {
        await transaction.rollback();
        return res.status(400).json({ 
          error: 'Cannot delete the only global configuration'
        });
      }
    }

    // Delete the configuration
    await sequelize.query(
      'DELETE FROM site.ai_configurations WHERE id = $1',
      { 
        replacements: [id],
        transaction
      }
    );

    await transaction.commit();
    return res.status(200).json({ 
      message: 'Configuration deleted successfully'
    });
  } catch (error) {
    await transaction.rollback();
    return handleServerError(res, error);
  }
};

/**
 * Get all AI instructions
 * @route GET /api/ai-configuration/instructions
 * @returns {Array} instructions - List of AI instructions
 */
export const getInstructions = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const instructions = await sequelize.query(
      'SELECT * FROM site.ai_instructions ORDER BY name ASC',
      { type: sequelize.QueryTypes.SELECT }
    );

    return res.status(200).json(instructions);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get AI instructions by task type
 * @route GET /api/ai-configuration/instructions/task/:taskType
 * @param {string} taskType - The task type
 * @returns {Array} instructions - List of AI instructions for the task type
 */
export const getInstructionsByTaskType = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { taskType } = req.params;

    // Validate required parameters
    if (!taskType) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    const instructions = await sequelize.query(
      'SELECT * FROM site.ai_instructions WHERE task_type = $1 ORDER BY name ASC',
      { 
        replacements: [taskType],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return res.status(200).json(instructions);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Get an AI instruction by ID
 * @route GET /api/ai-configuration/instructions/:id
 * @param {string} id - The instruction ID
 * @returns {Object} instruction - The instruction
 */
export const getInstructionById = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    const [instruction] = await sequelize.query(
      'SELECT * FROM site.ai_instructions WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!instruction) {
      return res.status(404).json({ 
        error: 'Instruction not found'
      });
    }

    return res.status(200).json(instruction);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Create a new AI instruction
 * @route POST /api/ai-configuration/instructions
 * @param {string} name - The instruction name
 * @param {string} description - The instruction description
 * @param {string} task_type - The task type
 * @param {string} instructions - The actual instructions
 * @param {string} example_input - Example input for testing
 * @param {string} example_output - Example expected output
 * @returns {Object} instruction - The created instruction
 */
export const createInstruction = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { name, description, task_type, instructions, example_input, example_output } = req.body;

    // Validate required parameters
    if (!name || !task_type || !instructions) {
      return res.status(400).json({ 
        error: 'Name, task type, and instructions are required'
      });
    }

    // Check if instruction with this name already exists
    const [existingInstruction] = await sequelize.query(
      'SELECT * FROM site.ai_instructions WHERE name = $1',
      { 
        replacements: [name],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (existingInstruction) {
      return res.status(400).json({ 
        error: 'An instruction with this name already exists'
      });
    }

    // Create the instruction
    const [result] = await sequelize.query(
      `INSERT INTO site.ai_instructions (name, description, task_type, instructions, example_input, example_output, is_enabled)
       VALUES ($1, $2, $3, $4, $5, $6, TRUE)
       RETURNING *`,
      { 
        replacements: [name, description, task_type, instructions, example_input, example_output],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return res.status(201).json(result);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Update an AI instruction
 * @route PUT /api/ai-configuration/instructions/:id
 * @param {string} id - The instruction ID
 * @param {string} name - The instruction name
 * @param {string} description - The instruction description
 * @param {string} task_type - The task type
 * @param {string} instructions - The actual instructions
 * @param {string} example_input - Example input for testing
 * @param {string} example_output - Example expected output
 * @param {boolean} is_enabled - Whether the instruction is enabled
 * @returns {Object} instruction - The updated instruction
 */
export const updateInstruction = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;
    const { name, description, task_type, instructions, example_input, example_output, is_enabled } = req.body;

    // Validate required parameters
    if (!id || !name || !task_type || !instructions) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if instruction exists
    const [existingInstruction] = await sequelize.query(
      'SELECT * FROM site.ai_instructions WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!existingInstruction) {
      return res.status(404).json({ 
        error: 'Instruction not found'
      });
    }

    // Check if another instruction with this name already exists
    const [duplicateInstruction] = await sequelize.query(
      'SELECT * FROM site.ai_instructions WHERE name = $1 AND id != $2',
      { 
        replacements: [name, id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (duplicateInstruction) {
      return res.status(400).json({ 
        error: 'Another instruction with this name already exists'
      });
    }

    // Update the instruction
    const [result] = await sequelize.query(
      `UPDATE site.ai_instructions
       SET name = $1, description = $2, task_type = $3, instructions = $4, 
           example_input = $5, example_output = $6, is_enabled = $7
       WHERE id = $8
       RETURNING *`,
      { 
        replacements: [
          name, 
          description, 
          task_type, 
          instructions, 
          example_input, 
          example_output, 
          is_enabled !== undefined ? is_enabled : existingInstruction.is_enabled,
          id
        ],
        type: sequelize.QueryTypes.SELECT
      }
    );

    return res.status(200).json(result);
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Delete an AI instruction
 * @route DELETE /api/ai-configuration/instructions/:id
 * @param {string} id - The instruction ID
 * @returns {Object} message - Success message
 */
export const deleteInstruction = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { id } = req.params;

    // Validate required parameters
    if (!id) {
      return res.status(400).json({ 
        error: 'Missing required parameters'
      });
    }

    // Check if instruction exists
    const [existingInstruction] = await sequelize.query(
      'SELECT * FROM site.ai_instructions WHERE id = $1',
      { 
        replacements: [id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!existingInstruction) {
      return res.status(404).json({ 
        error: 'Instruction not found'
      });
    }

    // Delete the instruction
    await sequelize.query(
      'DELETE FROM site.ai_instructions WHERE id = $1',
      { 
        replacements: [id]
      }
    );

    return res.status(200).json({ 
      message: 'Instruction deleted successfully'
    });
  } catch (error) {
    return handleServerError(res, error);
  }
};

/**
 * Test an AI configuration with a prompt
 * @route POST /api/ai-configuration/test
 * @param {string} configuration_id - The configuration ID
 * @param {string} model_id - The model ID (optional, uses default from configuration if not provided)
 * @param {string} prompt - The prompt to test
 * @returns {Object} response - The AI response
 */
export const testConfiguration = async (req, res) => {
  try {
    // Check if user is a global admin
    if (!await isGlobalAdmin(req)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only global admins can access AI configuration.'
      });
    }

    const { configuration_id, model_id, prompt } = req.body;

    // Validate required parameters
    if (!configuration_id || !prompt) {
      return res.status(400).json({ 
        error: 'Configuration ID and prompt are required'
      });
    }

    // Get the configuration
    const [configuration] = await sequelize.query(
      'SELECT * FROM site.ai_configurations WHERE id = ?',
      { 
        replacements: [configuration_id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!configuration) {
      return res.status(404).json({ 
        error: 'Configuration not found'
      });
    }

    // Get the provider
    const [provider] = await sequelize.query(
      'SELECT * FROM site.ai_providers WHERE id = ?',
      { 
        replacements: [configuration.provider_id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!provider) {
      return res.status(404).json({ 
        error: 'Provider not found'
      });
    }

    // Get the model
    const modelIdToUse = model_id || configuration.default_model_id;
    if (!modelIdToUse) {
      return res.status(400).json({ 
        error: 'No model specified and no default model in configuration'
      });
    }

    const [model] = await sequelize.query(
      'SELECT * FROM site.ai_models WHERE id = ?',
      { 
        replacements: [modelIdToUse],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!model) {
      return res.status(404).json({ 
        error: 'Model not found'
      });
    }

    // Check if the API key is available
    const apiKey = configuration.api_key || process.env.OPENAI_API_KEY;
    if (!apiKey) {
      return res.status(400).json({ 
        error: 'No API key available for this configuration'
      });
    }

    // Import OpenAI dynamically to avoid issues if it's not installed
    try {
      const { default: OpenAI } = await import('openai');

      // Initialize OpenAI client with better error handling
      const openai = new OpenAI({
        apiKey: apiKey,
        baseURL: provider.api_base_url || undefined,
        timeout: 30000, // 30 second timeout (reduced from 2 minutes)
        maxRetries: 2 // Reduced retries to fail faster
      });

      // Call the API
      const completion = await openai.chat.completions.create({
        model: model.model_identifier,
        messages: [
          { role: "system", content: "You are a helpful assistant." },
          { role: "user", content: prompt }
        ],
        max_tokens: 500,
        temperature: 0.7,
      });

      const response = completion.choices[0].message.content.trim();

      return res.status(200).json({ 
        response,
        model: model.name,
        provider: provider.name
      });
    } catch (error) {
      console.error('Error calling AI API:', error);

      // Check for OpenAI quota exceeded error
      if (error.message && error.message.includes('quota')) {
        return res.status(503).json({
          error: 'AI service quota exceeded',
          details: 'The AI service quota has been exceeded. Please check your billing details or try again later.'
        });
      }

      // Check for OpenAI timeout or connection errors
      if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
        return res.status(503).json({
          error: 'AI service timeout',
          details: 'The AI service is experiencing connection issues. Please try again in a moment.'
        });
      }

      return res.status(500).json({
        error: 'Error calling AI API',
        details: error.message
      });
    }
  } catch (error) {
    return handleServerError(res, error);
  }
};
