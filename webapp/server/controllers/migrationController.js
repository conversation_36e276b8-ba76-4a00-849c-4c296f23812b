import { Op } from 'sequelize';
import MigrationSystem from '../models/MigrationSystem.js';
import MigrationJob from '../models/MigrationJob.js';
import MigrationResult from '../models/MigrationResult.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import fs from 'fs';
import path from 'path';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'migrations');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const jobId = req.params.jobId || 'temp';
    const uniqueFilename = `${jobId}_${Date.now()}_${file.originalname}`;
    cb(null, uniqueFilename);
  }
});

export const upload = multer({ storage });

/**
 * Get all supported migration systems
 */
export const getSupportedSystems = async (req, res) => {
  try {
    const systems = await MigrationSystem.findAll();
    
    return res.status(200).json({
      success: true,
      systems
    });
  } catch (error) {
    console.error('Error fetching migration systems:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch migration systems'
    });
  }
};

/**
 * Get migration system by ID
 */
export const getMigrationSystem = async (req, res) => {
  try {
    const { systemId } = req.params;
    
    const system = await MigrationSystem.findByPk(systemId);
    
    if (!system) {
      return res.status(404).json({
        success: false,
        error: 'Migration system not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      system
    });
  } catch (error) {
    console.error('Error fetching migration system:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch migration system'
    });
  }
};

/**
 * Get migration jobs for a farm
 */
export const getMigrationJobs = async (req, res) => {
  try {
    const { farmId } = req.query;
    
    if (!farmId) {
      return res.status(400).json({
        success: false,
        error: 'Farm ID is required'
      });
    }
    
    const jobs = await MigrationJob.findAll({
      where: { farm_id: farmId },
      include: [
        { model: User, as: 'user', attributes: ['id', 'first_name', 'last_name', 'email'] },
        { model: Farm, as: 'farm', attributes: ['id', 'name'] },
        { model: MigrationSystem, as: 'system' }
      ],
      order: [['created_at', 'DESC']]
    });
    
    return res.status(200).json({
      success: true,
      jobs
    });
  } catch (error) {
    console.error('Error fetching migration jobs:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch migration jobs'
    });
  }
};

/**
 * Get migration job by ID
 */
export const getMigrationJob = async (req, res) => {
  try {
    const { jobId } = req.params;
    
    const job = await MigrationJob.findByPk(jobId, {
      include: [
        { model: User, as: 'user', attributes: ['id', 'first_name', 'last_name', 'email'] },
        { model: Farm, as: 'farm', attributes: ['id', 'name'] },
        { model: MigrationSystem, as: 'system' },
        { model: MigrationResult, as: 'result' }
      ]
    });
    
    if (!job) {
      return res.status(404).json({
        success: false,
        error: 'Migration job not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      job
    });
  } catch (error) {
    console.error('Error fetching migration job:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch migration job'
    });
  }
};

/**
 * Get migration job results
 */
export const getMigrationResults = async (req, res) => {
  try {
    const { jobId } = req.params;
    
    const result = await MigrationResult.findOne({
      where: { job_id: jobId },
      include: [{ model: MigrationJob, as: 'job' }]
    });
    
    if (!result) {
      return res.status(404).json({
        success: false,
        error: 'Migration results not found'
      });
    }
    
    return res.status(200).json({
      success: true,
      results: result
    });
  } catch (error) {
    console.error('Error fetching migration results:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch migration results'
    });
  }
};

/**
 * Create a new migration job
 */
export const createMigrationJob = async (req, res) => {
  try {
    const { farmId, sourceSystem, entities } = req.body;
    const userId = req.user.id;
    
    if (!farmId || !sourceSystem || !entities || entities.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Farm ID, source system, and entities are required'
      });
    }
    
    // Check if the system exists
    const system = await MigrationSystem.findByPk(sourceSystem);
    if (!system) {
      return res.status(404).json({
        success: false,
        error: 'Migration system not found'
      });
    }
    
    // Create the job
    const job = await MigrationJob.create({
      user_id: userId,
      farm_id: farmId,
      source_system: sourceSystem,
      entities,
      status: 'pending'
    });
    
    return res.status(201).json({
      success: true,
      job
    });
  } catch (error) {
    console.error('Error creating migration job:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to create migration job'
    });
  }
};

/**
 * Upload migration data file
 */
export const uploadMigrationFile = async (req, res) => {
  try {
    const { jobId } = req.params;
    const { format } = req.body;
    
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }
    
    // Find the job
    const job = await MigrationJob.findByPk(jobId);
    if (!job) {
      // Remove the uploaded file
      fs.unlinkSync(req.file.path);
      
      return res.status(404).json({
        success: false,
        error: 'Migration job not found'
      });
    }
    
    // Update the job with the file path
    job.file_path = req.file.path;
    await job.save();
    
    return res.status(200).json({
      success: true,
      job
    });
  } catch (error) {
    console.error('Error uploading migration file:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to upload migration file'
    });
  }
};

/**
 * Start migration job processing
 */
export const startMigrationJob = async (req, res) => {
  try {
    const { jobId } = req.params;
    
    // Find the job
    const job = await MigrationJob.findByPk(jobId);
    if (!job) {
      return res.status(404).json({
        success: false,
        error: 'Migration job not found'
      });
    }
    
    // Check if the job has a file
    if (!job.file_path) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded for this job'
      });
    }
    
    // Update the job status
    job.status = 'in_progress';
    await job.save();
    
    // In a real implementation, you would start a background process to handle the migration
    // For now, we'll just create a dummy result
    const result = await MigrationResult.create({
      job_id: job.id,
      total_records: 100,
      imported_records: 80,
      failed_records: 20,
      warnings: ['Some records had missing data'],
      errors: ['Some records could not be imported'],
      entity_counts: {
        fields: { total: 50, imported: 40, failed: 10 },
        crops: { total: 50, imported: 40, failed: 10 }
      }
    });
    
    // Update the job status to completed
    job.status = 'completed';
    job.completed_at = new Date();
    await job.save();
    
    return res.status(200).json({
      success: true,
      job,
      result
    });
  } catch (error) {
    console.error('Error starting migration job:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to start migration job'
    });
  }
};

/**
 * Cancel migration job
 */
export const cancelMigrationJob = async (req, res) => {
  try {
    const { jobId } = req.params;
    
    // Find the job
    const job = await MigrationJob.findByPk(jobId);
    if (!job) {
      return res.status(404).json({
        success: false,
        error: 'Migration job not found'
      });
    }
    
    // Check if the job can be canceled
    if (job.status === 'completed' || job.status === 'failed') {
      return res.status(400).json({
        success: false,
        error: 'Cannot cancel a completed or failed job'
      });
    }
    
    // Update the job status
    job.status = 'failed';
    job.error_message = 'Job canceled by user';
    await job.save();
    
    return res.status(200).json({
      success: true,
      job
    });
  } catch (error) {
    console.error('Error canceling migration job:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to cancel migration job'
    });
  }
};

/**
 * Export data to a file for migration to another system
 */
export const exportData = async (req, res) => {
  try {
    const { farmId, targetSystem, entities, format } = req.body;
    
    if (!farmId || !targetSystem || !entities || entities.length === 0 || !format) {
      return res.status(400).json({
        success: false,
        error: 'Farm ID, target system, entities, and format are required'
      });
    }
    
    // Check if the system exists
    const system = await MigrationSystem.findByPk(targetSystem);
    if (!system) {
      return res.status(404).json({
        success: false,
        error: 'Migration system not found'
      });
    }
    
    // In a real implementation, you would query the database for the requested entities
    // and generate a file in the requested format
    // For now, we'll just create a dummy file
    const filename = `export_${farmId}_${targetSystem}_${Date.now()}.${format}`;
    const filePath = path.join(process.cwd(), 'uploads', 'migrations', filename);
    
    // Create a dummy file
    fs.writeFileSync(filePath, 'Dummy export data');
    
    // Send the file
    res.download(filePath, filename, (err) => {
      if (err) {
        console.error('Error sending file:', err);
      }
      
      // Delete the file after sending
      fs.unlinkSync(filePath);
    });
  } catch (error) {
    console.error('Error exporting data:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to export data'
    });
  }
};

/**
 * Get migration templates for a specific system
 */
export const getMigrationTemplates = async (req, res) => {
  try {
    const { systemId } = req.params;
    
    // Check if the system exists
    const system = await MigrationSystem.findByPk(systemId);
    if (!system) {
      return res.status(404).json({
        success: false,
        error: 'Migration system not found'
      });
    }
    
    // In a real implementation, you would generate template files for the requested system
    // For now, we'll just create a dummy file
    const filename = `template_${systemId}.zip`;
    const filePath = path.join(process.cwd(), 'uploads', 'migrations', filename);
    
    // Create a dummy file
    fs.writeFileSync(filePath, 'Dummy template data');
    
    // Send the file
    res.download(filePath, filename, (err) => {
      if (err) {
        console.error('Error sending file:', err);
      }
      
      // Delete the file after sending
      fs.unlinkSync(filePath);
    });
  } catch (error) {
    console.error('Error getting migration templates:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get migration templates'
    });
  }
};