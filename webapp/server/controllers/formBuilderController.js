import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import SignableDocument from '../models/SignableDocument.js';
import DocumentElement from '../models/DocumentElement.js';
import DocumentField from '../models/DocumentField.js';
import DocumentSigner from '../models/DocumentSigner.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import {
  generateStoragePath,
  saveFile,
  updateStorageUsage
} from '../utils/fileUtils.js';

// Import the helper functions from documentSigningController.js
import { checkUserFarmAccess, createAuditLog } from './documentSigningController.js';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Create a new form-built document
 */
export const createFormBuiltDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params;
    const { 
      title, 
      description, 
      documentType,
      elements = [],
      signers = []
    } = req.body;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Get tenant ID for the farm
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create document record
    const document = await SignableDocument.create({
      title: title || 'Untitled Document',
      description: description || '',
      document_type: documentType || 'other',
      status: 'draft',
      file_path: null, // Form-built documents don't have a file initially
      file_size: 0,
      file_type: 'html',
      mime_type: 'text/html',
      version: 1,
      is_template: false,
      template_id: null,
      tenant_id: farm.tenant_id,
      farm_id: farmId,
      created_by: req.user.id,
      is_form_built: true
    }, { transaction });

    // Create elements if provided
    if (elements.length > 0) {
      for (const element of elements) {
        let fieldId = null;

        // If element is a field, create a DocumentField record
        if (element.element_type === 'field') {
          const field = await DocumentField.create({
            document_id: document.id,
            signer_id: null, // Will be assigned later
            field_type: element.field_type || 'text',
            field_name: element.field_name || 'Untitled Field',
            field_value: null,
            is_required: element.is_required || false,
            page_number: element.page_number || 1,
            x_position: element.x_position || 0,
            y_position: element.y_position || 0,
            width: element.width || 100,
            height: element.height || 30,
            placeholder: element.placeholder || null,
            options: element.options || null,
            validation_rules: element.validation_rules || null,
            style_properties: element.style_properties || null
          }, { transaction });

          fieldId = field.id;
        }

        // Create the element
        await DocumentElement.create({
          document_id: document.id,
          element_type: element.element_type,
          content: element.content || null,
          image_path: element.image_path || null,
          field_id: fieldId,
          page_number: element.page_number || 1,
          x_position: element.x_position || 0,
          y_position: element.y_position || 0,
          width: element.width || 100,
          height: element.height || 30,
          z_index: element.z_index || 0,
          font_family: element.font_family || null,
          font_size: element.font_size || null,
          font_color: element.font_color || null,
          background_color: element.background_color || null,
          border_style: element.border_style || null,
          border_width: element.border_width || null,
          border_color: element.border_color || null
        }, { transaction });
      }
    }

    // Create signers if provided
    if (signers.length > 0) {
      for (let i = 0; i < signers.length; i++) {
        const signer = signers[i];
        await DocumentSigner.create({
          document_id: document.id,
          signer_email: signer.email,
          signer_name: signer.name,
          signer_role: signer.role || null,
          signer_order: signer.order || i + 1,
          status: 'pending',
          verification_method: signer.verificationMethod || 'email'
        }, { transaction });
      }
    }

    // Create audit log entry
    await createAuditLog(
      document.id,
      req.user.id,
      null,
      'form_built_document_created',
      { title: document.title },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    await transaction.commit();

    return res.status(201).json({
      message: 'Form-built document created successfully',
      document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating form-built document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get a form-built document by ID with all its elements
 */
export const getFormBuiltDocument = async (req, res) => {
  try {
    const { id } = req.params;

    // Get document with associations
    const document = await SignableDocument.findByPk(id, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'signableDocumentFarm',
          attributes: ['id', 'name']
        },
        {
          model: DocumentSigner,
          as: 'signers'
        },
        {
          model: DocumentElement,
          as: 'elements',
          include: [
            {
              model: DocumentField,
              as: 'field'
            }
          ]
        }
      ]
    });

    if (!document) {
      return res.status(404).json({ error: 'Document not found' });
    }

    if (!document.is_form_built) {
      return res.status(400).json({ error: 'Document is not a form-built document' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    return res.status(200).json(document);
  } catch (error) {
    console.error('Error getting form-built document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Update a form-built document
 */
export const updateFormBuiltDocument = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { 
      title, 
      description, 
      documentType,
      elements = []
    } = req.body;

    // Get document
    const document = await SignableDocument.findByPk(id);

    if (!document) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    if (!document.is_form_built) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document is not a form-built document' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, document.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Only allow updates if document is in draft status
    if (document.status !== 'draft') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document can only be updated in draft status' });
    }

    // Update document
    const updates = {};

    if (title) updates.title = title;
    if (description !== undefined) updates.description = description;
    if (documentType) updates.document_type = documentType;

    await document.update(updates, { transaction });

    // If elements are provided, delete existing elements and create new ones
    if (elements.length > 0) {
      // Get existing elements
      const existingElements = await DocumentElement.findAll({
        where: { document_id: document.id },
        include: [
          {
            model: DocumentField,
            as: 'field'
          }
        ]
      });

      // Delete existing elements and their fields
      for (const element of existingElements) {
        if (element.field) {
          await element.field.destroy({ transaction });
        }
        await element.destroy({ transaction });
      }

      // Create new elements
      for (const element of elements) {
        let fieldId = null;

        // If element is a field, create a DocumentField record
        if (element.element_type === 'field') {
          const field = await DocumentField.create({
            document_id: document.id,
            signer_id: element.signer_id || null,
            field_type: element.field_type || 'text',
            field_name: element.field_name || 'Untitled Field',
            field_value: null,
            is_required: element.is_required || false,
            page_number: element.page_number || 1,
            x_position: element.x_position || 0,
            y_position: element.y_position || 0,
            width: element.width || 100,
            height: element.height || 30,
            placeholder: element.placeholder || null,
            options: element.options || null,
            validation_rules: element.validation_rules || null,
            style_properties: element.style_properties || null
          }, { transaction });

          fieldId = field.id;
        }

        // Create the element
        await DocumentElement.create({
          document_id: document.id,
          element_type: element.element_type,
          content: element.content || null,
          image_path: element.image_path || null,
          field_id: fieldId,
          page_number: element.page_number || 1,
          x_position: element.x_position || 0,
          y_position: element.y_position || 0,
          width: element.width || 100,
          height: element.height || 30,
          z_index: element.z_index || 0,
          font_family: element.font_family || null,
          font_size: element.font_size || null,
          font_color: element.font_color || null,
          background_color: element.background_color || null,
          border_style: element.border_style || null,
          border_width: element.border_width || null,
          border_color: element.border_color || null
        }, { transaction });
      }
    }

    // Create audit log entry
    await createAuditLog(
      document.id,
      req.user.id,
      null,
      'form_built_document_updated',
      { updates },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    await transaction.commit();

    return res.status(200).json({
      message: 'Form-built document updated successfully',
      document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating form-built document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Save a form-built document as a template
 */
export const saveFormBuiltDocumentAsTemplate = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { title, description } = req.body;

    // Get the source document
    const sourceDocument = await SignableDocument.findByPk(id, {
      include: [
        {
          model: DocumentElement,
          as: 'elements',
          include: [
            {
              model: DocumentField,
              as: 'field'
            }
          ]
        }
      ]
    });

    if (!sourceDocument) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    if (!sourceDocument.is_form_built) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Document is not a form-built document' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, sourceDocument.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Create template record
    const template = await SignableDocument.create({
      title: title || `${sourceDocument.title} (Template)`,
      description: description || sourceDocument.description,
      document_type: sourceDocument.document_type,
      status: 'draft',
      file_path: null,
      file_size: 0,
      file_type: 'html',
      mime_type: 'text/html',
      version: 1,
      is_template: true,
      is_form_built: true,
      template_id: null,
      tenant_id: sourceDocument.tenant_id,
      farm_id: sourceDocument.farm_id,
      created_by: req.user.id
    }, { transaction });

    // Copy elements from source document
    if (sourceDocument.elements && sourceDocument.elements.length > 0) {
      for (const element of sourceDocument.elements) {
        let fieldId = null;

        // If element has a field, create a copy of it
        if (element.field) {
          const field = await DocumentField.create({
            document_id: template.id,
            signer_id: null, // Templates don't have signers
            field_type: element.field.field_type,
            field_name: element.field.field_name,
            field_value: null, // Don't copy values
            is_required: element.field.is_required,
            page_number: element.field.page_number,
            x_position: element.field.x_position,
            y_position: element.field.y_position,
            width: element.field.width,
            height: element.field.height,
            placeholder: element.field.placeholder,
            options: element.field.options,
            validation_rules: element.field.validation_rules,
            style_properties: element.field.style_properties
          }, { transaction });

          fieldId = field.id;
        }

        // Create the element
        await DocumentElement.create({
          document_id: template.id,
          element_type: element.element_type,
          content: element.content,
          image_path: element.image_path,
          field_id: fieldId,
          page_number: element.page_number,
          x_position: element.x_position,
          y_position: element.y_position,
          width: element.width,
          height: element.height,
          z_index: element.z_index,
          font_family: element.font_family,
          font_size: element.font_size,
          font_color: element.font_color,
          background_color: element.background_color,
          border_style: element.border_style,
          border_width: element.border_width,
          border_color: element.border_color
        }, { transaction });
      }
    }

    // Create audit log entry
    await createAuditLog(
      template.id,
      req.user.id,
      null,
      'template_created_from_form_built_document',
      { 
        title: template.title,
        source_document_id: sourceDocument.id,
        source_document_title: sourceDocument.title
      },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    await transaction.commit();

    return res.status(201).json({
      message: 'Template created successfully from form-built document',
      template
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating template from form-built document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Create a document from a form-built template
 */
export const createDocumentFromFormBuiltTemplate = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { templateId } = req.params;
    const { 
      title, 
      description,
      signers = []
    } = req.body;

    // Get the template
    const template = await SignableDocument.findOne({
      where: {
        id: templateId,
        is_template: true,
        is_form_built: true
      },
      include: [
        {
          model: DocumentElement,
          as: 'elements',
          include: [
            {
              model: DocumentField,
              as: 'field'
            }
          ]
        }
      ]
    });

    if (!template) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Form-built template not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, template.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this template' });
    }

    // Create document record
    const document = await SignableDocument.create({
      title: title || template.title,
      description: description || template.description,
      document_type: template.document_type,
      status: 'draft',
      file_path: null,
      file_size: 0,
      file_type: 'html',
      mime_type: 'text/html',
      version: 1,
      is_template: false,
      is_form_built: true,
      template_id: template.id,
      tenant_id: template.tenant_id,
      farm_id: template.farm_id,
      created_by: req.user.id
    }, { transaction });

    // Create signers if provided
    const signerMap = {}; // Map to store signer IDs for field assignment
    if (signers.length > 0) {
      for (let i = 0; i < signers.length; i++) {
        const signer = signers[i];
        const newSigner = await DocumentSigner.create({
          document_id: document.id,
          signer_email: signer.email,
          signer_name: signer.name,
          signer_role: signer.role || null,
          signer_order: signer.order || i + 1,
          status: 'pending',
          verification_method: signer.verificationMethod || 'email'
        }, { transaction });

        // Store signer ID with role for field assignment
        if (signer.role) {
          signerMap[signer.role] = newSigner.id;
        }
      }
    }

    // Copy elements from template
    if (template.elements && template.elements.length > 0) {
      for (const element of template.elements) {
        let fieldId = null;

        // If element has a field, create a copy of it
        if (element.field) {
          // Determine signer ID based on field name or role
          let signerId = null;

          // Check if field name contains a role that matches a signer
          for (const role in signerMap) {
            if (element.field.field_name.toLowerCase().includes(role.toLowerCase())) {
              signerId = signerMap[role];
              break;
            }
          }

          const field = await DocumentField.create({
            document_id: document.id,
            signer_id: signerId,
            field_type: element.field.field_type,
            field_name: element.field.field_name,
            field_value: null, // Don't copy values
            is_required: element.field.is_required,
            page_number: element.field.page_number,
            x_position: element.field.x_position,
            y_position: element.field.y_position,
            width: element.field.width,
            height: element.field.height,
            placeholder: element.field.placeholder,
            options: element.field.options,
            validation_rules: element.field.validation_rules,
            style_properties: element.field.style_properties
          }, { transaction });

          fieldId = field.id;
        }

        // Create the element
        await DocumentElement.create({
          document_id: document.id,
          element_type: element.element_type,
          content: element.content,
          image_path: element.image_path,
          field_id: fieldId,
          page_number: element.page_number,
          x_position: element.x_position,
          y_position: element.y_position,
          width: element.width,
          height: element.height,
          z_index: element.z_index,
          font_family: element.font_family,
          font_size: element.font_size,
          font_color: element.font_color,
          background_color: element.background_color,
          border_style: element.border_style,
          border_width: element.border_width,
          border_color: element.border_color
        }, { transaction });
      }
    }

    // Create audit log entry
    await createAuditLog(
      document.id,
      req.user.id,
      null,
      'document_created_from_form_built_template',
      { 
        title: document.title,
        template_id: template.id,
        template_title: template.title
      },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    await transaction.commit();

    return res.status(201).json({
      message: 'Document created successfully from form-built template',
      document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating document from form-built template:', error);
    return res.status(500).json({ error: error.message });
  }
};