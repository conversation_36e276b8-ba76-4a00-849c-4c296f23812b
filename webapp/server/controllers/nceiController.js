import { sequelize } from '../config/database.js';
import Farm from '../models/Farm.js';
import Field from '../models/Field.js';
import {
  findNearestStation,
  getHistoricalTemperatureData,
  getHistoricalPrecipitationData,
  getClimateNormals,
  getExtremeWeatherEvents,
  getGrowingDegreeDays,
  getFrostFreezeDates,
  getFarmClimateData
} from '../services/nceiServices.js';

/**
 * Get historical temperature data for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFarmHistoricalTemperature = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { startDate, endDate } = req.query;
    
    // Validate required parameters
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Missing required parameters: startDate and endDate' });
    }
    
    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }
    
    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }
    
    // Fetch historical temperature data
    const temperatureData = await getHistoricalTemperatureData(
      farmLocation.latitude,
      farmLocation.longitude,
      startDate,
      endDate
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeHistoricalTemperatureData(temperatureData, farmId, null);
    
    return res.status(200).json(temperatureData);
  } catch (error) {
    console.error('Error getting farm historical temperature:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get historical temperature data for a field
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFieldHistoricalTemperature = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { startDate, endDate } = req.query;
    
    // Validate required parameters
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Missing required parameters: startDate and endDate' });
    }
    
    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }
    
    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }
    
    // Fetch historical temperature data
    const temperatureData = await getHistoricalTemperatureData(
      fieldLocation.center.latitude,
      fieldLocation.center.longitude,
      startDate,
      endDate
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeHistoricalTemperatureData(temperatureData, field.farm_id, fieldId);
    
    return res.status(200).json(temperatureData);
  } catch (error) {
    console.error('Error getting field historical temperature:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get historical precipitation data for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFarmHistoricalPrecipitation = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { startDate, endDate } = req.query;
    
    // Validate required parameters
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Missing required parameters: startDate and endDate' });
    }
    
    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }
    
    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }
    
    // Fetch historical precipitation data
    const precipitationData = await getHistoricalPrecipitationData(
      farmLocation.latitude,
      farmLocation.longitude,
      startDate,
      endDate
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeHistoricalPrecipitationData(precipitationData, farmId, null);
    
    return res.status(200).json(precipitationData);
  } catch (error) {
    console.error('Error getting farm historical precipitation:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get historical precipitation data for a field
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFieldHistoricalPrecipitation = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { startDate, endDate } = req.query;
    
    // Validate required parameters
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Missing required parameters: startDate and endDate' });
    }
    
    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }
    
    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }
    
    // Fetch historical precipitation data
    const precipitationData = await getHistoricalPrecipitationData(
      fieldLocation.center.latitude,
      fieldLocation.center.longitude,
      startDate,
      endDate
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeHistoricalPrecipitationData(precipitationData, field.farm_id, fieldId);
    
    return res.status(200).json(precipitationData);
  } catch (error) {
    console.error('Error getting field historical precipitation:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get climate normals for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFarmClimateNormals = async (req, res) => {
  try {
    const { farmId } = req.params;
    
    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }
    
    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }
    
    // Fetch climate normals data
    const climateNormals = await getClimateNormals(
      farmLocation.latitude,
      farmLocation.longitude
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeClimateNormals(climateNormals);
    
    return res.status(200).json(climateNormals);
  } catch (error) {
    console.error('Error getting farm climate normals:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get climate normals for a field
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFieldClimateNormals = async (req, res) => {
  try {
    const { fieldId } = req.params;
    
    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }
    
    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }
    
    // Fetch climate normals data
    const climateNormals = await getClimateNormals(
      fieldLocation.center.latitude,
      fieldLocation.center.longitude
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeClimateNormals(climateNormals);
    
    return res.status(200).json(climateNormals);
  } catch (error) {
    console.error('Error getting field climate normals:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get growing degree days for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFarmGrowingDegreeDays = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { startDate, endDate, baseTemp = 50 } = req.query;
    
    // Validate required parameters
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Missing required parameters: startDate and endDate' });
    }
    
    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }
    
    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }
    
    // Fetch growing degree days data
    const gddData = await getGrowingDegreeDays(
      farmLocation.latitude,
      farmLocation.longitude,
      startDate,
      endDate,
      parseInt(baseTemp)
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeGrowingDegreeDays(gddData, farmId, null);
    
    return res.status(200).json(gddData);
  } catch (error) {
    console.error('Error getting farm growing degree days:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get growing degree days for a field
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFieldGrowingDegreeDays = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { startDate, endDate, baseTemp = 50 } = req.query;
    
    // Validate required parameters
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Missing required parameters: startDate and endDate' });
    }
    
    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }
    
    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }
    
    // Fetch growing degree days data
    const gddData = await getGrowingDegreeDays(
      fieldLocation.center.latitude,
      fieldLocation.center.longitude,
      startDate,
      endDate,
      parseInt(baseTemp)
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeGrowingDegreeDays(gddData, field.farm_id, fieldId);
    
    return res.status(200).json(gddData);
  } catch (error) {
    console.error('Error getting field growing degree days:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get frost/freeze dates for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFarmFrostFreezeDates = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year } = req.query;
    
    // Validate required parameters
    if (!year) {
      return res.status(400).json({ error: 'Missing required parameter: year' });
    }
    
    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }
    
    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }
    
    // Fetch frost/freeze dates data
    const frostFreezeDates = await getFrostFreezeDates(
      farmLocation.latitude,
      farmLocation.longitude,
      parseInt(year)
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeFrostFreezeDates(frostFreezeDates, farmId, null);
    
    return res.status(200).json(frostFreezeDates);
  } catch (error) {
    console.error('Error getting farm frost/freeze dates:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get frost/freeze dates for a field
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFieldFrostFreezeDates = async (req, res) => {
  try {
    const { fieldId } = req.params;
    const { year } = req.query;
    
    // Validate required parameters
    if (!year) {
      return res.status(400).json({ error: 'Missing required parameter: year' });
    }
    
    // Find field to ensure it exists and get its location
    const field = await Field.findByPk(fieldId);
    if (!field) {
      return res.status(404).json({ error: 'Field not found' });
    }
    
    // Get field location data (center point)
    const fieldLocation = field.location_data;
    if (!fieldLocation || !fieldLocation.center || !fieldLocation.center.latitude || !fieldLocation.center.longitude) {
      return res.status(400).json({ error: 'Field location data is missing or invalid' });
    }
    
    // Fetch frost/freeze dates data
    const frostFreezeDates = await getFrostFreezeDates(
      fieldLocation.center.latitude,
      fieldLocation.center.longitude,
      parseInt(year)
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeFrostFreezeDates(frostFreezeDates, field.farm_id, fieldId);
    
    return res.status(200).json(frostFreezeDates);
  } catch (error) {
    console.error('Error getting field frost/freeze dates:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get extreme weather events for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getFarmExtremeWeatherEvents = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { startDate, endDate } = req.query;
    
    // Validate required parameters
    if (!startDate || !endDate) {
      return res.status(400).json({ error: 'Missing required parameters: startDate and endDate' });
    }
    
    // Find farm to ensure it exists and get its location
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }
    
    // Get farm location data
    const farmLocation = farm.location_data;
    if (!farmLocation || !farmLocation.latitude || !farmLocation.longitude) {
      return res.status(400).json({ error: 'Farm location data is missing or invalid' });
    }
    
    // Fetch extreme weather events data
    const extremeWeatherEvents = await getExtremeWeatherEvents(
      farmLocation.latitude,
      farmLocation.longitude,
      startDate,
      endDate
    );
    
    // Store data in database for caching (this would be implemented in a real application)
    // await storeExtremeWeatherEvents(extremeWeatherEvents, farmId);
    
    return res.status(200).json(extremeWeatherEvents);
  } catch (error) {
    console.error('Error getting farm extreme weather events:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get all climate data for a farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllFarmClimateData = async (req, res) => {
  try {
    const { farmId } = req.params;
    
    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }
    
    // Fetch all climate data for the farm
    const climateData = await getFarmClimateData(farmId);
    
    return res.status(200).json(climateData);
  } catch (error) {
    console.error('Error getting all farm climate data:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Export all functions
export default {
  getFarmHistoricalTemperature,
  getFieldHistoricalTemperature,
  getFarmHistoricalPrecipitation,
  getFieldHistoricalPrecipitation,
  getFarmClimateNormals,
  getFieldClimateNormals,
  getFarmGrowingDegreeDays,
  getFieldGrowingDegreeDays,
  getFarmFrostFreezeDates,
  getFieldFrostFreezeDates,
  getFarmExtremeWeatherEvents,
  getAllFarmClimateData
};