import axios from 'axios';
import * as Sentry from '@sentry/node';

/**
 * Get all Sentry issues (errors)
 */
export const getAllIssues = async (req, res) => {
  try {
    const { query, status } = req.query;
    const sentryToken = process.env.SENTRY_AUTH_TOKEN || '';
    // Use the organization slug directly from the environment variable
    const sentryOrg = process.env.SENTRY_ORG || '4509238970286080';
    const sentryProject = process.env.SENTRY_PROJECT || '4509238976577536';

    if (!sentryToken) {
      Sentry.captureMessage('Sentry API token not configured');
      return res.status(500).json({
        success: false,
        error: 'Sentry API token not configured'
      });
    }

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (query) {
      queryParams.append('query', query);
    }
    if (status) {
      queryParams.append('status', status);
    }

    // Fetch issues from Sentry API
    const response = await axios.get(
      `https://sentry.io/api/0/projects/${sentryOrg}/${sentryProject}/issues/?${queryParams.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${sentryToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return res.status(200).json({
      success: true,
      issues: response.data
    });
  } catch (error) {
    console.error('Error fetching Sentry issues:', error);
    Sentry.captureException(error);

    // Handle specific error types
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const statusCode = error.response.status;
      const errorMessage = error.response.data?.detail || error.response.data?.message || error.message;

      console.error(`Sentry API responded with status ${statusCode}:`, errorMessage);

      if (statusCode === 401) {
        return res.status(500).json({
          success: false,
          error: 'Authentication failed with Sentry API. Check your SENTRY_AUTH_TOKEN.',
          details: errorMessage
        });
      } else if (statusCode === 404) {
        return res.status(404).json({
          success: false,
          error: 'Resource not found in Sentry API',
          details: errorMessage
        });
      }

      return res.status(500).json({
        success: false,
        error: `Sentry API error (${statusCode})`,
        details: errorMessage
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received from Sentry API:', error.request);
      return res.status(500).json({
        success: false,
        error: 'No response received from Sentry API',
        details: 'The request was made but no response was received'
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch Sentry issues',
        details: error.message
      });
    }
  }
};

/**
 * Get a specific Sentry issue by ID
 */
export const getIssue = async (req, res) => {
  try {
    const { issueId } = req.params;
    const sentryToken = process.env.SENTRY_AUTH_TOKEN || '';
    // Use the organization slug directly from the environment variable
    const sentryOrg = process.env.SENTRY_ORG || '4509238970286080';

    if (!sentryToken) {
      Sentry.captureMessage('Sentry API token not configured');
      return res.status(500).json({
        success: false,
        error: 'Sentry API token not configured'
      });
    }

    // Fetch issue from Sentry API
    const response = await axios.get(
      `https://sentry.io/api/0/issues/${issueId}/`,
      {
        headers: {
          'Authorization': `Bearer ${sentryToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return res.status(200).json({
      success: true,
      issue: response.data
    });
  } catch (error) {
    console.error('Error fetching Sentry issue:', error);
    Sentry.captureException(error);

    // Handle specific error types
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const statusCode = error.response.status;
      const errorMessage = error.response.data?.detail || error.response.data?.message || error.message;

      console.error(`Sentry API responded with status ${statusCode}:`, errorMessage);

      if (statusCode === 401) {
        return res.status(500).json({
          success: false,
          error: 'Authentication failed with Sentry API. Check your SENTRY_AUTH_TOKEN.',
          details: errorMessage
        });
      } else if (statusCode === 404) {
        return res.status(404).json({
          success: false,
          error: 'Issue not found in Sentry API',
          details: errorMessage
        });
      }

      return res.status(500).json({
        success: false,
        error: `Sentry API error (${statusCode})`,
        details: errorMessage
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received from Sentry API:', error.request);
      return res.status(500).json({
        success: false,
        error: 'No response received from Sentry API',
        details: 'The request was made but no response was received'
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch Sentry issue',
        details: error.message
      });
    }
  }
};

/**
 * Get events for a specific Sentry issue
 */
export const getIssueEvents = async (req, res) => {
  try {
    const { issueId } = req.params;
    const sentryToken = process.env.SENTRY_AUTH_TOKEN || '';
    // Use the organization slug directly from the environment variable
    const sentryOrg = process.env.SENTRY_ORG || '4509238970286080';

    if (!sentryToken) {
      Sentry.captureMessage('Sentry API token not configured');
      return res.status(500).json({
        success: false,
        error: 'Sentry API token not configured'
      });
    }

    // Fetch events from Sentry API
    const response = await axios.get(
      `https://sentry.io/api/0/issues/${issueId}/events/`,
      {
        headers: {
          'Authorization': `Bearer ${sentryToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return res.status(200).json({
      success: true,
      events: response.data
    });
  } catch (error) {
    console.error('Error fetching Sentry issue events:', error);
    Sentry.captureException(error);

    // Handle specific error types
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const statusCode = error.response.status;
      const errorMessage = error.response.data?.detail || error.response.data?.message || error.message;

      console.error(`Sentry API responded with status ${statusCode}:`, errorMessage);

      if (statusCode === 401) {
        return res.status(500).json({
          success: false,
          error: 'Authentication failed with Sentry API. Check your SENTRY_AUTH_TOKEN.',
          details: errorMessage
        });
      } else if (statusCode === 404) {
        return res.status(404).json({
          success: false,
          error: 'Issue or events not found in Sentry API',
          details: errorMessage
        });
      }

      return res.status(500).json({
        success: false,
        error: `Sentry API error (${statusCode})`,
        details: errorMessage
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received from Sentry API:', error.request);
      return res.status(500).json({
        success: false,
        error: 'No response received from Sentry API',
        details: 'The request was made but no response was received'
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch Sentry issue events',
        details: error.message
      });
    }
  }
};

/**
 * Get stats for Sentry issues
 */
export const getStats = async (req, res) => {
  try {
    const sentryToken = process.env.SENTRY_AUTH_TOKEN || '';
    // Use the organization slug directly from the environment variable
    const sentryOrg = process.env.SENTRY_ORG || '4509238970286080';
    const sentryProject = process.env.SENTRY_PROJECT || '4509238976577536';

    if (!sentryToken) {
      Sentry.captureMessage('Sentry API token not configured');
      return res.status(500).json({
        success: false,
        error: 'Sentry API token not configured'
      });
    }

    // Get the current date and 30 days ago
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Format dates for Sentry API
    const statsSince = thirtyDaysAgo.toISOString();
    const statsUntil = now.toISOString();

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('statsPeriod', '30d');
    queryParams.append('statsCategory', 'error');

    // Fetch stats from Sentry API
    const response = await axios.get(
      `https://sentry.io/api/0/projects/${sentryOrg}/${sentryProject}/stats/?${queryParams.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${sentryToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return res.status(200).json({
      success: true,
      stats: response.data
    });
  } catch (error) {
    console.error('Error fetching Sentry stats:', error);
    Sentry.captureException(error);

    // Handle specific error types
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const statusCode = error.response.status;
      const errorMessage = error.response.data?.detail || error.response.data?.message || error.message;

      console.error(`Sentry API responded with status ${statusCode}:`, errorMessage);

      if (statusCode === 401) {
        return res.status(500).json({
          success: false,
          error: 'Authentication failed with Sentry API. Check your SENTRY_AUTH_TOKEN.',
          details: errorMessage
        });
      } else if (statusCode === 404) {
        return res.status(404).json({
          success: false,
          error: 'Stats resource not found in Sentry API',
          details: errorMessage
        });
      }

      return res.status(500).json({
        success: false,
        error: `Sentry API error (${statusCode})`,
        details: errorMessage
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received from Sentry API:', error.request);
      return res.status(500).json({
        success: false,
        error: 'No response received from Sentry API',
        details: 'The request was made but no response was received'
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      return res.status(500).json({
        success: false,
        error: 'Failed to fetch Sentry stats',
        details: error.message
      });
    }
  }
};

/**
 * Resolve a Sentry issue
 */
export const resolveIssue = async (req, res) => {
  try {
    const { issueId } = req.params;
    const sentryToken = process.env.SENTRY_AUTH_TOKEN || '';
    // Use the organization slug directly from the environment variable
    const sentryOrg = process.env.SENTRY_ORG || '4509238970286080';

    if (!sentryToken) {
      Sentry.captureMessage('Sentry API token not configured');
      return res.status(500).json({
        success: false,
        error: 'Sentry API token not configured'
      });
    }

    // Resolve issue in Sentry API
    const response = await axios.put(
      `https://sentry.io/api/0/issues/${issueId}/`,
      { status: 'resolved' },
      {
        headers: {
          'Authorization': `Bearer ${sentryToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return res.status(200).json({
      success: true,
      issue: response.data,
      message: 'Issue resolved successfully'
    });
  } catch (error) {
    console.error('Error resolving Sentry issue:', error);
    Sentry.captureException(error);

    // Handle specific error types
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const statusCode = error.response.status;
      const errorMessage = error.response.data?.detail || error.response.data?.message || error.message;

      console.error(`Sentry API responded with status ${statusCode}:`, errorMessage);

      if (statusCode === 401) {
        return res.status(500).json({
          success: false,
          error: 'Authentication failed with Sentry API. Check your SENTRY_AUTH_TOKEN.',
          details: errorMessage
        });
      } else if (statusCode === 404) {
        return res.status(404).json({
          success: false,
          error: 'Issue not found in Sentry API',
          details: errorMessage
        });
      }

      return res.status(500).json({
        success: false,
        error: `Sentry API error (${statusCode})`,
        details: errorMessage
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error('No response received from Sentry API:', error.request);
      return res.status(500).json({
        success: false,
        error: 'No response received from Sentry API',
        details: 'The request was made but no response was received'
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      return res.status(500).json({
        success: false,
        error: 'Failed to resolve Sentry issue',
        details: error.message
      });
    }
  }
};
