import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import SignableDocument from '../models/SignableDocument.js';
import DocumentField from '../models/DocumentField.js';
import DocumentSigner from '../models/DocumentSigner.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import {
  validateFileType,
  generateStoragePath,
  saveFile,
  checkStorageQuota,
  updateStorageUsage
} from '../utils/fileUtils.js';
import { v4 as uuidv4 } from 'uuid';

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base upload directory
const uploadsDir = path.join(__dirname, '..', '..', 'uploads');

// Import the helper functions from documentSigningController.js
import { checkUserFarmAccess, createAuditLog } from './documentSigningController.js';

/**
 * Get all document templates for a farm
 */
export const getAllTemplates = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      search, 
      documentType, 
      page = 1, 
      limit = 50, 
      sortBy = 'created_at', 
      sortOrder = 'desc' 
    } = req.query;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Build query conditions
    const where = { 
      farm_id: farmId,
      is_template: true
    };

    // Add search condition if provided
    if (search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Filter by document type if provided
    if (documentType) {
      where.document_type = documentType;
    }

    // Calculate pagination
    const offset = (page - 1) * limit;

    // Determine sort order
    const order = [[sortBy, sortOrder.toUpperCase()]];

    // Get templates
    const { count, rows: templates } = await SignableDocument.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'signableDocumentFarm',
          attributes: ['id', 'name']
        }
      ],
      order,
      limit,
      offset
    });

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    return res.status(200).json({
      templates,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages
      }
    });
  } catch (error) {
    console.error('Error getting document templates:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Get a document template by ID
 */
export const getTemplateById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get template with associations
    const template = await SignableDocument.findOne({
      where: {
        id,
        is_template: true
      },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: Farm,
          as: 'signableDocumentFarm',
          attributes: ['id', 'name']
        },
        {
          model: DocumentField,
          as: 'fields'
        }
      ]
    });

    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, template.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'You do not have access to this template' });
    }

    return res.status(200).json(template);
  } catch (error) {
    console.error('Error getting document template:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Create a new document template
 */
export const createTemplate = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params;
    const { 
      title, 
      description, 
      documentType,
      fields = []
    } = req.body;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Check if file was uploaded
    if (!req.files || !req.files.file) {
      await transaction.rollback();
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const file = req.files.file;

    // Validate file type
    const fileBuffer = Buffer.from(file.data);
    const fileTypeValidation = await validateFileType(file.name, fileBuffer);

    if (!fileTypeValidation.valid) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: 'Invalid file type', 
        reason: fileTypeValidation.reason 
      });
    }

    // Check storage quota
    const quotaCheck = await checkStorageQuota(farmId, file.size, req.user.is_global_admin);

    if (!quotaCheck.allowed) {
      await transaction.rollback();
      return res.status(400).json({ 
        error: quotaCheck.reason,
        currentUsage: quotaCheck.currentUsage,
        quota: quotaCheck.quota
      });
    }

    // Get tenant ID for the farm
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Generate storage path
    const storagePath = generateStoragePath(farmId, req.user.id, file.name);

    // Save file to disk
    const fullPath = await saveFile(file.data, storagePath);

    // Create template record
    const template = await SignableDocument.create({
      title: title || file.name,
      description: description || '',
      document_type: documentType || 'other',
      status: 'draft',
      file_path: storagePath,
      file_size: file.size,
      file_type: path.extname(file.name).substring(1) || 'unknown',
      mime_type: fileTypeValidation.detectedType || file.mimetype,
      version: 1,
      is_template: true,
      template_id: null,
      tenant_id: farm.tenant_id,
      farm_id: farmId,
      created_by: req.user.id
    }, { transaction });

    // Create fields if provided
    if (fields.length > 0) {
      for (const field of fields) {
        await DocumentField.create({
          document_id: template.id,
          signer_id: null, // Templates don't have signers
          field_type: field.type,
          field_name: field.name,
          field_value: field.defaultValue || null,
          is_required: field.required || false,
          page_number: field.page || 1,
          x_position: field.x || 0,
          y_position: field.y || 0,
          width: field.width || 100,
          height: field.height || 30
        }, { transaction });
      }
    }

    // Create audit log entry
    await createAuditLog(
      template.id,
      req.user.id,
      null,
      'template_created',
      { title: template.title },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    // Update storage usage
    await updateStorageUsage(farmId, file.size, false, 1);

    await transaction.commit();

    return res.status(201).json({
      message: 'Template created successfully',
      template
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating document template:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Save an existing document as a template
 */
export const saveAsTemplate = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { title, description } = req.body;

    // Get the source document
    const sourceDocument = await SignableDocument.findByPk(id, {
      include: [
        {
          model: DocumentField,
          as: 'fields'
        }
      ]
    });

    if (!sourceDocument) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Document not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, sourceDocument.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this document' });
    }

    // Create template record
    const template = await SignableDocument.create({
      title: title || `${sourceDocument.title} (Template)`,
      description: description || sourceDocument.description,
      document_type: sourceDocument.document_type,
      status: 'draft',
      file_path: sourceDocument.file_path,
      file_size: sourceDocument.file_size,
      file_type: sourceDocument.file_type,
      mime_type: sourceDocument.mime_type,
      version: 1,
      is_template: true,
      template_id: null,
      tenant_id: sourceDocument.tenant_id,
      farm_id: sourceDocument.farm_id,
      created_by: req.user.id
    }, { transaction });

    // Copy fields from source document
    if (sourceDocument.fields && sourceDocument.fields.length > 0) {
      for (const field of sourceDocument.fields) {
        await DocumentField.create({
          document_id: template.id,
          signer_id: null, // Templates don't have signers
          field_type: field.field_type,
          field_name: field.field_name,
          field_value: null, // Don't copy values
          is_required: field.is_required,
          page_number: field.page_number,
          x_position: field.x_position,
          y_position: field.y_position,
          width: field.width,
          height: field.height
        }, { transaction });
      }
    }

    // Create audit log entry
    await createAuditLog(
      template.id,
      req.user.id,
      null,
      'template_created_from_document',
      { 
        title: template.title,
        source_document_id: sourceDocument.id,
        source_document_title: sourceDocument.title
      },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    await transaction.commit();

    return res.status(201).json({
      message: 'Template created successfully from document',
      template
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating template from document:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Create a document from a template
 */
export const createDocumentFromTemplate = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { templateId } = req.params;
    const { 
      title, 
      description,
      signers = []
    } = req.body;

    // Get the template
    const template = await SignableDocument.findOne({
      where: {
        id: templateId,
        is_template: true
      },
      include: [
        {
          model: DocumentField,
          as: 'fields'
        }
      ]
    });

    if (!template) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Template not found' });
    }

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, template.farm_id);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this template' });
    }

    // Create document record
    const document = await SignableDocument.create({
      title: title || template.title,
      description: description || template.description,
      document_type: template.document_type,
      status: 'draft',
      file_path: template.file_path,
      file_size: template.file_size,
      file_type: template.file_type,
      mime_type: template.mime_type,
      version: 1,
      is_template: false,
      template_id: template.id,
      tenant_id: template.tenant_id,
      farm_id: template.farm_id,
      created_by: req.user.id
    }, { transaction });

    // Create signers if provided
    const signerMap = {}; // Map to store signer IDs for field assignment
    if (signers.length > 0) {
      for (let i = 0; i < signers.length; i++) {
        const signer = signers[i];
        const newSigner = await DocumentSigner.create({
          document_id: document.id,
          signer_email: signer.email,
          signer_name: signer.name,
          signer_role: signer.role || null,
          signer_order: signer.order || i + 1,
          status: 'pending',
          verification_method: signer.verificationMethod || 'email'
        }, { transaction });

        // Store signer ID with role for field assignment
        if (signer.role) {
          signerMap[signer.role] = newSigner.id;
        }
      }
    }

    // Copy fields from template
    if (template.fields && template.fields.length > 0) {
      for (const field of template.fields) {
        // Determine signer ID based on field name or role
        let signerId = null;

        // Check if field name contains a role that matches a signer
        for (const role in signerMap) {
          if (field.field_name.toLowerCase().includes(role.toLowerCase())) {
            signerId = signerMap[role];
            break;
          }
        }

        await DocumentField.create({
          document_id: document.id,
          signer_id: signerId,
          field_type: field.field_type,
          field_name: field.field_name,
          field_value: null, // Don't copy values
          is_required: field.is_required,
          page_number: field.page_number,
          x_position: field.x_position,
          y_position: field.y_position,
          width: field.width,
          height: field.height
        }, { transaction });
      }
    }

    // Create audit log entry
    await createAuditLog(
      document.id,
      req.user.id,
      null,
      'document_created_from_template',
      { 
        title: document.title,
        template_id: template.id,
        template_title: template.title
      },
      req.ip,
      req.headers['user-agent'],
      transaction
    );

    await transaction.commit();

    return res.status(201).json({
      message: 'Document created successfully from template',
      document
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating document from template:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Create default templates for a farm
 */
export const createDefaultTemplates = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { farmId } = req.params;

    // Validate farm access
    const hasAccess = await checkUserFarmAccess(req.user.id, farmId);
    if (!hasAccess && !req.user.is_global_admin) {
      await transaction.rollback();
      return res.status(403).json({ error: 'You do not have access to this farm' });
    }

    // Get tenant ID for the farm
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Define default templates
    const defaultTemplates = [
      {
        title: 'Farm Lease Agreement',
        description: 'Standard lease agreement for farm land',
        documentType: 'lease',
        fileName: 'farm_lease_template.pdf.txt',
        fields: [
          {
            type: 'signature',
            name: 'Landlord Signature',
            required: true,
            page: 1,
            x: 100,
            y: 500,
            width: 200,
            height: 50
          },
          {
            type: 'signature',
            name: 'Tenant Signature',
            required: true,
            page: 1,
            x: 400,
            y: 500,
            width: 200,
            height: 50
          },
          {
            type: 'date',
            name: 'Signing Date',
            required: true,
            page: 1,
            x: 250,
            y: 550,
            width: 150,
            height: 30
          }
        ]
      },
      {
        title: 'Equipment Rental Agreement',
        description: 'Agreement for renting farm equipment',
        documentType: 'agreement',
        fileName: 'equipment_rental_template.pdf.txt',
        fields: [
          {
            type: 'signature',
            name: 'Owner Signature',
            required: true,
            page: 1,
            x: 100,
            y: 500,
            width: 200,
            height: 50
          },
          {
            type: 'signature',
            name: 'Renter Signature',
            required: true,
            page: 1,
            x: 400,
            y: 500,
            width: 200,
            height: 50
          },
          {
            type: 'date',
            name: 'Rental Date',
            required: true,
            page: 1,
            x: 250,
            y: 550,
            width: 150,
            height: 30
          }
        ]
      },
      {
        title: 'Crop Purchase Contract',
        description: 'Contract for purchasing crops',
        documentType: 'contract',
        fileName: 'crop_purchase_template.pdf.txt',
        fields: [
          {
            type: 'signature',
            name: 'Seller Signature',
            required: true,
            page: 1,
            x: 100,
            y: 500,
            width: 200,
            height: 50
          },
          {
            type: 'signature',
            name: 'Buyer Signature',
            required: true,
            page: 1,
            x: 400,
            y: 500,
            width: 200,
            height: 50
          },
          {
            type: 'date',
            name: 'Contract Date',
            required: true,
            page: 1,
            x: 250,
            y: 550,
            width: 150,
            height: 30
          }
        ]
      }
    ];

    const createdTemplates = [];

    // Create each default template
    for (const templateData of defaultTemplates) {
      // Check if template file exists in the default templates directory
      const templateFilePath = path.join(__dirname, '..', 'templates', 'documents', templateData.fileName);

      if (!fs.existsSync(templateFilePath)) {
        console.warn(`Template file ${templateData.fileName} not found, skipping`);
        continue;
      }

      // Read template file
      const fileBuffer = await fs.promises.readFile(templateFilePath);

      // Generate storage path
      const storagePath = generateStoragePath(farmId, req.user.id, templateData.fileName);

      // Save file to disk
      const fullPath = await saveFile(fileBuffer, storagePath);

      // Get file stats
      const fileStats = await fs.promises.stat(templateFilePath);

      // Create template record
      const template = await SignableDocument.create({
        title: templateData.title,
        description: templateData.description,
        document_type: templateData.documentType,
        status: 'draft',
        file_path: storagePath,
        file_size: fileStats.size,
        file_type: 'txt',
        mime_type: 'text/plain',
        version: 1,
        is_template: true,
        template_id: null,
        tenant_id: farm.tenant_id,
        farm_id: farmId,
        created_by: req.user.id
      }, { transaction });

      // Create fields for the template
      for (const field of templateData.fields) {
        await DocumentField.create({
          document_id: template.id,
          signer_id: null,
          field_type: field.type,
          field_name: field.name,
          field_value: null,
          is_required: field.required,
          page_number: field.page,
          x_position: field.x,
          y_position: field.y,
          width: field.width,
          height: field.height
        }, { transaction });
      }

      // Create audit log entry
      await createAuditLog(
        template.id,
        req.user.id,
        null,
        'default_template_created',
        { title: template.title },
        req.ip,
        req.headers['user-agent'],
        transaction
      );

      createdTemplates.push(template);

      // Update storage usage
      await updateStorageUsage(farmId, fileStats.size, false, 1);
    }

    await transaction.commit();

    return res.status(201).json({
      message: `${createdTemplates.length} default templates created successfully`,
      templates: createdTemplates
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating default templates:', error);
    return res.status(500).json({ error: error.message });
  }
};
