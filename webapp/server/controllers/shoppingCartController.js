import { sequelize } from '../config/database.js';
import ShoppingCart from '../models/ShoppingCart.js';
import ShoppingCartItem from '../models/ShoppingCartItem.js';
import Product from '../models/Product.js';
import ProductImage from '../models/ProductImage.js';
import PurchaseRequest from '../models/PurchaseRequest.js';
import PurchaseRequestItem from '../models/PurchaseRequestItem.js';
import Farm from '../models/Farm.js';
import CustomerNotification from '../models/CustomerNotification.js';
import { Op } from 'sequelize';

// Helper function to get or create a cart
const getOrCreateCart = async (userId, sessionId, farmId) => {
  let cart;

  // If user is logged in, try to find their cart for this farm
  if (userId) {
    cart = await ShoppingCart.findOne({
      where: {
        user_id: userId,
        farm_id: farmId,
        is_saved: false
      }
    });
  } 
  // Otherwise, try to find cart by session ID
  else if (sessionId) {
    cart = await ShoppingCart.findOne({
      where: {
        session_id: sessionId,
        farm_id: farmId,
        is_saved: false
      }
    });
  }

  // If no cart exists, create a new one
  if (!cart) {
    cart = await ShoppingCart.create({
      user_id: userId || null,
      session_id: sessionId || null,
      farm_id: farmId,
      is_saved: false
    });
  }

  return cart;
};

// Add item to cart
export const addItemToCart = async (req, res) => {
  try {
    const { productId, quantity = 1, farmId } = req.body;

    if (!productId || !farmId) {
      return res.status(400).json({
        message: "Product ID and Farm ID are required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Verify product exists and is marketplace visible
    const product = await Product.findOne({
      where: {
        id: productId,
        farm_id: farmId,
        is_marketplace_visible: true,
        is_active: true
      }
    });

    if (!product) {
      return res.status(404).json({
        message: "Product not found or not available in marketplace",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get user ID from authenticated user or use session ID for guests
    const userId = req.user ? req.user.id : null;
    const sessionId = !userId ? (req.cookies.cart_session_id || `session_${Date.now()}`) : null;

    // If using session ID, set a cookie
    if (sessionId && !req.cookies.cart_session_id) {
      res.cookie('cart_session_id', sessionId, {
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
    }

    // Get or create cart
    const cart = await getOrCreateCart(userId, sessionId, farmId);

    // Check if item already exists in cart
    let cartItem = await ShoppingCartItem.findOne({
      where: {
        cart_id: cart.id,
        product_id: productId
      }
    });

    // If item exists, update quantity
    if (cartItem) {
      cartItem.quantity += parseInt(quantity);
      await cartItem.save();
    } 
    // Otherwise create new item
    else {
      cartItem = await ShoppingCartItem.create({
        cart_id: cart.id,
        product_id: productId,
        quantity: parseInt(quantity)
      });
    }

    // Return updated cart
    const updatedCart = await ShoppingCart.findByPk(cart.id, {
      include: [
        {
          model: ShoppingCartItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              include: [
                {
                  model: ProductImage,
                  as: 'images',
                  where: { is_primary: true },
                  required: false,
                  limit: 1
                }
              ]
            }
          ]
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name', 'logo_url', 'subdomain']
        }
      ]
    });

    res.status(200).json(updatedCart);
  } catch (error) {
    console.error('Error adding item to cart:', error);
    res.status(500).json({
      message: "Failed to add item to cart",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get current cart
export const getCurrentCart = async (req, res) => {
  try {
    const { farmId } = req.query;

    if (!farmId) {
      return res.status(400).json({
        message: "Farm ID is required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get user ID from authenticated user or use session ID for guests
    const userId = req.user ? req.user.id : null;
    const sessionId = req.cookies.cart_session_id;

    // If no user ID or session ID, return empty cart
    if (!userId && !sessionId) {
      return res.status(200).json({
        id: null,
        items: [],
        farm: null,
        itemCount: 0,
        totalPrice: 0
      });
    }

    // Find cart
    const whereClause = {
      farm_id: farmId,
      is_saved: false
    };

    if (userId) {
      whereClause.user_id = userId;
    } else {
      whereClause.session_id = sessionId;
    }

    const cart = await ShoppingCart.findOne({
      where: whereClause,
      include: [
        {
          model: ShoppingCartItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              include: [
                {
                  model: ProductImage,
                  as: 'images',
                  where: { is_primary: true },
                  required: false,
                  limit: 1
                }
              ]
            }
          ]
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name', 'logo_url', 'subdomain']
        }
      ]
    });

    // If no cart found, return empty cart
    if (!cart) {
      return res.status(200).json({
        id: null,
        items: [],
        farm: null,
        itemCount: 0,
        totalPrice: 0
      });
    }

    // Calculate totals
    let itemCount = 0;
    let totalPrice = 0;

    if (cart.items) {
      cart.items.forEach(item => {
        itemCount += item.quantity;
        totalPrice += item.quantity * (item.product ? item.product.price : 0);
      });
    }

    // Add calculated values to response
    const cartData = cart.toJSON();
    cartData.itemCount = itemCount;
    cartData.totalPrice = totalPrice;

    res.status(200).json(cartData);
  } catch (error) {
    console.error('Error fetching cart:', error);
    res.status(500).json({
      message: "Failed to fetch cart",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get all carts for a user
export const getAllCarts = async (req, res) => {
  try {
    // This endpoint requires authentication, so we should have a user
    const userId = req.user.id;

    if (!userId) {
      return res.status(401).json({
        message: "Authentication required",
        errorType: "UnauthorizedError",
        errorCode: "401",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Find all active (not saved) carts for this user
    const carts = await ShoppingCart.findAll({
      where: {
        user_id: userId,
        is_saved: false
      },
      include: [
        {
          model: ShoppingCartItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              include: [
                {
                  model: ProductImage,
                  as: 'images',
                  where: { is_primary: true },
                  required: false,
                  limit: 1
                }
              ]
            }
          ]
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name', 'logo_url', 'subdomain']
        }
      ]
    });

    // If no carts found, return empty array
    if (!carts || carts.length === 0) {
      return res.status(200).json([]);
    }

    // Calculate totals for each cart
    const cartsWithTotals = carts.map(cart => {
      let itemCount = 0;
      let totalPrice = 0;

      if (cart.items) {
        cart.items.forEach(item => {
          itemCount += item.quantity;
          totalPrice += item.quantity * (item.product ? item.product.price : 0);
        });
      }

      const cartData = cart.toJSON();
      cartData.itemCount = itemCount;
      cartData.totalPrice = totalPrice;

      return cartData;
    });

    res.status(200).json(cartsWithTotals);
  } catch (error) {
    console.error('Error fetching all carts:', error);
    res.status(500).json({
      message: "Failed to fetch carts",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Update cart item quantity
export const updateCartItem = async (req, res) => {
  try {
    const { itemId } = req.params;
    const { quantity } = req.body;

    if (!itemId || quantity === undefined) {
      return res.status(400).json({
        message: "Item ID and quantity are required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Find the cart item
    const cartItem = await ShoppingCartItem.findByPk(itemId);

    if (!cartItem) {
      return res.status(404).json({
        message: "Cart item not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get the cart to verify ownership
    const cart = await ShoppingCart.findByPk(cartItem.cart_id);

    // Verify user owns this cart
    const userId = req.user ? req.user.id : null;
    const sessionId = req.cookies.cart_session_id;

    if ((userId && cart.user_id !== userId) && 
        (!userId && sessionId && cart.session_id !== sessionId)) {
      return res.status(403).json({
        message: "You don't have permission to update this cart",
        errorType: "ForbiddenError",
        errorCode: "403",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Update quantity or remove if quantity is 0
    if (parseInt(quantity) <= 0) {
      await cartItem.destroy();
    } else {
      cartItem.quantity = parseInt(quantity);
      await cartItem.save();
    }

    // Return updated cart
    const updatedCart = await ShoppingCart.findByPk(cart.id, {
      include: [
        {
          model: ShoppingCartItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              include: [
                {
                  model: ProductImage,
                  as: 'images',
                  where: { is_primary: true },
                  required: false,
                  limit: 1
                }
              ]
            }
          ]
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name', 'logo_url', 'subdomain']
        }
      ]
    });

    // Calculate totals
    let itemCount = 0;
    let totalPrice = 0;

    if (updatedCart.items) {
      updatedCart.items.forEach(item => {
        itemCount += item.quantity;
        totalPrice += item.quantity * (item.product ? item.product.price : 0);
      });
    }

    // Add calculated values to response
    const cartData = updatedCart.toJSON();
    cartData.itemCount = itemCount;
    cartData.totalPrice = totalPrice;

    res.status(200).json(cartData);
  } catch (error) {
    console.error('Error updating cart item:', error);
    res.status(500).json({
      message: "Failed to update cart item",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Remove item from cart
export const removeCartItem = async (req, res) => {
  try {
    const { itemId } = req.params;

    if (!itemId) {
      return res.status(400).json({
        message: "Item ID is required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Find the cart item
    const cartItem = await ShoppingCartItem.findByPk(itemId);

    if (!cartItem) {
      return res.status(404).json({
        message: "Cart item not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get the cart to verify ownership
    const cart = await ShoppingCart.findByPk(cartItem.cart_id);

    // Verify user owns this cart
    const userId = req.user ? req.user.id : null;
    const sessionId = req.cookies.cart_session_id;

    if ((userId && cart.user_id !== userId) && 
        (!userId && sessionId && cart.session_id !== sessionId)) {
      return res.status(403).json({
        message: "You don't have permission to update this cart",
        errorType: "ForbiddenError",
        errorCode: "403",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Delete the cart item
    await cartItem.destroy();

    // Return updated cart
    const updatedCart = await ShoppingCart.findByPk(cart.id, {
      include: [
        {
          model: ShoppingCartItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              include: [
                {
                  model: ProductImage,
                  as: 'images',
                  where: { is_primary: true },
                  required: false,
                  limit: 1
                }
              ]
            }
          ]
        },
        {
          model: Farm,
          as: 'farm',
          attributes: ['id', 'name', 'logo_url', 'subdomain']
        }
      ]
    });

    // Calculate totals
    let itemCount = 0;
    let totalPrice = 0;

    if (updatedCart.items) {
      updatedCart.items.forEach(item => {
        itemCount += item.quantity;
        totalPrice += item.quantity * (item.product ? item.product.price : 0);
      });
    }

    // Add calculated values to response
    const cartData = updatedCart.toJSON();
    cartData.itemCount = itemCount;
    cartData.totalPrice = totalPrice;

    res.status(200).json(cartData);
  } catch (error) {
    console.error('Error removing cart item:', error);
    res.status(500).json({
      message: "Failed to remove cart item",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Save cart for later
export const saveCart = async (req, res) => {
  try {
    const { cartId } = req.params;

    if (!cartId) {
      return res.status(400).json({
        message: "Cart ID is required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Find the cart
    const cart = await ShoppingCart.findByPk(cartId);

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Verify user owns this cart
    const userId = req.user ? req.user.id : null;

    if (!userId) {
      return res.status(401).json({
        message: "You must be logged in to save a cart",
        errorType: "UnauthorizedError",
        errorCode: "401",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    if (cart.user_id !== userId) {
      return res.status(403).json({
        message: "You don't have permission to save this cart",
        errorType: "ForbiddenError",
        errorCode: "403",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Save the cart
    cart.is_saved = true;
    await cart.save();

    res.status(200).json({
      message: "Cart saved successfully",
      cart
    });
  } catch (error) {
    console.error('Error saving cart:', error);
    res.status(500).json({
      message: "Failed to save cart",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Submit purchase request (checkout)
export const checkout = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { cartId, notes } = req.body;

    if (!cartId) {
      return res.status(400).json({
        message: "Cart ID is required",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Find the cart with items
    const cart = await ShoppingCart.findByPk(cartId, {
      include: [
        {
          model: ShoppingCartItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            }
          ]
        }
      ],
      transaction
    });

    if (!cart) {
      await transaction.rollback();
      return res.status(404).json({
        message: "Cart not found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Verify cart has items
    if (!cart.items || cart.items.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        message: "Cart is empty",
        errorType: "ValidationError",
        errorCode: "400",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Verify user owns this cart
    const userId = req.user ? req.user.id : null;
    const sessionId = req.cookies.cart_session_id;

    if ((userId && cart.user_id !== userId) && 
        (!userId && sessionId && cart.session_id !== sessionId)) {
      await transaction.rollback();
      return res.status(403).json({
        message: "You don't have permission to checkout this cart",
        errorType: "ForbiddenError",
        errorCode: "403",
        context: {
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Create purchase request
    const purchaseRequest = await PurchaseRequest.create({
      user_id: userId,
      customer_id: null, // This would be set if we have customer info
      farm_id: cart.farm_id,
      status: 'pending',
      notes: notes || null
    }, { transaction });

    // Create purchase request items
    for (const item of cart.items) {
      await PurchaseRequestItem.create({
        request_id: purchaseRequest.id,
        product_id: item.product_id,
        quantity: item.quantity,
        price: item.product.price
      }, { transaction });
    }

    // Delete the cart and its items (cascade delete will handle items)
    await cart.destroy({ transaction });

    // Get farm information for notification
    const farm = await Farm.findByPk(cart.farm_id, { transaction });

    // Create notification for farm admin
    if (farm) {
      try {
        // Create a notification for the farm admin
        await CustomerNotification.create({
          title: 'New Purchase Request',
          message: `A new purchase request has been submitted for ${farm.name}.`,
          type: 'purchase_request',
          customer_id: null, // No specific customer for admin notification
          farm_id: farm.id,
          related_entity_type: 'purchase_request',
          related_entity_id: purchaseRequest.id,
          action_url: `/dashboard/purchase-requests/${purchaseRequest.id}`,
          read: false
        }, { transaction });

        // If the user is a customer, create a notification for them too
        if (purchaseRequest.customer_id) {
          await CustomerNotification.create({
            title: 'Purchase Request Submitted',
            message: `Your purchase request has been submitted to ${farm.name}.`,
            type: 'purchase_request',
            customer_id: purchaseRequest.customer_id,
            farm_id: farm.id,
            related_entity_type: 'purchase_request',
            related_entity_id: purchaseRequest.id,
            action_url: `/marketplace/orders/${purchaseRequest.id}`,
            read: false
          }, { transaction });
        }
      } catch (notificationError) {
        console.error('Error creating purchase request notification:', notificationError);
        // Continue with the checkout process even if notification creation fails
      }
    }

    // Commit transaction
    await transaction.commit();

    res.status(201).json({
      message: "Purchase request submitted successfully",
      purchaseRequest
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error checking out:', error);
    res.status(500).json({
      message: "Failed to checkout",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};
