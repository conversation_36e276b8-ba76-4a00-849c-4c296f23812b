import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Field from './Field.js';
import dotenv from 'dotenv';

dotenv.config();

const ConservationPractice = defineModel('ConservationPractice', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Field,
      key: 'id'
    }
  },
  practice_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Name of the conservation practice'
  },
  practice_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Type of conservation practice (e.g., soil, water, habitat)'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Description of the conservation practice'
  },
  implementation_date: {
    type: DataTypes.DATEONLY,
    allowNull: true,
    comment: 'Date when the practice was implemented'
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'active',
    comment: 'Status of the practice (active, planned, completed)'
  },
  benefits: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Benefits of the conservation practice'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'conservation_practices',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default ConservationPractice;