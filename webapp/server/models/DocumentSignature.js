import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import SignableDocument from './SignableDocument.js';
import DocumentSigner from './DocumentSigner.js';

const DocumentSignature = defineModel('DocumentSignature', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  document_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SignableDocument,
      key: 'id'
    }
  },
  signer_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: DocumentSigner,
      key: 'id'
    }
  },
  signature_type: {
    type: DataTypes.ENUM('drawn', 'typed', 'uploaded', 'click'),
    allowNull: false,
    defaultValue: 'drawn'
  },
  signature_image_path: {
    type: DataTypes.STRING(1024),
    allowNull: true
  },
  signature_text: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  signature_font: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  page_number: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  x_position: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  y_position: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  width: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  height: {
    type: DataTypes.FLOAT,
    allowNull: true
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'document_signatures',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'document_signatures_document_id_idx',
      fields: ['document_id']
    },
    {
      name: 'document_signatures_signer_id_idx',
      fields: ['signer_id']
    }
  ]
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default DocumentSignature;
