import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import SignableDocument from './SignableDocument.js';

const DocumentSigner = defineModel('DocumentSigner', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  document_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SignableDocument,
      key: 'id'
    }
  },
  signer_email: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  signer_name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  signer_role: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  signer_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  status: {
    type: DataTypes.ENUM('pending', 'sent', 'viewed', 'signed', 'declined'),
    allowNull: false,
    defaultValue: 'pending'
  },
  access_code: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  verification_method: {
    type: DataTypes.ENUM('email', 'sms', 'phone', 'none'),
    allowNull: false,
    defaultValue: 'email'
  },
  verification_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  viewed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  signed_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  declined_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  decline_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  reminder_sent_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'document_signers',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'document_signers_document_id_idx',
      fields: ['document_id']
    },
    {
      name: 'document_signers_signer_email_idx',
      fields: ['signer_email']
    },
    {
      name: 'document_signers_status_idx',
      fields: ['status']
    },
    {
      name: 'document_signers_signer_order_idx',
      fields: ['signer_order']
    }
  ]
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default DocumentSigner;
