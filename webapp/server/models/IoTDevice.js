import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const IoTDevice = defineModel('IoTDevice', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  device_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Type of IoT device (e.g., sensor, tracker, controller)'
  },
  manufacturer: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  model: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  serial_number: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  firmware_version: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  location_description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Description of where the device is located'
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'active',
    comment: 'Status of the device (active, inactive, maintenance, etc.)'
  },
  last_communication: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Timestamp of the last communication with the device'
  },
  battery_level: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Battery level in percentage (0-100)'
  },
  signal_strength: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Signal strength in percentage (0-100)'
  },
  configuration: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Device-specific configuration settings'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'iot_devices',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default IoTDevice;