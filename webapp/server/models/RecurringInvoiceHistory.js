import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import RecurringInvoice from './RecurringInvoice.js';
import Invoice from './Invoice.js';
import dotenv from 'dotenv';

dotenv.config();

const RecurringInvoiceHistory = defineModel('RecurringInvoiceHistory', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  recurring_invoice_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: RecurringInvoice,
      key: 'id'
    }
  },
  generated_invoice_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Invoice,
      key: 'id'
    }
  },
  generation_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'recurring_invoice_history',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Note: Associations are defined in associations.js to avoid duplicates

export default RecurringInvoiceHistory;