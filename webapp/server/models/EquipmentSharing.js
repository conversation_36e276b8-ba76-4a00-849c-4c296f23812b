import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Equipment from './Equipment.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const EquipmentSharing = defineModel('EquipmentSharing', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  equipment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Equipment,
      key: 'id'
    }
  },
  owner_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  renter_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  start_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  end_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  rental_cost: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  rental_cost_type: {
    type: DataTypes.ENUM('flat', 'daily', 'hourly', 'per_acre'),
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'active', 'completed', 'cancelled', 'rejected'),
    defaultValue: 'pending',
    allowNull: false
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'equipment_sharing',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default EquipmentSharing;
