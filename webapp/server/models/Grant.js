import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const Grant = defineModel('Grant', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  external_id: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'ID from the external grants API'
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  agency: {
    type: DataTypes.STRING,
    allowNull: true
  },
  opportunity_number: {
    type: DataTypes.STRING,
    allowNull: true
  },
  category: {
    type: DataTypes.STRING,
    allowNull: true
  },
  eligibility: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  funding_amount: {
    type: DataTypes.STRING,
    allowNull: true
  },
  close_date: {
    type: DataTypes.DATE,
    allowNull: true
  },
  url: {
    type: DataTypes.STRING,
    allowNull: true
  },
  source: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Source of the grant data (e.g., grants.gov, farmers.gov, usda)'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'grants',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default Grant;