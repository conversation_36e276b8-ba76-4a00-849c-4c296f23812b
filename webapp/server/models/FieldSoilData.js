import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Field from './Field.js';
import dotenv from 'dotenv';

dotenv.config();

const FieldSoilData = defineModel('FieldSoilData', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Field,
      key: 'id'
    }
  },
  soil_type: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Type of soil (e.g., Loam, Clay, Sandy)'
  },
  soil_health_index: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Soil health index (0-100)'
  },
  land_capability_class: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Land capability classification (e.g., Class I, Class II)'
  },
  erosion_risk: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Erosion risk level (Low, Medium, High)'
  },
  water_availability: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Water availability level (Low, Medium, High)'
  },
  last_updated: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: 'Date when the soil data was last updated'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'field_soil_data',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default FieldSoilData;