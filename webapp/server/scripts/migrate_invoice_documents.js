import { sequelize } from '../config/database.js';
import Invoice from '../models/Invoice.js';
import { downloadFromSpaces, uploadToSpaces, deleteFromSpaces, createFolderInSpaces } from '../utils/spacesUtils.js';
import path from 'path';

/**
 * Migrates invoice documents from the old path structure (invoice-documents/[farm]/[customer])
 * to the new path structure (farms/[farmId]/invoices)
 */
async function migrateInvoiceDocuments() {
  console.log('Starting migration of invoice documents...');
  
  try {
    // Find all invoices that have a document_storage_path
    const invoices = await Invoice.findAll({
      where: {
        document_storage_path: {
          [sequelize.Op.not]: null
        }
      }
    });

    console.log(`Found ${invoices.length} invoices with documents to migrate`);

    // Process each invoice
    for (const invoice of invoices) {
      try {
        const oldPath = invoice.document_storage_path;
        
        // Skip if the path already starts with 'farms/'
        if (oldPath.startsWith('farms/')) {
          console.log(`Invoice ${invoice.id} already uses the new path structure: ${oldPath}`);
          continue;
        }

        console.log(`Migrating document for invoice ${invoice.id} from ${oldPath}`);

        // Create the new path: farms/[farmId]/invoices/[filename]
        const fileName = path.basename(oldPath);
        const newPath = path.join('farms', invoice.farm_id, 'invoices', fileName).replace(/\\/g, '/');

        // Ensure the folder exists
        const folderPath = path.join('farms', invoice.farm_id, 'invoices').replace(/\\/g, '/');
        await createFolderInSpaces(folderPath);

        // Download the file from the old location
        const fileBuffer = await downloadFromSpaces(oldPath);
        
        // Upload the file to the new location
        await uploadToSpaces(fileBuffer, newPath);
        
        // Update the invoice record with the new path
        await invoice.update({
          document_storage_path: newPath
        });
        
        // Delete the file from the old location
        await deleteFromSpaces(oldPath);
        
        console.log(`Successfully migrated document for invoice ${invoice.id} to ${newPath}`);
      } catch (error) {
        console.error(`Error migrating document for invoice ${invoice.id}:`, error);
        // Continue with the next invoice even if this one fails
      }
    }

    console.log('Invoice document migration completed successfully');
  } catch (error) {
    console.error('Error during invoice document migration:', error);
  }
}

// Run the migration
migrateInvoiceDocuments()
  .then(() => {
    console.log('Migration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });