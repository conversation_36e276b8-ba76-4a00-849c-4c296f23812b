import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { sequelize } from '../config/database.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration() {
  try {
    console.log('Starting migration: Adding ON DELETE CASCADE to role_permissions.farm_id foreign key...');

    // Read the SQL file
    const migrationFile = 'add_cascade_delete_to_role_permissions.sql';
    const sqlPath = path.join(__dirname, '..', 'db', migrationFile);
    
    if (!fs.existsSync(sqlPath)) {
      throw new Error(`Migration file not found: ${sqlPath}`);
    }
    
    let sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Replace schema placeholder if needed
    const schema = process.env.DB_SCHEMA || 'site';
    sql = sql.replace(/SET search_path TO site;/g, `SET search_path TO ${schema};`);
    
    // Execute the SQL
    await sequelize.query(sql);
    
    // Verify the constraint was added
    const [results] = await sequelize.query(`
      SELECT 1
      FROM information_schema.table_constraints
      WHERE constraint_name = 'role_permissions_farm_id_fkey'
      AND table_schema = '${schema}'
      AND table_name = 'role_permissions'
    `);
    
    if (results.length > 0) {
      console.log('ON DELETE CASCADE successfully added to role_permissions.farm_id foreign key constraint.');
      console.log('Now when a farm is deleted, all associated role permissions will be automatically deleted.');
    } else {
      throw new Error('Failed to add ON DELETE CASCADE to role_permissions.farm_id foreign key constraint.');
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the migration
runMigration();