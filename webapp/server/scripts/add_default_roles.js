import { sequelize } from '../config/database.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function addDefaultRoles() {
  try {
    console.log('Starting migration: Adding default roles to the database...');

    // Define the migration file
    const migrationFile = 'add_default_roles.sql';

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../db', migrationFile);
    let sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Replace the schema variable with the actual schema name
    const schema = process.env.DB_SCHEMA || 'site';
    sql = sql.replace('${DB_SCHEMA:=public}', schema);

    console.log(`Using database schema: ${schema}`);

    // Execute the SQL
    await sequelize.query(sql);

    console.log(`Migration ${migrationFile} completed successfully`);
    console.log('Default role permissions have been added to the database');

    // Verify the roles were added
    const [results] = await sequelize.query('SELECT role_name, feature, COUNT(*) FROM role_permissions WHERE farm_id IS NULL GROUP BY role_name, feature ORDER BY role_name, feature');

    console.log('\nVerification of added roles:');
    console.log('----------------------------');

    let currentRole = '';
    for (const row of results) {
      if (row.role_name !== currentRole) {
        currentRole = row.role_name;
        console.log(`\nRole: ${currentRole}`);
      }
      console.log(`  - Feature: ${row.feature}, Count: ${row.count}`);
    }

  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

// Run the function
addDefaultRoles()
  .then(() => {
    console.log('\nMigration completed, exiting...');
    process.exit(0);
  })
  .catch(err => {
    console.error('Unhandled error:', err);
    process.exit(1);
  });
