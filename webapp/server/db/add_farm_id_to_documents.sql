-- Check if document_folders table exists before trying to alter it

-- Set the search path to the appropriate schema
SET search_path TO site;
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = current_schema() 
               AND table_name = 'document_folders') THEN

        -- Add farm_id column to document_folders table if it doesn't exist
        ALTER TABLE site.document_folders ADD COLUMN IF NOT EXISTS farm_id UUID REFERENCES site.farms(id);

        -- Copy data from tenant_id to farm_id in document_folders if both columns exist
        IF EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'site'
                  AND table_name = 'document_folders' 
                  AND column_name = 'tenant_id') THEN

            -- Update farm_id with values from tenant_id
            -- This assumes there's a relationship between tenants and farms
            -- Adjust the join condition as needed for your data model
            UPDATE site.document_folders df
            SET farm_id = f.id
            FROM site.farms f
            WHERE df.tenant_id = f.tenant_id;

        END IF;

        -- Add NOT NULL constraint to farm_id in document_folders
        -- Only do this after data migration to avoid constraint violations
        ALTER TABLE site.document_folders ALTER COLUMN farm_id SET NOT NULL;

        -- Create index for better query performance
        CREATE INDEX IF NOT EXISTS idx_document_folders_farm_id ON site.document_folders(farm_id);

        -- Add comment explaining the purpose of this column
        COMMENT ON COLUMN site.document_folders.farm_id IS 'Reference to the farm this document folder belongs to';
    ELSE
        RAISE NOTICE 'document_folders table does not exist, skipping modifications';
    END IF;
END $$;

-- Check if documents table exists before trying to alter it
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = current_schema() 
               AND table_name = 'documents') THEN

        -- Add farm_id column to documents table if it doesn't exist
        ALTER TABLE site.documents ADD COLUMN IF NOT EXISTS farm_id UUID REFERENCES site.farms(id);

        -- Make farm_id NOT NULL in documents table
        -- First, update any NULL farm_id values
        IF EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_schema = 'site'
                  AND table_name = 'documents' 
                  AND column_name = 'tenant_id') THEN

            -- Update farm_id with values from tenant_id where farm_id is NULL
            UPDATE site.documents d
            SET farm_id = f.id
            FROM site.farms f
            WHERE d.tenant_id = f.tenant_id
            AND d.farm_id IS NULL;

        END IF;

        -- Now add the NOT NULL constraint
        ALTER TABLE site.documents ALTER COLUMN farm_id SET NOT NULL;

        -- Create index for better query performance
        CREATE INDEX IF NOT EXISTS idx_documents_farm_id ON site.documents(farm_id);

        -- Add comment explaining the purpose of this column
        COMMENT ON COLUMN site.documents.farm_id IS 'Reference to the farm this document belongs to';
    ELSE
        RAISE NOTICE 'documents table does not exist, skipping modifications';
    END IF;
END $$;
