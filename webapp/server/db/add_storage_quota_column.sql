-- Add storage_quota_gb column to subscription_plans table

-- Set the search path to the appropriate schema
SET search_path TO site;
ALTER TABLE site.subscription_plans ADD COLUMN IF NOT EXISTS storage_quota_gb INTEGER NOT NULL DEFAULT 5;

-- Add max_file_size_mb column to subscription_plans table if it doesn't exist
ALTER TABLE site.subscription_plans ADD COLUMN IF NOT EXISTS max_file_size_mb INTEGER NOT NULL DEFAULT 25;

-- Update existing subscription plans with storage quotas
UPDATE site.subscription_plans SET storage_quota_gb = 5, max_file_size_mb = 25 WHERE name = 'Basic';
UPDATE site.subscription_plans SET storage_quota_gb = 20, max_file_size_mb = 50 WHERE name = 'Standard';
UPDATE site.subscription_plans SET storage_quota_gb = 100, max_file_size_mb = 100 WHERE name = 'Premium';
UPDATE site.subscription_plans SET storage_quota_gb = 1000, max_file_size_mb = 500 WHERE name = 'Enterprise';
