-- Set the search path to the appropriate schema
SET search_path TO site;

-- Create sessions table to track user login sessions
CREATE TABLE IF NOT EXISTS site.sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES site.users(id) ON DELETE CASCADE,
  token VARCHAR(255) NOT NULL UNIQUE,
  ip_address VARCHAR(45),
  user_agent TEXT,
  device_type VARCHAR(50),
  browser VARCHAR(100),
  operating_system VARCHAR(100),
  location VARCHAR(255),
  last_active_at TIMESTAMP NOT NULL DEFAULT NOW(),
  expires_at TIMESTAMP NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Add index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS sessions_user_id_idx ON site.sessions(user_id);

-- Add index on token for faster lookups
CREATE INDEX IF NOT EXISTS sessions_token_idx ON site.sessions(token);

-- Add index on is_active for filtering active sessions
CREATE INDEX IF NOT EXISTS sessions_is_active_idx ON site.sessions(is_active);

-- Add comment to the table
COMMENT ON TABLE site.sessions IS 'Stores user login sessions with device and location information';