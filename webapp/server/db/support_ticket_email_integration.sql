-- Set the search path to the appropriate schema
SET search_path TO site;

-- Add email-related fields to support_tickets table
ALTER TABLE site.support_tickets
ADD COLUMN IF NOT EXISTS email_thread_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS last_email_sent_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS email_notifications_enabled BOOLEAN DEFAULT TRUE;

-- Add email-related fields to support_ticket_comments table
ALTER TABLE site.support_ticket_comments
ADD COLUMN IF NOT EXISTS is_from_email BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS email_source VARCHAR(255),
ADD COLUMN IF NOT EXISTS email_message_id VARCHAR(255);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_support_tickets_email_thread_id ON site.support_tickets(email_thread_id);
CREATE INDEX IF NOT EXISTS idx_support_ticket_comments_is_from_email ON site.support_ticket_comments(is_from_email);

-- Add comments to explain the fields
COMMENT ON COLUMN site.support_tickets.email_thread_id IS 'Unique ID for tracking email thread conversations';
COMMENT ON COLUMN site.support_tickets.last_email_sent_at IS 'Timestamp of the last email sent for this ticket';
COMMENT ON COLUMN site.support_tickets.email_notifications_enabled IS 'Whether email notifications are enabled for this ticket';
COMMENT ON COLUMN site.support_ticket_comments.is_from_email IS 'Whether the comment was received via email';
COMMENT ON COLUMN site.support_ticket_comments.email_source IS 'Email address that sent this comment (if from email)';
COMMENT ON COLUMN site.support_ticket_comments.email_message_id IS 'Message ID from the email headers';