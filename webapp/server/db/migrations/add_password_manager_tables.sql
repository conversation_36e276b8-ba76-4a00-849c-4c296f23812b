-- Migration: Add password manager tables
-- This migration adds tables for the password manager system, including password groups, passwords, permissions, and recovery keys

SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
  RAISE NOTICE 'Migration step: %', step_name;
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- Log the start of the migration
DO $$
BEGIN
  PERFORM log_migration_step('Starting add_password_manager_tables migration');
END $$;

-- Create password_groups table
CREATE TABLE IF NOT EXISTS password_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID REFERENCES farms(id) ON DELETE CASCADE,
  name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
  description VARCHAR(255),
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to password_groups columns
COMMENT ON TABLE password_groups IS 'Groups for organizing passwords';
COMMENT ON COLUMN password_groups.id IS 'Unique identifier for the password group';
COMMENT ON COLUMN password_groups.farm_id IS 'Reference to the farm this password group belongs to';
COMMENT ON COLUMN password_groups.name IS 'Name of the password group';
COMMENT ON COLUMN password_groups.description IS 'Description of the password group';
COMMENT ON COLUMN password_groups.created_by IS 'User who created the password group';
COMMENT ON COLUMN password_groups.created_at IS 'Timestamp when the password group was created';
COMMENT ON COLUMN password_groups.updated_at IS 'Timestamp when the password group was last updated';

-- Create index on farm_id for password_groups
CREATE INDEX IF NOT EXISTS idx_password_groups_farm_id ON password_groups(farm_id);

DO $$
BEGIN
  PERFORM log_migration_step('Created password_groups table');
END $$;

-- Create passwords table
CREATE TABLE IF NOT EXISTS passwords (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID REFERENCES password_groups(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  username VARCHAR(255),
  password VARCHAR(1024),
  url VARCHAR(1024),
  notes TEXT,
  has_2fa BOOLEAN DEFAULT FALSE,
  totp_secret VARCHAR(255),
  encryption_key_id VARCHAR(255),
  encryption_iv VARCHAR(255),
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to passwords columns
COMMENT ON TABLE passwords IS 'Encrypted password entries';
COMMENT ON COLUMN passwords.id IS 'Unique identifier for the password entry';
COMMENT ON COLUMN passwords.group_id IS 'Reference to the password group this password belongs to';
COMMENT ON COLUMN passwords.name IS 'Name/title of the password entry';
COMMENT ON COLUMN passwords.username IS 'Encrypted username for the password entry';
COMMENT ON COLUMN passwords.password IS 'Encrypted password for the entry';
COMMENT ON COLUMN passwords.url IS 'Encrypted URL associated with the password entry';
COMMENT ON COLUMN passwords.notes IS 'Encrypted notes for the password entry';
COMMENT ON COLUMN passwords.has_2fa IS 'Whether this password entry has 2FA enabled';
COMMENT ON COLUMN passwords.totp_secret IS 'Encrypted TOTP secret for 2FA';
COMMENT ON COLUMN passwords.encryption_key_id IS 'Identifier for the encryption key used';
COMMENT ON COLUMN passwords.encryption_iv IS 'Initialization vector used for encryption';
COMMENT ON COLUMN passwords.created_by IS 'User who created the password entry';
COMMENT ON COLUMN passwords.created_at IS 'Timestamp when the password entry was created';
COMMENT ON COLUMN passwords.updated_at IS 'Timestamp when the password entry was last updated';

-- Create index on group_id for passwords
CREATE INDEX IF NOT EXISTS idx_passwords_group_id ON passwords(group_id);

DO $$
BEGIN
  PERFORM log_migration_step('Created passwords table');
END $$;

-- Create password_group_permissions table
CREATE TABLE IF NOT EXISTS password_group_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID REFERENCES password_groups(id) ON DELETE CASCADE,
  role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  permission_type VARCHAR(20) NOT NULL CHECK (permission_type IN ('view', 'edit', 'manage')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT check_role_or_user_not_null CHECK (
    (role_id IS NOT NULL AND user_id IS NULL) OR
    (role_id IS NULL AND user_id IS NOT NULL)
  )
);

-- Add comments to password_group_permissions columns
COMMENT ON TABLE password_group_permissions IS 'Permissions for password groups';
COMMENT ON COLUMN password_group_permissions.id IS 'Unique identifier for the permission';
COMMENT ON COLUMN password_group_permissions.group_id IS 'Reference to the password group';
COMMENT ON COLUMN password_group_permissions.role_id IS 'Reference to the role (if permission is role-based)';
COMMENT ON COLUMN password_group_permissions.user_id IS 'Reference to the user (if permission is user-specific)';
COMMENT ON COLUMN password_group_permissions.permission_type IS 'Type of permission: view, edit, or manage';
COMMENT ON COLUMN password_group_permissions.created_at IS 'Timestamp when the permission was created';
COMMENT ON COLUMN password_group_permissions.updated_at IS 'Timestamp when the permission was last updated';

-- Create indexes for password_group_permissions
CREATE INDEX IF NOT EXISTS idx_password_group_permissions_group_id ON password_group_permissions(group_id);
CREATE INDEX IF NOT EXISTS idx_password_group_permissions_role_id ON password_group_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_password_group_permissions_user_id ON password_group_permissions(user_id);

DO $$
BEGIN
  PERFORM log_migration_step('Created password_group_permissions table');
END $$;

-- Create user_recovery_keys table
CREATE TABLE IF NOT EXISTS user_recovery_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  encrypted_recovery_key VARCHAR(1024) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to user_recovery_keys columns
COMMENT ON TABLE user_recovery_keys IS 'Recovery keys for user password manager access';
COMMENT ON COLUMN user_recovery_keys.id IS 'Unique identifier for the recovery key';
COMMENT ON COLUMN user_recovery_keys.user_id IS 'Reference to the user this recovery key belongs to';
COMMENT ON COLUMN user_recovery_keys.encrypted_recovery_key IS 'Encrypted recovery key for the user';
COMMENT ON COLUMN user_recovery_keys.created_at IS 'Timestamp when the recovery key was created';
COMMENT ON COLUMN user_recovery_keys.updated_at IS 'Timestamp when the recovery key was last updated';

-- Create unique index on user_id for user_recovery_keys (one recovery key per user)
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_recovery_keys_user_id ON user_recovery_keys(user_id);

DO $$
BEGIN
  PERFORM log_migration_step('Created user_recovery_keys table');
END $$;

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
    INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status)
    VALUES (
      gen_random_uuid(),
      'Add password manager tables',
      'webapp/server/db/migrations/add_password_manager_tables.sql',
      (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
      NOW(),
      NOW(),
      NOW(),
      'completed'
    );

    PERFORM log_migration_step('Migration recorded in database_migrations table');
  ELSE
    PERFORM log_migration_step('database_migrations table does not exist, skipping recording');
  END IF;
END $$;

-- Log the completion of the migration
DO $$
BEGIN
  PERFORM log_migration_step('Completed add_password_manager_tables migration');
END $$;

-- Drop the logging function
DROP FUNCTION IF EXISTS log_migration_step;
