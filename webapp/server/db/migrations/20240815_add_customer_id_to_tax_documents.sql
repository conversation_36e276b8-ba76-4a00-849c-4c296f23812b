-- Migration: Add customer_id to tax_documents table for customer tax form uploads
-- Depends on: add_tax_system_tables.sql

SET search_path TO site;

-- Add customer_id column to tax_documents table
ALTER TABLE tax_documents
ADD COLUMN customer_id UUID NULL,
ADD CONSTRAINT fk_tax_documents_customer
FOREIGN KEY (customer_id)
REFERENCES customers(id)
ON DELETE CASCADE;

-- Add index for customer_id
CREATE INDEX tax_documents_customer_id_idx ON tax_documents(customer_id);

-- Add new document_type option for customer tax forms
ALTER TYPE tax_document_type ADD VALUE IF NOT EXISTS 'customer_tax_form';

-- Update the document_type enum to include customer tax forms if not already present
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tax_document_type') THEN
        CREATE TYPE tax_document_type AS ENUM ('w2', '1099', 'schedule_f', 'depreciation', 'expense', 'income', 'other', 'customer_tax_form');
    ELSE
        BEGIN
            ALTER TYPE tax_document_type ADD VALUE 'customer_tax_form';
        EXCEPTION
            WHEN duplicate_object THEN
                -- Type value already exists, do nothing
        END;
    END IF;
END$$;