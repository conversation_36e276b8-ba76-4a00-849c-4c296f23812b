-- Migration: Add recurring_invoice_history table for tracking generated recurring invoices
-- Depends on: 20240801_add_recurring_invoices_table.sql

SET search_path TO site;

-- Create the recurring_invoice_history table
CREATE TABLE IF NOT EXISTS recurring_invoice_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  recurring_invoice_id UUID NOT NULL,
  generated_invoice_id UUID NOT NULL,
  generation_date DATE NOT NULL,
  status VARCHAR(50) NOT NULL, -- success, error
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  FOREIGN KEY (recurring_invoice_id) REFERENCES recurring_invoices(id) ON DELETE CASCADE,
  FOREIGN KEY (generated_invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);

-- Add indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_recurring_invoice_history_recurring_invoice_id ON recurring_invoice_history(recurring_invoice_id);
CREATE INDEX IF NOT EXISTS idx_recurring_invoice_history_generated_invoice_id ON recurring_invoice_history(generated_invoice_id);
CREATE INDEX IF NOT EXISTS idx_recurring_invoice_history_generation_date ON recurring_invoice_history(generation_date);

-- Add comment to explain the table
COMMENT ON TABLE recurring_invoice_history IS 'Tracks the history of invoices generated from recurring invoice templates';
COMMENT ON COLUMN recurring_invoice_history.recurring_invoice_id IS 'The recurring invoice configuration that generated this invoice';
COMMENT ON COLUMN recurring_invoice_history.generated_invoice_id IS 'The actual invoice that was generated';
COMMENT ON COLUMN recurring_invoice_history.generation_date IS 'The date when the invoice was generated';
COMMENT ON COLUMN recurring_invoice_history.status IS 'Status of the generation process (success, error)';
COMMENT ON COLUMN recurring_invoice_history.error_message IS 'Error message if the generation process failed';