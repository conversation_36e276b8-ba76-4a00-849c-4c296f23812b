-- Migration: Add tracking fields to invoice_emails table
-- Depends on:

SET search_path TO site;

-- Add tracking_id and viewed_at columns to invoice_emails table
ALTER TABLE invoice_emails
ADD COLUMN IF NOT EXISTS tracking_id UUID DEFAULT uuid_generate_v4(),
ADD COLUMN IF NOT EXISTS viewed_at TIMESTAMP WITH TIME ZONE;

-- Add index on tracking_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_invoice_emails_tracking_id ON invoice_emails(tracking_id);

-- Add comments
COMMENT ON COLUMN invoice_emails.tracking_id IS 'Unique ID used for tracking when an invoice email is viewed';
COMMENT ON COLUMN invoice_emails.viewed_at IS 'Timestamp when the invoice email was viewed by the recipient';