-- Update user_farms table to work with the new roles table

-- Set the search path to the site schema
SET search_path TO site;

-- Add role_id column to user_farms table if it doesn't exist
ALTER TABLE site.user_farms ADD COLUMN IF NOT EXISTS role_id UUID REFERENCES site.roles(id);
COMMENT ON COLUMN site.user_farms.role_id IS 'Reference to the role';

-- Modify role column to be nullable and mark as deprecated
ALTER TABLE site.user_farms ALTER COLUMN role DROP NOT NULL;
COMMENT ON COLUMN site.user_farms.role IS 'Legacy role field (deprecated, use role_id instead)';

-- Verify the column was added
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'user_farms' 
        AND column_name = 'role_id'
    ) INTO column_exists;

    IF column_exists THEN
        RAISE NOTICE 'The role_id column was successfully added to the user_farms table.';
    ELSE
        RAISE EXCEPTION 'Failed to add the role_id column to the user_farms table.';
    END IF;
END $$;

-- Migrate existing user_farms to use role_id
DO $$
DECLARE
    user_farm_record RECORD;
    found_role_id UUID;
    role_name VARCHAR;
BEGIN
    FOR user_farm_record IN SELECT * FROM site.user_farms WHERE role_id IS NULL AND role IS NOT NULL LOOP
        -- Map the enum value to a role name
        CASE user_farm_record.role
            WHEN 'farm_owner' THEN role_name := 'Farm Owner';
            WHEN 'farm_admin' THEN role_name := 'Farm Admin';
            WHEN 'farm_manager' THEN role_name := 'Farm Manager';
            WHEN 'farm_employee' THEN role_name := 'Farm Employee';
            WHEN 'accountant' THEN role_name := 'Accountant';
            ELSE role_name := user_farm_record.role::VARCHAR;
        END CASE;

        -- Find the corresponding role
        SELECT id INTO found_role_id FROM site.roles 
        WHERE name = role_name 
        AND roles.farm_id = user_farm_record.farm_id;

        IF found_role_id IS NOT NULL THEN
            -- Update the user_farm with the role_id
            UPDATE site.user_farms 
            SET role_id = found_role_id 
            WHERE user_farms.user_id = user_farm_record.user_id AND user_farms.farm_id = user_farm_record.farm_id;

            RAISE NOTICE 'Updated user_farm for user % and farm % with role_id %', 
                user_farm_record.user_id, user_farm_record.farm_id, found_role_id;
        ELSE
            -- If no matching role found, create one
            INSERT INTO site.roles (id, farm_id, name, description, is_system_role)
            VALUES (gen_random_uuid(), user_farm_record.farm_id, role_name, 'Migrated from legacy role', TRUE)
            RETURNING id INTO found_role_id;

            -- Update the user_farm with the new role_id
            UPDATE site.user_farms 
            SET role_id = found_role_id 
            WHERE user_farms.user_id = user_farm_record.user_id AND user_farms.farm_id = user_farm_record.farm_id;

            RAISE NOTICE 'Created new role % for user_farm (user %, farm %)', 
                found_role_id, user_farm_record.user_id, user_farm_record.farm_id;
        END IF;
    END LOOP;
END $$;

-- Add NOT NULL constraint to role_id after migration
DO $$
BEGIN
    -- Check if all user_farms have a role_id
    IF EXISTS (SELECT 1 FROM site.user_farms WHERE role_id IS NULL) THEN
        RAISE EXCEPTION 'Cannot add NOT NULL constraint to role_id because some user_farms have NULL role_id';
    ELSE
        -- Add NOT NULL constraint
        ALTER TABLE site.user_farms ALTER COLUMN role_id SET NOT NULL;
        RAISE NOTICE 'Added NOT NULL constraint to role_id column in user_farms table.';
    END IF;
END $$;
