-- Migration: Add expert_context field to ai_instructions table
-- Depends on:

-- Set the search path to the site schema
SET search_path TO site;

-- Begin transaction
BEGIN;

-- Add expert_context column to ai_instructions table
ALTER TABLE site.ai_instructions
ADD COLUMN expert_context TEXT;

-- Add comment to the new column
COMMENT ON COLUMN site.ai_instructions.expert_context IS 'The expert context to use for this instruction (e.g., agronomist, vet, financial expert)';

-- Update existing instructions with appropriate expert contexts
UPDATE site.ai_instructions
SET expert_context = 'You are an expert agronomist with deep knowledge of farming practices, and crop management, soil health, pest management, chemical management, yield optimization, seed selection, and equipment management.'
WHERE task_type IN ('crop_rotation', 'harvest_scheduling', 'soil_health', 'field_health');

UPDATE site.ai_instructions
SET expert_context = 'You are an expert veterinarian with deep knowledge of animal health, nutrition, and care.'
WHERE task_type IN ('herd_health');

-- Insert financial instructions with financial expert context
INSERT INTO site.ai_instructions (name, description, task_type, instructions, expert_context, is_enabled)
VALUES
  (
    'Financial Analysis',
    'Instructions for analyzing farm financial data and providing insights.',
    'financial_analysis',
    'Analyze the provided financial data to assess farm financial health and recommend improvements. Consider income, expenses, cash flow, and profitability in your analysis. Provide specific recommendations for improving financial performance.',
    'You are an expert tax analyst and financial expert with deep knowledge of agricultural finance, tax regulations, and financial planning for farms.',
    TRUE
  ),
  (
    'Tax Planning',
    'Instructions for analyzing tax data and providing tax planning advice.',
    'tax_planning',
    'Analyze the provided tax data to identify tax planning opportunities and recommend strategies. Consider income, expenses, deductions, and credits in your analysis. Provide specific recommendations for minimizing tax liability while remaining compliant with tax regulations.',
    'You are an expert tax analyst and financial expert with deep knowledge of agricultural taxation, tax regulations, and tax planning strategies for farms.',
    TRUE
  ),
  (
    'General Farming',
    'Instructions for providing general farming advice.',
    'general_farming',
    'Provide helpful, accurate, and practical advice on farming practices, crop management, equipment maintenance, weather impacts, market trends, and other agricultural topics. Keep your responses concise, practical, and focused on providing actionable advice.',
    'You are an expert agronomist and veterinarian with deep knowledge of farming practices, crop management, and animal health.',
    TRUE
  ),
  (
    'Document Generation',
    'Instructions for generating farm-related documents.',
    'document_generation',
    'Generate professional, legally-sound documents for farmers. The document should be well-structured, professionally written, and include all necessary sections. Include appropriate legal language, terms, and conditions that would typically be found in this type of document.',
    'You are an expert agronomist and veterinarian with deep knowledge of farming practices, crop management, and animal health. You also have expertise in creating professional farm-related documents.',
    TRUE
  ),
  (
    'Financial Document',
    'Instructions for generating financial or legal documents.',
    'financial_document',
    'Generate professional, legally-sound financial or legal documents for farmers. The document should be well-structured, professionally written, and include all necessary sections. Include appropriate legal and financial language, terms, and conditions that would typically be found in this type of document.',
    'You are an expert tax analyst and financial expert with deep knowledge of agricultural finance, tax regulations, and financial planning for farms. You also have expertise in creating professional financial and legal documents.',
    TRUE
  ),
  (
    'Document Title',
    'Instructions for generating document titles.',
    'document_title',
    'Generate a concise, professional title for the document. Respond with only the title, no additional text.',
    'You are an expert at creating concise, professional document titles.',
    TRUE
  );

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at)
        VALUES (
            gen_random_uuid(), 
            'add_expert_context_to_ai_instructions', 
            'webapp/server/db/migrations/add_expert_context_to_ai_instructions.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );
    END IF;
END $$;

-- Commit transaction
COMMIT;

-- Verify the column was added
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'ai_instructions' 
        AND column_name = 'expert_context'
    ) THEN
        RAISE EXCEPTION 'Migration failed. The expert_context column was not added to the ai_instructions table.';
    ELSE
        RAISE NOTICE 'Migration successful. The expert_context column was added to the ai_instructions table.';
    END IF;
END $$;
