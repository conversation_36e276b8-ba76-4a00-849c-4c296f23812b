-- Migration: Add customer addresses table
-- Depends on: add_customer_portal_features.sql

SET search_path TO site;

CREATE TABLE customer_addresses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  farm_alias VARCHAR(255),
  address VARCHAR(255) NOT NULL,
  city VARCHAR(100) NOT NULL,
  state VARCHAR(50) NOT NULL,
  zip_code VARCHAR(20) NOT NULL,
  country VARCHAR(100) DEFAULT 'USA',
  delivery_instructions TEXT,
  access_code VARCHAR(100),
  contact_name VARCHAR(255),
  contact_phone VARCHAR(20),
  is_default BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX customer_addresses_customer_id_idx ON customer_addresses(customer_id);
CREATE INDEX customer_addresses_farm_id_idx ON customer_addresses(farm_id);