-- Migration: Fix billing system tables to use explicit schema references
-- Depends on:

SET search_path TO site;

-- This migration fixes the issue with the add_billing_system_tables.sql migration
-- by explicitly specifying the schema name in all table creation and reference statements.

-- Table for bill categories
CREATE TABLE IF NOT EXISTS site.bill_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  color VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(farm_id, name)
);

-- Table for bills
CREATE TABLE IF NOT EXISTS site.bills (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_id UUID NOT NULL REFERENCES site.farms(id) ON DELETE CASCADE,
  category_id UUID REFERENCES site.bill_categories(id) ON DELETE SET NULL,
  vendor_id UUID REFERENCES site.vendors(id) ON DELETE SET NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  amount DECIMAL(15, 2) NOT NULL,
  due_date DATE NOT NULL,
  status VARCHAR(50) NOT NULL DEFAULT 'unpaid', -- unpaid, paid, partial, overdue
  payment_method VARCHAR(100),
  reference_number VARCHAR(100),
  notes TEXT,
  is_recurring BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES site.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for recurring bill schedules
CREATE TABLE IF NOT EXISTS site.recurring_bills (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bill_id UUID NOT NULL REFERENCES site.bills(id) ON DELETE CASCADE,
  frequency VARCHAR(50) NOT NULL, -- weekly, biweekly, monthly, quarterly, annually
  start_date DATE NOT NULL,
  end_date DATE,
  day_of_month INTEGER,
  day_of_week INTEGER,
  week_of_month INTEGER,
  month_of_year INTEGER,
  last_generated_date DATE,
  next_due_date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for bill payments
CREATE TABLE IF NOT EXISTS site.bill_payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bill_id UUID NOT NULL REFERENCES site.bills(id) ON DELETE CASCADE,
  amount DECIMAL(15, 2) NOT NULL,
  payment_date DATE NOT NULL,
  payment_method VARCHAR(100),
  reference_number VARCHAR(100),
  notes TEXT,
  created_by UUID REFERENCES site.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for linking bills to transactions
CREATE TABLE IF NOT EXISTS site.bill_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bill_id UUID NOT NULL REFERENCES site.bills(id) ON DELETE CASCADE,
  transaction_id UUID NOT NULL REFERENCES site.transactions(id) ON DELETE CASCADE,
  amount DECIMAL(15, 2) NOT NULL,
  linked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  linked_by UUID REFERENCES site.users(id) ON DELETE SET NULL,
  notes TEXT,
  UNIQUE(bill_id, transaction_id)
);

-- Table for bill attachments (documents, receipts, etc.)
CREATE TABLE IF NOT EXISTS site.bill_attachments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bill_id UUID NOT NULL REFERENCES site.bills(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(512) NOT NULL,
  file_type VARCHAR(100),
  file_size INTEGER,
  uploaded_by UUID REFERENCES site.users(id) ON DELETE SET NULL,
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_bills_farm_id ON site.bills(farm_id);
CREATE INDEX IF NOT EXISTS idx_bills_category_id ON site.bills(category_id);
CREATE INDEX IF NOT EXISTS idx_bills_vendor_id ON site.bills(vendor_id);
CREATE INDEX IF NOT EXISTS idx_bills_due_date ON site.bills(due_date);
CREATE INDEX IF NOT EXISTS idx_bills_status ON site.bills(status);
CREATE INDEX IF NOT EXISTS idx_bill_categories_farm_id ON site.bill_categories(farm_id);
CREATE INDEX IF NOT EXISTS idx_recurring_bills_bill_id ON site.recurring_bills(bill_id);
CREATE INDEX IF NOT EXISTS idx_recurring_bills_next_due_date ON site.recurring_bills(next_due_date);
CREATE INDEX IF NOT EXISTS idx_bill_payments_bill_id ON site.bill_payments(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_transactions_bill_id ON site.bill_transactions(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_transactions_transaction_id ON site.bill_transactions(transaction_id);
CREATE INDEX IF NOT EXISTS idx_bill_attachments_bill_id ON site.bill_attachments(bill_id);