# Document Encryption Migration

## Overview

This migration adds encryption support to the document management system. It adds fields to the `documents` and `signable_documents` tables to track encryption status, method, and key information.

## What This Migration Does

The migration adds the following fields to both the `documents` and `signable_documents` tables:

1. `is_encrypted` (BOOLEAN) - Indicates whether the document is encrypted
2. `encryption_method` (VARCHAR) - The encryption method used (e.g., AES-256-CBC)
3. `encryption_key_id` (VARCHAR) - Identifier for the encryption key used
4. `encryption_iv` (VARCHAR) - Initialization vector used for encryption

It also adds indexes for better query performance:
- `idx_documents_is_encrypted` on `documents(is_encrypted)`
- `idx_signable_documents_is_encrypted` on `signable_documents(is_encrypted)`

## How to Apply the Migration

To apply this migration, run the following command from the PostgreSQL command line:

```sql
\i '/path/to/webapp/server/db/migrations/add_document_encryption_fields.sql'
```

Or you can run it using the database migration script:

```bash
node server/scripts/run-migration.js add_document_encryption_fields.sql
```

## Dependencies

This migration has dependencies on the following existing tables:
- `documents`
- `signable_documents`

Make sure these tables exist before running this migration.

## Verification

After running the migration, you can verify that the columns were added correctly by running:

```sql
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'documents' AND column_name LIKE 'encryption%';

SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'signable_documents' AND column_name LIKE 'encryption%';
```

## Using Document Encryption

After applying this migration, you can use document encryption by:

1. Setting the `encrypt` parameter to `true` when uploading documents or creating signable documents
2. The system will automatically encrypt the file and store the encryption details
3. When downloading the document, the system will automatically decrypt it

Example API usage:

```javascript
// Upload an encrypted document
const response = await fetch('/api/documents/upload', {
  method: 'POST',
  body: formData,
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  data: {
    encrypt: true
  }
});

// Create an encrypted signable document
const response = await fetch('/api/signable-documents/create', {
  method: 'POST',
  body: formData,
  headers: {
    'Content-Type': 'multipart/form-data',
  },
  data: {
    encrypt: true
  }
});
```

## Security Considerations

The encryption system uses AES-256-CBC encryption with a master key stored in environment variables. In production, ensure that:

1. The `DOCUMENT_ENCRYPTION_MASTER_KEY` environment variable is set to a secure random value
2. The master key is properly secured and rotated periodically
3. Access to encrypted documents is properly restricted