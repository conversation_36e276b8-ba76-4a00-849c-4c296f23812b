-- Create driver_schedules table for transport management

-- Set the search path to the site schema
SET search_path TO site;

-- Create driver_schedules table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.driver_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  driver_id UUID NOT NULL REFERENCES site.drivers(id),
  delivery_id UUID REFERENCES site.deliveries(id),
  pickup_id UUID REFERENCES site.pickups(id),
  schedule_type VARCHAR(20) NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  is_all_day BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
  location VARCHAR(255),
  location_lat DECIMAL(10, 7),
  location_lng DECIMAL(10, 7),
  priority VARCHAR(10) DEFAULT 'medium',
  recurrence_pattern VARCHAR(100),
  recurrence_end_date TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for schedule_type
ALTER TABLE site.driver_schedules DROP CONSTRAINT IF EXISTS driver_schedules_schedule_type_check;
ALTER TABLE site.driver_schedules ADD CONSTRAINT driver_schedules_schedule_type_check 
  CHECK (schedule_type IN ('delivery', 'pickup', 'maintenance', 'break', 'other'));

-- Add enum constraint for status
ALTER TABLE site.driver_schedules DROP CONSTRAINT IF EXISTS driver_schedules_status_check;
ALTER TABLE site.driver_schedules ADD CONSTRAINT driver_schedules_status_check 
  CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled'));

-- Add enum constraint for priority
ALTER TABLE site.driver_schedules DROP CONSTRAINT IF EXISTS driver_schedules_priority_check;
ALTER TABLE site.driver_schedules ADD CONSTRAINT driver_schedules_priority_check 
  CHECK (priority IN ('low', 'medium', 'high'));

-- Add comments to columns
COMMENT ON COLUMN site.driver_schedules.id IS 'Unique identifier for the driver schedule';
COMMENT ON COLUMN site.driver_schedules.farm_id IS 'ID of the farm this schedule belongs to';
COMMENT ON COLUMN site.driver_schedules.driver_id IS 'ID of the driver assigned to this schedule';
COMMENT ON COLUMN site.driver_schedules.delivery_id IS 'The delivery associated with this schedule (if applicable)';
COMMENT ON COLUMN site.driver_schedules.pickup_id IS 'The pickup associated with this schedule (if applicable)';
COMMENT ON COLUMN site.driver_schedules.schedule_type IS 'Type of schedule: delivery, pickup, maintenance, break, or other';
COMMENT ON COLUMN site.driver_schedules.title IS 'Title or name of the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.description IS 'Detailed description of the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.start_time IS 'Start time of the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.end_time IS 'End time of the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.is_all_day IS 'Whether this is an all-day activity';
COMMENT ON COLUMN site.driver_schedules.status IS 'Status of the schedule: scheduled, in_progress, completed, or cancelled';
COMMENT ON COLUMN site.driver_schedules.location IS 'Location description for the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.location_lat IS 'Latitude coordinate for the location';
COMMENT ON COLUMN site.driver_schedules.location_lng IS 'Longitude coordinate for the location';
COMMENT ON COLUMN site.driver_schedules.priority IS 'Priority level: low, medium, or high';
COMMENT ON COLUMN site.driver_schedules.recurrence_pattern IS 'Pattern for recurring schedules (e.g., "daily", "weekly", "monthly")';
COMMENT ON COLUMN site.driver_schedules.recurrence_end_date IS 'End date for recurring schedules';
COMMENT ON COLUMN site.driver_schedules.notes IS 'Additional notes about the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.created_at IS 'Timestamp when the schedule record was created';
COMMENT ON COLUMN site.driver_schedules.updated_at IS 'Timestamp when the schedule record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS driver_schedules_farm_id_idx ON site.driver_schedules(farm_id);
CREATE INDEX IF NOT EXISTS driver_schedules_driver_id_idx ON site.driver_schedules(driver_id);
CREATE INDEX IF NOT EXISTS driver_schedules_delivery_id_idx ON site.driver_schedules(delivery_id);
CREATE INDEX IF NOT EXISTS driver_schedules_pickup_id_idx ON site.driver_schedules(pickup_id);
CREATE INDEX IF NOT EXISTS driver_schedules_start_time_idx ON site.driver_schedules(start_time);
CREATE INDEX IF NOT EXISTS driver_schedules_end_time_idx ON site.driver_schedules(end_time);
CREATE INDEX IF NOT EXISTS driver_schedules_status_idx ON site.driver_schedules(status);
CREATE INDEX IF NOT EXISTS driver_schedules_schedule_type_idx ON site.driver_schedules(schedule_type);

-- Verify the table was created
DO $$
DECLARE
    table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'site' 
        AND table_name = 'driver_schedules'
    ) INTO table_exists;

    IF table_exists THEN
        RAISE NOTICE 'The driver_schedules table was successfully created.';
    ELSE
        RAISE EXCEPTION 'Failed to create the driver_schedules table.';
    END IF;
END $$;

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_driver_schedules_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_driver_schedules_timestamp
BEFORE UPDATE ON site.driver_schedules
FOR EACH ROW EXECUTE FUNCTION update_driver_schedules_updated_at();