-- Migration: Add custom domain support
-- Depends on: add_subdomain_column.sql

SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
  RAISE NOTICE 'Migration step: %', step_name;
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- Add custom_domain column to farms table
SELECT log_migration_step('Adding custom_domain column to farms table');

ALTER TABLE farms ADD COLUMN IF NOT EXISTS custom_domain VARCHAR(255);
COMMENT ON COLUMN farms.custom_domain IS 'Custom domain for the farm (paid feature)';

-- Add custom_domain_verified column to farms table
SELECT log_migration_step('Adding custom_domain_verified column to farms table');

ALTER TABLE farms ADD COLUMN IF NOT EXISTS custom_domain_verified BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN farms.custom_domain_verified IS 'Whether the custom domain has been verified';

-- Add custom_domain_enabled to subscription_plans features
SELECT log_migration_step('Updating subscription plans to include custom domain feature');

-- Update Basic plan (no custom domain)
UPDATE subscription_plans 
SET features = features || '{"custom_domain_enabled": false}'::jsonb
WHERE name = 'Basic' AND NOT (features ? 'custom_domain_enabled');

-- Update Standard plan (no custom domain)
UPDATE subscription_plans 
SET features = features || '{"custom_domain_enabled": false}'::jsonb
WHERE name = 'Standard' AND NOT (features ? 'custom_domain_enabled');

-- Update Premium plan (includes custom domain)
UPDATE subscription_plans 
SET features = features || '{"custom_domain_enabled": true}'::jsonb
WHERE name = 'Premium' AND NOT (features ? 'custom_domain_enabled');

-- Update Enterprise plan (includes custom domain)
UPDATE subscription_plans 
SET features = features || '{"custom_domain_enabled": true}'::jsonb
WHERE name = 'Enterprise' AND NOT (features ? 'custom_domain_enabled');

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
    INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, dependencies)
    VALUES (
      gen_random_uuid(),
      'Add custom domain support',
      'webapp/server/db/migrations/add_custom_domain_support.sql',
      (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
      NOW(),
      NOW(),
      NOW(),
      ARRAY['add_subdomain_column.sql']
    );
  END IF;
END $$;

-- Verify the migration was successful
DO $$
DECLARE
  missing_columns TEXT := '';
BEGIN
  -- Check if custom_domain column exists in farms table
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'custom_domain'
  ) THEN
    missing_columns := missing_columns || 'custom_domain, ';
  END IF;

  -- Check if custom_domain_verified column exists in farms table
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'site' AND table_name = 'farms' AND column_name = 'custom_domain_verified'
  ) THEN
    missing_columns := missing_columns || 'custom_domain_verified, ';
  END IF;

  -- Check if any columns are missing
  IF missing_columns <> '' THEN
    missing_columns := SUBSTRING(missing_columns, 1, LENGTH(missing_columns) - 2);
    RAISE EXCEPTION 'Migration failed. The following columns were not created: %', missing_columns;
  ELSE
    RAISE NOTICE 'Migration successful. All columns were created.';
  END IF;
END $$;