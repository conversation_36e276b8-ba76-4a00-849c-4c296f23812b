# Database Schema Management in NxtAcre

## Overview

The NxtAcre Farm Management Platform uses a hybrid approach to database schema management, combining SQL scripts for initial schema creation and major alterations with Sequelize ORM for model definition and synchronization.

## Schema Management Components

### 1. SQL Scripts

- **schema.sql**: Defines the initial database structure with tables, constraints, and indexes
- **Migration scripts**: SQL files that alter the schema (add/modify columns, tables, etc.)
- **Master migration scripts**: Files like `add_transport_and_receipt_tables.sql` that orchestrate multiple migrations

### 2. Sequelize ORM

- **Model definitions**: JavaScript files in the `models` directory that define the data structure
- **Associations**: Defined in `associations.js` to establish relationships between models
- **Synchronization**: The `syncModels` function in `database.js` synchronizes models with the database

### 3. Schema Comparison Tools

- **compareModelsToSchema.js**: Script that compares Sequelize models to the database schema and generates SQL scripts for missing columns

## Schema Management Process

1. **Schema Initialization**:
   - The `initializeSchema` function in `database.js` checks if the schema exists
   - If the schema doesn't exist, it creates it
   - If the users table doesn't exist, it executes `schema.sql` to create all tables
   - If the users table exists, it applies alter schema files to add/modify columns

2. **Model Synchronization**:
   - The `syncModels` function in `database.js` synchronizes Sequelize models with the database
   - Synchronization is done with `alter: false`, meaning it won't modify existing tables
   - This ensures that models are aware of the database structure but doesn't change it

3. **Schema Comparison and Updates**:
   - The `compareModelsToSchema.js` script compares models to the database schema
   - It identifies missing columns and generates SQL scripts to add them
   - These scripts are stored in the `model_updates` directory for review and execution

## Best Practices

1. **Always use SQL scripts for schema changes**:
   - Create SQL migration scripts for all schema changes
   - Don't rely on Sequelize's auto-migration features (alter: true)
   - This ensures better control and visibility of schema changes

2. **Test migrations before applying**:
   - Use test scripts (e.g., `test_role_migration.js`) to verify migrations
   - Run migrations in a test environment before production

3. **Document schema changes**:
   - Update README files when adding new tables or making significant schema changes
   - Include information about the purpose of tables and their relationships

4. **Use the compareModelsToSchema.js script regularly**:
   - Run it to identify discrepancies between models and the database schema
   - Review and apply the generated SQL scripts as needed

## Recommendations for Improvement

1. **Enhance the compareModelsToSchema.js script**:
   - Add support for detecting type mismatches between models and database columns
   - Add support for detecting missing foreign keys and constraints
   - Add support for detecting missing tables

2. **Implement a version-based migration system**:
   - Add a migrations table to track applied migrations
   - Create a more general-purpose migration runner that can apply migrations in order
   - Ensure migrations are idempotent (can be run multiple times without issues)

3. **Automate schema verification**:
   - Create a script that verifies all models have corresponding tables
   - Verify that all relationships defined in associations.js are reflected in the database schema
   - Run this verification as part of the CI/CD pipeline

4. **Improve documentation**:
   - Create a comprehensive guide for database schema management
   - Document the purpose and relationships of all tables
   - Provide examples of common schema changes and how to implement them

## Conclusion

The current hybrid approach to schema management provides good control and visibility of schema changes while leveraging the benefits of Sequelize ORM for model definition and querying. By following the best practices and implementing the recommended improvements, the schema management process can be made more robust and maintainable.