-- Migration: Fix customer_id to tax_documents table for customer tax form uploads
-- Depends on: add_tax_system_tables.sql

SET search_path TO site;

-- Add customer_id column to tax_documents table
ALTER TABLE tax_documents
ADD COLUMN IF NOT EXISTS customer_id UUID NULL,
ADD CONSTRAINT fk_tax_documents_customer
FOREIGN KEY (customer_id)
REFERENCES customers(id)
ON DELETE CASCADE;

-- Add index for customer_id
CREATE INDEX IF NOT EXISTS tax_documents_customer_id_idx ON tax_documents(customer_id);

-- Update the document_type CHECK constraint to include customer_tax_form
ALTER TABLE tax_documents
DROP CONSTRAINT IF EXISTS tax_documents_document_type_check,
ADD CONSTRAINT tax_documents_document_type_check 
CHECK (document_type IN ('w2', '1099', 'schedule_f', 'depreciation', 'expense', 'income', 'other', 'customer_tax_form'));