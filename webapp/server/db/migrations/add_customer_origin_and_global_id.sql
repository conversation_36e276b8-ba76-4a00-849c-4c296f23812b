-- Migration: Add customer origin and global ID
-- Depends on: add_customer_portal_features.sql

SET search_path TO site;

ALTER TABLE customers
ADD COLUMN origin VARCHAR(50) DEFAULT 'farm',
ADD COLUMN global_customer_id UUID,
ADD COLUMN marketplace_user_id UUID;

COMMENT ON COLUMN customers.origin IS 'Origin of the customer: farm, marketplace';
COMMENT ON COLUMN customers.global_customer_id IS 'ID linking customers across farms for the same user';
COMMENT ON COLUMN customers.marketplace_user_id IS 'ID of the user in the marketplace system';