-- Add ON DELETE CASCADE to role_permissions.farm_id foreign key constraint
-- This script modifies the foreign key constraint on the role_permissions table
-- to automatically delete role permissions when the referenced farm is deleted

-- Set the search path to the appropriate schema
SET search_path TO site;

-- First, drop the existing foreign key constraint
DO $$
BEGIN
    -- Check if the constraint exists before trying to drop it
    IF EXISTS (
        SELECT 1
        FROM information_schema.table_constraints
        WHERE constraint_name = 'role_permissions_farm_id_fkey'
        AND table_schema = 'site'
        AND table_name = 'role_permissions'
    ) THEN
        ALTER TABLE role_permissions DROP CONSTRAINT role_permissions_farm_id_fkey;
        RAISE NOTICE 'Dropped existing foreign key constraint: role_permissions_farm_id_fkey';
    ELSE
        RAISE NOTICE 'Foreign key constraint role_permissions_farm_id_fkey does not exist, creating new constraint';
    END IF;
END
$$;

-- Add the new foreign key constraint with ON DELETE CASCADE
ALTER TABLE role_permissions
ADD CONSTRAINT role_permissions_farm_id_fkey
FOREIGN KEY (farm_id) REFERENCES farms(id) ON DELETE CASCADE;

-- Add a comment to explain the purpose of the constraint
COMMENT ON CONSTRAINT role_permissions_farm_id_fkey ON role_permissions IS 'Ensures that when a farm is deleted, all associated role permissions are also deleted';
