/**
 * Password Generator Utility
 * 
 * This utility provides functions for generating secure passwords with various options:
 * - Length
 * - Include uppercase letters
 * - Include lowercase letters
 * - Include numbers
 * - Include special characters
 * - Exclude similar characters
 * - Exclude ambiguous characters
 */

export interface PasswordGeneratorOptions {
  length: number;
  includeUppercase: boolean;
  includeLowercase: boolean;
  includeNumbers: boolean;
  includeSpecial: boolean;
  excludeSimilar: boolean;
  excludeAmbiguous: boolean;
}

// Character sets
const LOWERCASE_CHARS = 'abcdefghijklmnopqrstuvwxyz';
const UPPERCASE_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
const NUMBER_CHARS = '0123456789';
const SPECIAL_CHARS = '!@#$%^&*()_+-=[]{}|;:,.<>?';
const SIMILAR_CHARS = 'il1Lo0O';
const AMBIGUOUS_CHARS = '{}[]()/\\\'"`~,;:.<>';

/**
 * Generates a secure random password based on the provided options
 * @param options Password generation options
 * @returns A randomly generated password
 */
export const generatePassword = (options: PasswordGeneratorOptions): string => {
  const {
    length,
    includeUppercase,
    includeLowercase,
    includeNumbers,
    includeSpecial,
    excludeSimilar,
    excludeAmbiguous
  } = options;

  // Validate options
  if (length <= 0) {
    throw new Error('Password length must be greater than 0');
  }

  if (!includeUppercase && !includeLowercase && !includeNumbers && !includeSpecial) {
    throw new Error('At least one character type must be included');
  }

  // Build character pool based on options
  let charPool = '';

  if (includeLowercase) {
    charPool += LOWERCASE_CHARS;
  }

  if (includeUppercase) {
    charPool += UPPERCASE_CHARS;
  }

  if (includeNumbers) {
    charPool += NUMBER_CHARS;
  }

  if (includeSpecial) {
    charPool += SPECIAL_CHARS;
  }

  // Remove excluded characters
  if (excludeSimilar) {
    for (const char of SIMILAR_CHARS) {
      charPool = charPool.replace(new RegExp(char, 'g'), '');
    }
  }

  if (excludeAmbiguous) {
    for (const char of AMBIGUOUS_CHARS) {
      // Escape special regex characters
      const escapedChar = char.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      charPool = charPool.replace(new RegExp(escapedChar, 'g'), '');
    }
  }

  // Ensure we have characters to work with
  if (charPool.length === 0) {
    throw new Error('No characters available with the selected options');
  }

  // Generate password
  let password = '';
  let hasRequiredChars = false;

  // Keep generating until we have a password that meets all requirements
  while (!hasRequiredChars) {
    password = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charPool.length);
      password += charPool[randomIndex];
    }

    // Check if password meets requirements
    hasRequiredChars = true;

    if (includeUppercase && !/[A-Z]/.test(password)) {
      hasRequiredChars = false;
    }

    if (includeLowercase && !/[a-z]/.test(password)) {
      hasRequiredChars = false;
    }

    if (includeNumbers && !/[0-9]/.test(password)) {
      hasRequiredChars = false;
    }

    if (includeSpecial && !/[^A-Za-z0-9]/.test(password)) {
      hasRequiredChars = false;
    }
  }

  return password;
};

/**
 * Default options for password generation
 */
export const DEFAULT_PASSWORD_OPTIONS: PasswordGeneratorOptions = {
  length: 16,
  includeUppercase: true,
  includeLowercase: true,
  includeNumbers: true,
  includeSpecial: true,
  excludeSimilar: false,
  excludeAmbiguous: false
};