/**
 * Utility functions for handling redirects related to farm selection and admin pages
 */
import { MAIN_DOMAIN } from '../config';
import { getSubdomain } from './subdomainUtils';

/**
 * Interface for user object with minimal required properties for redirect logic
 */
interface UserForRedirect {
  is_global_admin?: boolean;
}

/**
 * Redirects to the appropriate farm subdomain if needed
 * @param targetSubdomain - The subdomain to redirect to
 * @param currentPath - The current path to maintain after redirect
 * @param user - The user object to check for global admin status
 * @param preserveAuth - Whether to preserve authentication when redirecting (default: true)
 * @returns boolean - True if a redirect was performed, false otherwise
 */
export const redirectToFarmSubdomain = (
  targetSubdomain: string | null,
  currentPath: string = window.location.pathname,
  user?: UserForRedirect | null,
  preserveAuth: boolean = true
): boolean => {
  // If no target subdomain, stay on app subdomain if not already there
  if (!targetSubdomain) {
    const currentSubdomain = getSubdomain();

    // If we're not on the app subdomain, redirect to it
    if (currentSubdomain !== 'app' && currentSubdomain !== 'admin' && currentSubdomain !== 'store') {
      const hostname = window.location.hostname;
      const parts = hostname.split('.');
      const domain = parts.length >= 2 ? parts.slice(1).join('.') : hostname;

      // Construct the redirect URL to app subdomain
      let redirectUrl = `https://app.${domain}${currentPath}`;

      // If preserveAuth is true, add a query parameter to indicate that we should check for auth cookies
      if (preserveAuth) {
        const separator = currentPath.includes('?') ? '&' : '?';
        redirectUrl += `${separator}checkAuth=true`;
      }

      // Perform the redirect
      window.location.href = redirectUrl;
      return true;
    }

    return false;
  }

  // Get current hostname and subdomain
  const currentSubdomain = getSubdomain();

  // Check if user is a global admin
  const isGlobalAdmin = user?.is_global_admin || false;

  // If we're already on the correct subdomain, no redirect needed
  if (currentSubdomain === targetSubdomain) {
    return false;
  }

  // Don't redirect from the admin subdomain if user is global admin
  if (currentSubdomain === 'admin' && isGlobalAdmin) {
    return false;
  }

  // Don't redirect from the store subdomain
  if (currentSubdomain === 'store') {
    return false;
  }

  // Construct the redirect URL
  let redirectUrl = `https://${targetSubdomain}.${MAIN_DOMAIN}${currentPath}`;

  // If preserveAuth is true, add a query parameter to indicate that we should check for auth cookies
  if (preserveAuth) {
    const separator = currentPath.includes('?') ? '&' : '?';
    redirectUrl += `${separator}checkAuth=true`;
  }

  // Perform the redirect
  window.location.href = redirectUrl;
  return true;
};

/**
 * Redirects to the admin subdomain if needed
 * @param currentPath - The current path to maintain after redirect
 * @param user - The user object to check for global admin status
 * @returns boolean - True if a redirect was performed, false otherwise
 */
export const redirectToAdminSubdomain = (
  currentPath: string = window.location.pathname,
  user?: UserForRedirect | null
): boolean => {
  // Only global admins can access admin subdomain
  if (!user?.is_global_admin) {
    return false;
  }

  // Get current subdomain
  const currentSubdomain = getSubdomain();

  // If we're already on the admin subdomain, no redirect needed
  if (currentSubdomain === 'admin') {
    return false;
  }

  // Perform the redirect
  window.location.href = `https://admin.${MAIN_DOMAIN}${currentPath}`;
  return true;
};

/**
 * Redirects to the app subdomain for login
 * @param originalUrl - The original URL to redirect back to after login
 * @param targetSubdomain - The target subdomain to redirect to after login
 * @param error - Optional error message to include in the redirect
 * @param unauthorized - Optional flag to indicate unauthorized access
 * @returns string - The redirect URL
 */
export const getAppSubdomainLoginRedirectUrl = (
  originalUrl: string,
  targetSubdomain?: string,
  error?: string,
  unauthorized?: boolean
): string => {
  const protocol = window.location.protocol;
  let redirectUrl = `${protocol}//app.${MAIN_DOMAIN}/login?redirect=${encodeURIComponent(originalUrl)}`;

  if (targetSubdomain) {
    redirectUrl += `&subdomain=${targetSubdomain}`;
  }

  if (error) {
    redirectUrl += `&error=${encodeURIComponent(error)}`;
  }

  if (unauthorized) {
    redirectUrl += '&unauthorized=true';
  }

  return redirectUrl;
};

/**
 * Server-side function to get the app subdomain login redirect URL
 * This is a separate function because it doesn't use window.location
 * @param protocol - The protocol (http or https)
 * @param originalUrl - The original URL to redirect back to after login
 * @param targetSubdomain - The target subdomain to redirect to after login
 * @param error - Optional error message to include in the redirect
 * @param unauthorized - Optional flag to indicate unauthorized access
 * @returns string - The redirect URL
 */
export const getServerAppSubdomainLoginRedirectUrl = (
  protocol: string,
  originalUrl: string,
  targetSubdomain?: string,
  error?: string,
  unauthorized?: boolean
): string => {
  let redirectUrl = `${protocol}://app.${MAIN_DOMAIN}/login?redirect=${encodeURIComponent(originalUrl)}`;

  if (targetSubdomain) {
    redirectUrl += `&subdomain=${targetSubdomain}`;
  }

  if (error) {
    redirectUrl += `&error=${encodeURIComponent(error)}`;
  }

  if (unauthorized) {
    redirectUrl += '&unauthorized=true';
  }

  return redirectUrl;
};
