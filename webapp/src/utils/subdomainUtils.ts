/**
 * Utility functions for handling subdomains in the client application
 */
import { API_URL } from '../config';
import { setStorageItem } from './storageUtils';

/**
 * Extract the subdomain from the current hostname
 * @returns {string|null} The subdomain or null if no subdomain is present
 */
export const getSubdomain = (): string | null => {
  const hostname = window.location.hostname;

  // If localhost or IP address, return null
  if (hostname === 'localhost' || /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname)) {
    return null;
  }

  // Extract the subdomain (first part of the hostname)
  const parts = hostname.split('.');

  // If there are at least 3 parts (subdomain.domain.tld), return the first part
  if (parts.length >= 3) {
    const subdomain = parts[0];

    // Special case: return 'admin' for admin subdomain to prevent redirect loops
    if (subdomain === 'admin') {
      return 'admin';
    }

    // Don't return other common subdomains like 'www', 'app', or 'api'
    if (subdomain !== 'www' && subdomain !== 'app' && subdomain !== 'api') {
      // Store the farm subdomain in a cookie for later use
      // This is used when redirecting back from admin to the farm
      setStorageItem('lastFarmSubdomain', subdomain);
      return subdomain;
    }
  }

  return null;
};

/**
 * Get the farm ID from the subdomain using the server API
 * @returns {Promise<string|null>} The farm ID or null if not found
 */
export const getFarmIdFromSubdomain = async (): Promise<string | null> => {
  const subdomain = getSubdomain();

  if (!subdomain) {
    return null;
  }

  try {
    const response = await fetch(`${API_URL}/farms/by-subdomain/${subdomain}`);

    if (!response.ok) {
      console.error('Error fetching farm by subdomain:', await response.text());
      return null;
    }

    const data = await response.json();
    return data.id;
  } catch (error) {
    console.error('Error fetching farm by subdomain:', error);
    return null;
  }
};
