import axios from 'axios';
import { API_URL } from '../config';
import { getAuthToken } from '../utils/storageUtils';

// Define Session interface
export interface Session {
  id: string;
  user_id: string;
  token: string;
  ip_address: string;
  user_agent: string;
  device_type: string;
  browser: string;
  operating_system: string;
  is_active: boolean;
  is_trusted: boolean;
  device_fingerprint?: string;
  device_name?: string;
  expires_at: string;
  last_active_at: string;
  created_at: string;
  updated_at: string;
}

// Get all active sessions for the current user
export const getUserSessions = async (): Promise<Session[]> => {
  const token = getAuthToken();

  const response = await axios.get(`${API_URL}/sessions`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data;
};

// Terminate a specific session
export const terminateSession = async (sessionId: string): Promise<{ message: string }> => {
  const token = getAuthToken();

  const response = await axios.delete(`${API_URL}/sessions/${sessionId}`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data;
};

// Terminate all sessions except the current one
export const terminateAllSessions = async (): Promise<{ message: string }> => {
  const token = getAuthToken();

  const response = await axios.delete(`${API_URL}/sessions`, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data;
};

// Format date for display
export const formatSessionDate = (dateString: string): string => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

// Check if a session is the current session
export const isCurrentSession = (session: Session): boolean => {
  const token = getAuthToken();
  return session.token === token;
};

// Trust a device
export const trustDevice = async (deviceFingerprint: string): Promise<{ message: string }> => {
  const token = getAuthToken();

  const response = await axios.post(`${API_URL}/sessions/trust/${deviceFingerprint}`, {}, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data;
};

// Untrust a device
export const untrustDevice = async (deviceFingerprint: string): Promise<{ message: string }> => {
  const token = getAuthToken();

  const response = await axios.post(`${API_URL}/sessions/untrust/${deviceFingerprint}`, {}, {
    headers: {
      Authorization: `Bearer ${token}`
    }
  });

  return response.data;
};
