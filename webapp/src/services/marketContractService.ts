import axios from 'axios';
import { API_URL } from '../config';

// Contract statistics interface
export interface ContractStatistics {
  total: number;
  active: number;
  pending: number;
  completed: number;
}

// Types for market contract data
export interface MarketContract {
  id: string;
  farm_id: string;
  supplier_id?: string;
  created_by: string;
  title: string;
  description?: string;
  contract_type: string;
  status: string;
  start_date?: string;
  end_date?: string;
  value?: number;
  currency?: string;
  payment_terms?: string;
  delivery_terms?: string;
  products?: any;
  attachments?: any;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  marketContractSupplier?: {
    id: string;
    name: string;
    contact_name?: string;
    email?: string;
    phone?: string;
  };
  creator?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

// Base URL for market contract API
const MARKET_CONTRACT_API_BASE = `${API_URL}/market`;

/**
 * Get all contracts for the current farm
 * @param farmId The ID of the farm
 * @returns Promise<MarketContract[]> List of contracts for the farm
 */
export const getContracts = async (farmId?: string): Promise<MarketContract[]> => {
  try {
    const url = farmId 
      ? `${MARKET_CONTRACT_API_BASE}/contracts?farmId=${farmId}`
      : `${MARKET_CONTRACT_API_BASE}/contracts`;

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching contracts:', error);
    throw error;
  }
};

/**
 * Get a single contract by ID
 * @param contractId The ID of the contract to fetch
 * @returns Promise<MarketContract> The contract details
 */
export const getContract = async (contractId: string): Promise<MarketContract> => {
  try {
    const response = await axios.get(`${MARKET_CONTRACT_API_BASE}/contracts/${contractId}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching contract ${contractId}:`, error);
    throw error;
  }
};

/**
 * Create a new contract
 * @param contractData The contract data to create
 * @returns Promise<MarketContract> The created contract
 */
export const createContract = async (contractData: Partial<MarketContract>): Promise<MarketContract> => {
  try {
    const response = await axios.post(`${MARKET_CONTRACT_API_BASE}/contracts`, contractData);
    return response.data;
  } catch (error) {
    console.error('Error creating contract:', error);
    throw error;
  }
};

/**
 * Update an existing contract
 * @param contractId The ID of the contract to update
 * @param contractData The updated contract data
 * @returns Promise<MarketContract> The updated contract
 */
export const updateContract = async (
  contractId: string,
  contractData: Partial<MarketContract>
): Promise<MarketContract> => {
  try {
    const response = await axios.put(
      `${MARKET_CONTRACT_API_BASE}/contracts/${contractId}`,
      contractData
    );
    return response.data;
  } catch (error) {
    console.error(`Error updating contract ${contractId}:`, error);
    throw error;
  }
};

/**
 * Delete a contract
 * @param contractId The ID of the contract to delete
 * @returns Promise<{ message: string }> Success message
 */
export const deleteContract = async (contractId: string): Promise<{ message: string }> => {
  try {
    const response = await axios.delete(`${MARKET_CONTRACT_API_BASE}/contracts/${contractId}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting contract ${contractId}:`, error);
    throw error;
  }
};

/**
 * Calculate contract statistics from a list of contracts
 * @param contracts List of contracts to calculate statistics from
 * @returns ContractStatistics Object containing contract statistics
 */
export const getContractStatistics = async (farmId?: string): Promise<ContractStatistics> => {
  try {
    const contracts = await getContracts(farmId);

    const statistics: ContractStatistics = {
      total: contracts.length,
      active: contracts.filter(contract => contract.status === 'active').length,
      pending: contracts.filter(contract => contract.status === 'pending').length,
      completed: contracts.filter(contract => contract.status === 'completed').length
    };

    return statistics;
  } catch (error) {
    console.error('Error calculating contract statistics:', error);
    throw error;
  }
};
