import axios from 'axios';
import { API_URL } from '../config';

export interface MigrationSystem {
  id: string;
  name: string;
  description: string;
  logo_url: string | null;
  website: string | null;
  supported_formats: string[];
  supported_entities: string[];
}

export interface MigrationJob {
  id: string;
  user_id: string;
  farm_id: string;
  source_system: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  entities: string[];
  file_path: string | null;
  error_message: string | null;
  created_at: string;
  updated_at: string;
  completed_at: string | null;
}

export interface MigrationResult {
  job_id: string;
  total_records: number;
  imported_records: number;
  failed_records: number;
  warnings: string[];
  errors: string[];
  entity_counts: {
    [key: string]: {
      total: number;
      imported: number;
      failed: number;
    }
  };
}

/**
 * Get all supported migration systems
 */
export const getSupportedSystems = async (): Promise<MigrationSystem[]> => {
  try {
    const response = await axios.get(`${API_URL}/migration/systems`);
    return response.data.systems;
  } catch (error) {
    console.error('Error fetching supported migration systems:', error);
    throw error;
  }
};

/**
 * Get migration system by ID
 */
export const getMigrationSystem = async (systemId: string): Promise<MigrationSystem> => {
  try {
    const response = await axios.get(`${API_URL}/migration/systems/${systemId}`);
    return response.data.system;
  } catch (error) {
    console.error('Error fetching migration system:', error);
    throw error;
  }
};

/**
 * Get migration jobs for a farm
 */
export const getMigrationJobs = async (farmId: string): Promise<MigrationJob[]> => {
  try {
    const response = await axios.get(`${API_URL}/migration/jobs?farmId=${farmId}`);
    return response.data.jobs;
  } catch (error) {
    console.error('Error fetching migration jobs:', error);
    throw error;
  }
};

/**
 * Get migration job by ID
 */
export const getMigrationJob = async (jobId: string): Promise<MigrationJob> => {
  try {
    const response = await axios.get(`${API_URL}/migration/jobs/${jobId}`);
    return response.data.job;
  } catch (error) {
    console.error('Error fetching migration job:', error);
    throw error;
  }
};

/**
 * Get migration job results
 */
export const getMigrationResults = async (jobId: string): Promise<MigrationResult> => {
  try {
    const response = await axios.get(`${API_URL}/migration/jobs/${jobId}/results`);
    return response.data.results;
  } catch (error) {
    console.error('Error fetching migration results:', error);
    throw error;
  }
};

/**
 * Create a new migration job
 */
export const createMigrationJob = async (data: {
  farmId: string;
  sourceSystem: string;
  entities: string[];
}): Promise<MigrationJob> => {
  try {
    const response = await axios.post(`${API_URL}/migration/jobs`, data);
    return response.data.job;
  } catch (error) {
    console.error('Error creating migration job:', error);
    throw error;
  }
};

/**
 * Upload migration data file
 */
export const uploadMigrationFile = async (
  jobId: string,
  file: File,
  format: string
): Promise<MigrationJob> => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('format', format);

    const response = await axios.post(
      `${API_URL}/migration/jobs/${jobId}/upload`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );

    return response.data.job;
  } catch (error) {
    console.error('Error uploading migration file:', error);
    throw error;
  }
};

/**
 * Start migration job processing
 */
export const startMigrationJob = async (jobId: string): Promise<MigrationJob> => {
  try {
    const response = await axios.post(`${API_URL}/migration/jobs/${jobId}/start`);
    return response.data.job;
  } catch (error) {
    console.error('Error starting migration job:', error);
    throw error;
  }
};

/**
 * Cancel migration job
 */
export const cancelMigrationJob = async (jobId: string): Promise<MigrationJob> => {
  try {
    const response = await axios.post(`${API_URL}/migration/jobs/${jobId}/cancel`);
    return response.data.job;
  } catch (error) {
    console.error('Error canceling migration job:', error);
    throw error;
  }
};

/**
 * Export data to a file for migration to another system
 */
export const exportData = async (
  farmId: string,
  targetSystem: string,
  entities: string[],
  format: string
): Promise<Blob> => {
  try {
    const response = await axios.post(
      `${API_URL}/migration/export`,
      {
        farmId,
        targetSystem,
        entities,
        format
      },
      {
        responseType: 'blob'
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error exporting data:', error);
    throw error;
  }
};

/**
 * Get migration templates for a specific system
 */
export const getMigrationTemplates = async (systemId: string): Promise<Blob> => {
  try {
    const response = await axios.get(
      `${API_URL}/migration/systems/${systemId}/templates`,
      {
        responseType: 'blob'
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching migration templates:', error);
    throw error;
  }
};
