import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import SupportDashboard from './SupportDashboard';
import SupportTicketList from './SupportTicketList';
import SupportTicketDetail from './SupportTicketDetail';
import SupportTicketForm from './SupportTicketForm';

const SupportRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<SupportDashboard />} />
      <Route path="/tickets" element={<SupportTicketList />} />
      <Route path="/tickets/new" element={<SupportTicketForm />} />
      <Route path="/tickets/:ticketId" element={<SupportTicketDetail />} />
      <Route path="/tickets/:ticketId/edit" element={<SupportTicketForm />} />
      <Route path="*" element={<Navigate to="/support" replace />} />
    </Routes>
  );
};

export default SupportRoutes;