import React, { useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import GlobalMarketplace from './GlobalMarketplace';
import FarmMarketplace from './FarmMarketplace';
import ProductDetail from './ProductDetail';
import CategoryBrowsing from './CategoryBrowsing';
import SearchResults from './SearchResults';
import Checkout from './Checkout';
import CheckoutSuccess from './CheckoutSuccess';
import CustomerProfile from './CustomerProfile';
import OrderHistory from './OrderHistory';
import OrderDetail from './OrderDetail';
import SavedCarts from './SavedCarts';
import Cart from './Cart';
import Login from './Login';
import Register from './Register';

const MarketplaceRoutes: React.FC = () => {
  const location = useLocation();

  // Check if we're on the store subdomain
  useEffect(() => {
    const hostname = window.location.hostname;

    // Skip for localhost and IP addresses during development
    if (hostname === 'localhost' || /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname)) {
      return;
    }

    // If we're not on the store subdomain, redirect to the store subdomain
    if (!hostname.startsWith('store.')) {
      const parts = hostname.split('.');
      const domain = parts.length >= 2 ? parts.slice(1).join('.') : hostname;

      // Get the current path
      const path = window.location.pathname;

      // Construct the store subdomain URL
      let storeUrl = `https://store.${domain}`;

      // If we're on a specific route, append it to the URL
      if (path === '/store-login') {
        storeUrl += '/store-login';
      } else if (path === '/store-register') {
        storeUrl += '/store-register';
      } else {
        storeUrl += path;
      }

      // Redirect to the store subdomain
      window.location.href = storeUrl;
    }
  }, []);

  // Check if we're on a specific route
  const isStoreLogin = location.pathname === '/store-login';
  const isStoreRegister = location.pathname === '/store-register';

  // If we're on a specific route, render the appropriate component
  if (isStoreLogin) {
    return <Login />;
  }

  if (isStoreRegister) {
    return <Register />;
  }

  return (
    <Routes>
      <Route index element={<GlobalMarketplace />} />
      <Route path="farm/:farmId" element={<FarmMarketplace />} />
      <Route path="product/:productId" element={<ProductDetail />} />
      <Route path="categories/:category" element={<CategoryBrowsing />} />
      <Route path="checkout" element={<Checkout />} />
      <Route path="checkout-success" element={<CheckoutSuccess />} />
      <Route path="profile" element={<CustomerProfile />} />
      <Route path="orders" element={<OrderHistory />} />
      <Route path="orders/:orderId" element={<OrderDetail />} />
      <Route path="saved-carts" element={<SavedCarts />} />
      <Route path="cart" element={<Cart />} />
      <Route path="login" element={<Login />} />
      <Route path="register" element={<Register />} />
      {/* Add more marketplace routes here as they are implemented */}
      <Route path="search" element={<SearchResults />} />
    </Routes>
  );
};

export default MarketplaceRoutes;
