import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Driver {
  id: string;
  firstName: string;
  lastName: string;
}

interface Item {
  id: string;
  name: string;
  quantity: number;
  unit: string;
}

interface PickupFormData {
  pickupDate: string;
  status: string;
  source: string;
  contactName: string;
  contactInfo: string;
  driverId: string;
  pickupType: string;
  items: Item[];
  notes: string;
  referenceNumber: string;
  estimatedArrival: string;
}

const PickupForm = () => {
  const { pickupId } = useParams<{ pickupId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!pickupId;

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  const [formData, setFormData] = useState<PickupFormData>({
    pickupDate: '',
    status: 'scheduled',
    source: '',
    contactName: '',
    contactInfo: '',
    driverId: '',
    pickupType: 'product',
    items: [],
    notes: '',
    referenceNumber: '',
    estimatedArrival: '',
  });

  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [inventoryItems, setInventoryItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch drivers and inventory items for the selected farm
  useEffect(() => {
    const fetchData = async () => {
      if (!currentFarm) return;

      try {
        const [driversResponse, inventoryResponse] = await Promise.all([
          axios.get(`${API_URL}/drivers?farmId=${currentFarm.id}`),
          axios.get(`${API_URL}/api/inventory?farmId=${currentFarm.id}`)
        ]);

        setDrivers(Array.isArray(driversResponse.data) ? driversResponse.data : []);
        setInventoryItems(Array.isArray(inventoryResponse.data) ? inventoryResponse.data : []);
      } catch (err: any) {
        console.error('Error fetching form data:', err);
        setError('Failed to load form data. Please try again later.');
      }
    };

    fetchData();
  }, [currentFarm]);

  // Fetch pickup data if in edit mode
  useEffect(() => {
    if (isEditMode && pickupId) {
      const fetchPickup = async () => {
        setLoading(true);
        setError(null);

        try {
          const response = await axios.get(`${API_URL}/pickups/${pickupId}`);
          const pickupData = response.data;

          setFormData({
            pickupDate: pickupData.pickupDate || '',
            status: pickupData.status || 'scheduled',
            source: pickupData.source || '',
            contactName: pickupData.contactName || '',
            contactInfo: pickupData.contactInfo || '',
            driverId: pickupData.driverId || '',
            pickupType: pickupData.pickupType || 'product',
            items: pickupData.items || [],
            notes: pickupData.notes || '',
            referenceNumber: pickupData.referenceNumber || '',
            estimatedArrival: pickupData.estimatedArrival || '',
          });
        } catch (err: any) {
          console.error('Error fetching pickup:', err);
          setError('Failed to load pickup data. Please try again later.');
        } finally {
          setLoading(false);
        }
      };

      fetchPickup();
    }
  }, [isEditMode, pickupId]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // If changing pickup type to customer_pickup, set source to farm's address if available
    if (name === 'pickupType' && value === 'customer_pickup' && currentFarm) {
      setFormData(prev => ({ 
        ...prev, 
        [name]: value,
        source: currentFarm.address || prev.source
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle item selection
  const handleAddItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { id: '', name: '', quantity: 0, unit: '' }]
    }));
  };

  // Handle item removal
  const handleRemoveItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }));
  };

  // Handle item field changes
  const handleItemChange = (index: number, field: string, value: string | number) => {
    setFormData(prev => {
      const updatedItems = [...prev.items];
      if (field === 'id' && typeof value === 'string') {
        const selectedItem = inventoryItems.find(item => item.id === value);
        if (selectedItem) {
          updatedItems[index] = {
            ...updatedItems[index],
            id: selectedItem.id,
            name: selectedItem.name,
            unit: selectedItem.unit || 'unit'
          };
        }
      } else {
        updatedItems[index] = {
          ...updatedItems[index],
          [field]: value
        };
      }
      return { ...prev, items: updatedItems };
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm) {
      setError('No farm selected. Please select a farm before creating a pickup.');
      return;
    }

    // Validate driver field for non-customer pickups
    if (formData.pickupType !== 'customer_pickup' && !formData.driverId) {
      setError('Driver is required for non-customer pickups.');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const pickupData = {
        ...formData,
        farmId: currentFarm.id
      };

      if (isEditMode) {
        await axios.put(`${API_URL}/pickups/${pickupId}`, pickupData);
        setSuccessMessage('Pickup updated successfully!');
      } else {
        await axios.post(`${API_URL}/pickups`, pickupData);
        setSuccessMessage('Pickup created successfully!');
      }

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/transport/pickups');
      }, 1500);
    } catch (err: any) {
      console.error('Error saving pickup:', err);
      setError('Failed to save pickup. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Pickup' : 'Create New Pickup'}
        </h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{successMessage}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg p-6">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="pickupDate" className="block text-sm font-medium text-gray-700 mb-1">
                Pickup Date
              </label>
              <input
                type="date"
                id="pickupDate"
                name="pickupDate"
                value={formData.pickupDate}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label htmlFor="estimatedArrival" className="block text-sm font-medium text-gray-700 mb-1">
                Estimated Arrival
              </label>
              <input
                type="date"
                id="estimatedArrival"
                name="estimatedArrival"
                value={formData.estimatedArrival}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              >
                <option value="scheduled">Scheduled</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label htmlFor="pickupType" className="block text-sm font-medium text-gray-700 mb-1">
                Pickup Type
              </label>
              <select
                id="pickupType"
                name="pickupType"
                value={formData.pickupType}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              >
                <option value="product">Product</option>
                <option value="equipment">Equipment</option>
                <option value="return">Return</option>
                <option value="customer_pickup">Customer Pickup</option>
                <option value="other">Other</option>
              </select>
              {formData.pickupType === 'customer_pickup' && (
                <p className="mt-1 text-sm text-gray-500">
                  Customer pickup means the customer will come to your location to pick up items. The pickup address will be set to your farm's address.
                </p>
              )}
            </div>

            <div>
              <label htmlFor="driverId" className="block text-sm font-medium text-gray-700 mb-1">
                Driver {formData.pickupType !== 'customer_pickup' && <span className="text-red-500">*</span>}
              </label>
              <select
                id="driverId"
                name="driverId"
                value={formData.driverId}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required={formData.pickupType !== 'customer_pickup'}
              >
                <option value="">Select a driver</option>
                {drivers.map(driver => (
                  <option key={driver.id} value={driver.id}>
                    {driver.firstName} {driver.lastName}
                  </option>
                ))}
              </select>
              {formData.pickupType === 'customer_pickup' && (
                <p className="mt-1 text-sm text-gray-500">
                  Driver is optional for customer pickups since the customer will come to your location.
                </p>
              )}
            </div>

            <div>
              <label htmlFor="source" className="block text-sm font-medium text-gray-700 mb-1">
                Source Location
              </label>
              <input
                type="text"
                id="source"
                name="source"
                value={formData.source}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label htmlFor="referenceNumber" className="block text-sm font-medium text-gray-700 mb-1">
                Reference Number
              </label>
              <input
                type="text"
                id="referenceNumber"
                name="referenceNumber"
                value={formData.referenceNumber}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label htmlFor="contactName" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Name
              </label>
              <input
                type="text"
                id="contactName"
                name="contactName"
                value={formData.contactName}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label htmlFor="contactInfo" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Information
              </label>
              <input
                type="text"
                id="contactInfo"
                name="contactInfo"
                value={formData.contactInfo}
                onChange={handleInputChange}
                className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700">Items</label>
              <button
                type="button"
                onClick={handleAddItem}
                className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Add Item
              </button>
            </div>

            {formData.items.length === 0 ? (
              <p className="text-sm text-gray-500">No items added yet.</p>
            ) : (
              <div className="space-y-3">
                {formData.items.map((item, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-md">
                    <div className="flex-grow grid grid-cols-1 md:grid-cols-3 gap-3">
                      <div>
                        <label htmlFor={`item-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Item
                        </label>
                        <select
                          id={`item-${index}`}
                          value={item.id}
                          onChange={(e) => handleItemChange(index, 'id', e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                        >
                          <option value="">Select an item</option>
                          {inventoryItems.map(invItem => (
                            <option key={invItem.id} value={invItem.id}>{invItem.name}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label htmlFor={`quantity-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Quantity
                        </label>
                        <input
                          type="number"
                          id={`quantity-${index}`}
                          value={item.quantity}
                          onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value))}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                          min="1"
                        />
                      </div>
                      <div>
                        <label htmlFor={`unit-${index}`} className="block text-xs font-medium text-gray-700 mb-1">
                          Unit
                        </label>
                        <input
                          type="text"
                          id={`unit-${index}`}
                          value={item.unit}
                          onChange={(e) => handleItemChange(index, 'unit', e.target.value)}
                          className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-xs border-gray-300 rounded-md"
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveItem(index)}
                      className="inline-flex items-center p-1 border border-transparent rounded-full text-red-600 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="mb-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              rows={3}
              value={formData.notes}
              onChange={handleInputChange}
              className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => navigate('/transport/pickups')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {loading ? 'Saving...' : isEditMode ? 'Update Pickup' : 'Create Pickup'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default PickupForm;
