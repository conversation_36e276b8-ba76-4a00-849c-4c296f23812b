import { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getDriverLocations, formatDate, DriverLocation as ServiceDriverLocation } from '../../services/driverLocationService';

// Extend the DriverLocation interface from the service with additional fields needed for the map
interface DriverLocation extends ServiceDriverLocation {
  driverName: string;
  status: string;
  vehicleType: string;
  vehiclePlate: string;
  currentDeliveryId?: string;
  currentPickupId?: string;
  nextStopName?: string;
  nextStopTime?: string;
}

const DriverLocationMap = () => {
  const [driverLocations, setDriverLocations] = useState<DriverLocation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [selectedDriver, setSelectedDriver] = useState<DriverLocation | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<number>(60); // seconds
  const [lastRefreshed, setLastRefreshed] = useState<Date>(new Date());

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Load Google Maps API
  useEffect(() => {
    if (!window.google || !window.google.maps) {
      const googleMapsScript = document.createElement('script');
      googleMapsScript.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.REACT_APP_GOOGLE_MAPS_API_KEY}&libraries=places`;
      googleMapsScript.async = true;
      googleMapsScript.defer = true;
      googleMapsScript.onload = () => setMapLoaded(true);
      document.head.appendChild(googleMapsScript);

      return () => {
        document.head.removeChild(googleMapsScript);
      };
    } else {
      setMapLoaded(true);
    }
  }, []);

  // Fetch driver locations
  const fetchDriverLocations = async () => {
    if (!currentFarm) return;

    setLoading(true);
    setError(null);

    try {
      const locationsData = await getDriverLocations({ farmId: currentFarm.id });
      setDriverLocations(locationsData as DriverLocation[]);
      setLastRefreshed(new Date());
    } catch (err: any) {
      console.error('Error fetching driver locations:', err);

      // Check if the error has structured error information
      if (err.structuredError) {
        setError(err.structuredError.message);
      } else {
        setError('Failed to load driver locations. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchDriverLocations();
  }, [currentFarm]);

  // Set up refresh interval
  useEffect(() => {
    const intervalId = setInterval(() => {
      fetchDriverLocations();
    }, refreshInterval * 1000);

    return () => clearInterval(intervalId);
  }, [refreshInterval, currentFarm]);

  // Initialize map when Google Maps is loaded and driver locations are available
  useEffect(() => {
    if (mapLoaded && driverLocations.length > 0 && !loading) {
      initializeMap();
    }
  }, [mapLoaded, driverLocations, loading]);

  // Initialize Google Map
  const initializeMap = () => {
    const mapContainer = document.getElementById('driver-location-map');
    if (!mapContainer) return;

    // Create map centered on the average of all driver locations
    const bounds = new window.google.maps.LatLngBounds();
    driverLocations.forEach(location => {
      bounds.extend(new window.google.maps.LatLng(location.latitude, location.longitude));
    });

    const map = new window.google.maps.Map(mapContainer, {
      zoom: 10,
      center: bounds.getCenter(),
      mapTypeId: window.google.maps.MapTypeId.ROADMAP,
      mapTypeControl: true,
      streetViewControl: true,
      fullscreenControl: true,
    });

    // Fit map to bounds if multiple drivers
    if (driverLocations.length > 1) {
      map.fitBounds(bounds);
    }

    // Add markers for each driver
    driverLocations.forEach(location => {
      const marker = new window.google.maps.Marker({
        position: { lat: location.latitude, lng: location.longitude },
        map,
        title: location.driverName,
        icon: {
          url: getMarkerIcon(location.status),
          scaledSize: new window.google.maps.Size(30, 30),
        },
      });

      // Add click event to marker
      marker.addListener('click', () => {
        setSelectedDriver(location);
      });
    });
  };

  // Get marker icon based on driver status
  const getMarkerIcon = (status: string) => {
    switch (status) {
      case 'active':
        return 'https://maps.google.com/mapfiles/ms/icons/green-dot.png';
      case 'inactive':
        return 'https://maps.google.com/mapfiles/ms/icons/red-dot.png';
      case 'on_delivery':
        return 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png';
      case 'on_pickup':
        return 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png';
      default:
        return 'https://maps.google.com/mapfiles/ms/icons/purple-dot.png';
    }
  };

  // Using formatDate from driverLocationService.ts

  // Handle refresh interval change
  const handleRefreshIntervalChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setRefreshInterval(parseInt(e.target.value));
  };

  // Manual refresh
  const handleManualRefresh = () => {
    fetchDriverLocations();
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Driver Locations</h1>
        <div className="flex items-center space-x-4">
          <div>
            <label htmlFor="refresh-interval" className="block text-sm font-medium text-gray-700 mr-2">
              Refresh every:
            </label>
            <select
              id="refresh-interval"
              value={refreshInterval}
              onChange={handleRefreshIntervalChange}
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
            >
              <option value="10">10 seconds</option>
              <option value="30">30 seconds</option>
              <option value="60">1 minute</option>
              <option value="300">5 minutes</option>
            </select>
          </div>
          <button
            onClick={handleManualRefresh}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Refresh Now
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500">
              Showing {driverLocations.length} driver{driverLocations.length !== 1 ? 's' : ''}
            </p>
            <p className="text-sm text-gray-500">
              Last updated: {lastRefreshed.toLocaleTimeString()}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3">
          {/* Driver list */}
          <div className="md:col-span-1 border-r border-gray-200 overflow-y-auto" style={{ maxHeight: '600px' }}>
            {loading && driverLocations.length === 0 ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                <p className="ml-3 text-gray-500">Loading driver locations...</p>
              </div>
            ) : driverLocations.length === 0 ? (
              <div className="p-6 text-center">
                <p className="text-gray-500">No driver locations available.</p>
              </div>
            ) : (
              <ul className="divide-y divide-gray-200">
                {driverLocations.map((location) => (
                  <li 
                    key={location.driverId} 
                    className={`p-4 hover:bg-gray-50 cursor-pointer ${selectedDriver?.driverId === location.driverId ? 'bg-primary-50' : ''}`}
                    onClick={() => setSelectedDriver(location)}
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div 
                          className="h-8 w-8 rounded-full flex items-center justify-center"
                          style={{ backgroundColor: location.status === 'active' ? '#d1fae5' : 
                                                    location.status === 'inactive' ? '#fee2e2' : 
                                                    location.status === 'on_delivery' ? '#dbeafe' : 
                                                    location.status === 'on_pickup' ? '#fef3c7' : '#f3e8ff' }}
                        >
                          <span className="text-sm">
                            {location.driverName.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">{location.driverName}</p>
                        <p className="text-xs text-gray-500">
                          {location.vehicleType} • {location.vehiclePlate}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDate(location.timestamp)}
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>

          {/* Map */}
          <div className="md:col-span-2">
            <div id="driver-location-map" style={{ height: '600px', width: '100%' }}>
              {!mapLoaded && (
                <div className="flex justify-center items-center h-full">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
                  <p className="ml-3 text-gray-500">Loading map...</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Selected driver details */}
        {selectedDriver && (
          <div className="p-4 border-t border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-2">{selectedDriver.driverName}</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-500">Vehicle</p>
                <p className="text-sm font-medium">{selectedDriver.vehicleType} ({selectedDriver.vehiclePlate})</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <p className="text-sm font-medium">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    selectedDriver.status === 'active' ? 'bg-green-100 text-green-800' : 
                    selectedDriver.status === 'inactive' ? 'bg-red-100 text-red-800' : 
                    selectedDriver.status === 'on_delivery' ? 'bg-blue-100 text-blue-800' : 
                    selectedDriver.status === 'on_pickup' ? 'bg-yellow-100 text-yellow-800' : 
                    'bg-purple-100 text-purple-800'
                  }`}>
                    {selectedDriver.status === 'active' ? 'Active' : 
                     selectedDriver.status === 'inactive' ? 'Inactive' : 
                     selectedDriver.status === 'on_delivery' ? 'On Delivery' : 
                     selectedDriver.status === 'on_pickup' ? 'On Pickup' : 
                     selectedDriver.status}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Last Updated</p>
                <p className="text-sm font-medium">{formatDate(selectedDriver.timestamp)}</p>
              </div>
              {selectedDriver.nextStopName && (
                <div className="md:col-span-2">
                  <p className="text-sm text-gray-500">Next Stop</p>
                  <p className="text-sm font-medium">
                    {selectedDriver.nextStopName}
                    {selectedDriver.nextStopTime && ` (${formatDate(selectedDriver.nextStopTime)})`}
                  </p>
                </div>
              )}
              {(selectedDriver.currentDeliveryId || selectedDriver.currentPickupId) && (
                <div className="md:col-span-3">
                  <p className="text-sm text-gray-500">Current Assignment</p>
                  <div className="mt-1">
                    {selectedDriver.currentDeliveryId && (
                      <a 
                        href={`/transport/deliveries/${selectedDriver.currentDeliveryId}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-2"
                      >
                        View Delivery
                      </a>
                    )}
                    {selectedDriver.currentPickupId && (
                      <a 
                        href={`/transport/pickups/${selectedDriver.currentPickupId}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View Pickup
                      </a>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default DriverLocationMap;
