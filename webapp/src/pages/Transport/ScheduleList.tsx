import { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { getDriverSchedules, deleteDriverSchedule, formatDate as serviceFormatDate, DriverSchedule } from '../../services/driverScheduleService';

// Extend the DriverSchedule interface from the service with additional fields needed for the schedule list
interface Schedule extends DriverSchedule {
  driverName: string;
  startDate: string;
  endDate: string;
  title: string;
  description: string;
  routeDetails: string;
  stops: Array<{
    id: string;
    type: string;
    locationName: string;
    address: string;
    scheduledTime: string;
    notes: string;
  }>;
}

const ScheduleList = () => {
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Fetch schedules for the selected farm
  useEffect(() => {
    const fetchSchedules = async () => {
      if (!currentFarm) return;

      setLoading(true);
      setError(null);

      try {
        const schedulesData = await getDriverSchedules({ farmId: currentFarm.id });
        setSchedules(schedulesData as Schedule[]);
      } catch (err: any) {
        console.error('Error fetching schedules:', err);

        // Check if the error has structured error information
        if (err.structuredError) {
          setError(err.structuredError.message);
        } else {
          setError('Failed to load schedules. Please try again later.');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSchedules();
  }, [currentFarm]);

  // Handle schedule deletion
  const handleDeleteSchedule = async (scheduleId: string) => {
    if (!window.confirm('Are you sure you want to delete this schedule?')) {
      return;
    }

    try {
      await deleteDriverSchedule(scheduleId);
      setSchedules(schedules.filter(schedule => schedule.id !== scheduleId));
    } catch (err: any) {
      console.error('Error deleting schedule:', err);

      // Check if the error has structured error information
      if (err.structuredError) {
        setError(err.structuredError.message);
      } else {
        setError('Failed to delete schedule. Please try again later.');
      }
    }
  };

  // Using formatDate from driverScheduleService.ts with a custom wrapper to show only the date part
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format time
  const formatTime = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Driver Schedules</h1>
        <div>
          <Link
            to="/transport/schedules/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create Schedule
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading schedules...</p>
        </div>
      ) : schedules.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No schedules found for this farm.</p>
          <p className="text-sm text-gray-400 mb-6">
            Create your first driver schedule to start planning routes and deliveries.
          </p>
          <Link
            to="/transport/schedules/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create Schedule
          </Link>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {schedules.map((schedule) => (
              <li key={schedule.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="ml-3">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {schedule.title}
                        </p>
                        <p className="text-sm text-gray-500">
                          {schedule.driverName && <span className="mr-2">Driver: {schedule.driverName}</span>}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link
                        to={`/transport/schedules/${schedule.id}`}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        View
                      </Link>
                      <Link
                        to={`/transport/schedules/${schedule.id}/edit`}
                        className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDeleteSchedule(schedule.id)}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      {schedule.startDate && (
                        <p className="flex items-center text-sm text-gray-500 mr-6">
                          <span>Start: {formatDate(schedule.startDate)}</span>
                        </p>
                      )}
                      {schedule.endDate && (
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 mr-6">
                          <span>End: {formatDate(schedule.endDate)}</span>
                        </p>
                      )}
                    </div>
                    {schedule.status && (
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          schedule.status === 'scheduled' ? 'bg-blue-100 text-blue-800' : 
                          schedule.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' : 
                          schedule.status === 'completed' ? 'bg-green-100 text-green-800' : 
                          schedule.status === 'cancelled' ? 'bg-red-100 text-red-800' : 
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {schedule.status === 'scheduled' ? 'Scheduled' : 
                           schedule.status === 'in_progress' ? 'In Progress' : 
                           schedule.status === 'completed' ? 'Completed' : 
                           schedule.status === 'cancelled' ? 'Cancelled' : 
                           schedule.status}
                        </span>
                      </div>
                    )}
                  </div>
                  {schedule.description && (
                    <div className="mt-2 text-sm text-gray-500">
                      <p>{schedule.description}</p>
                    </div>
                  )}
                  {schedule.stops && schedule.stops.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm font-medium text-gray-500">Stops ({schedule.stops.length}):</p>
                      <div className="mt-1 flex flex-wrap gap-2">
                        {schedule.stops.slice(0, 3).map((stop, index) => (
                          <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            {stop.locationName} ({formatTime(stop.scheduledTime)})
                          </span>
                        ))}
                        {schedule.stops.length > 3 && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            +{schedule.stops.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </Layout>
  );
};

export default ScheduleList;
