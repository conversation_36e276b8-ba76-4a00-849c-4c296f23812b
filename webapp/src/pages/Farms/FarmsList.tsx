import { useState, useEffect, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { checkPermission } from '../../services/rolePermissionService';

interface Farm {
  id: string;
  name: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  tax_id: string;
  UserFarm?: {
    role: string;
  };
}

const FarmsList = () => {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editablePermissions, setEditablePermissions] = useState<{[key: string]: boolean}>({});

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      navigate('/login');
      return;
    }

    const fetchFarms = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/farms/user/${user.id}`);
        const farmsData = response.data.farms || [];
        setFarms(farmsData);

        // Check edit permissions for each farm
        const permissions: {[key: string]: boolean} = {};
        for (const farm of farmsData) {
          try {
            const hasEditPermission = await checkPermission(farm.id, user.id, 'farms', 'edit');
            permissions[farm.id] = hasEditPermission;
          } catch (permErr) {
            console.error(`Error checking permissions for farm ${farm.id}:`, permErr);
            permissions[farm.id] = false;
          }
        }
        setEditablePermissions(permissions);
      } catch (err: any) {
        console.error('Error fetching farms:', err);
        setError(err.response?.data?.error || 'Failed to load farms');
      } finally {
        setLoading(false);
      }
    };

    fetchFarms();
  }, [user, navigate]);

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Your Farms</h1>
        <Link
          to="/farms/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Add New Farm
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading farms...</p>
        </div>
      ) : farms.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No farms yet</h3>
          <p className="text-gray-500 mb-6">Get started by adding your first farm.</p>
          <Link
            to="/farms/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Farm
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {farms.map((farm) => (
            <div key={farm.id} className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">{farm.name}</h3>
                <p className="text-sm text-gray-500 mb-4">
                  {farm.address && (
                    <>
                      {farm.address}, {farm.city}, {farm.state} {farm.zip_code}
                      <br />
                    </>
                  )}
                  {farm.UserFarm && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-2">
                      {farm.UserFarm.role.charAt(0).toUpperCase() + farm.UserFarm.role.slice(1)}
                    </span>
                  )}
                </p>
                <div className="mt-4 flex justify-end space-x-2">
                  {editablePermissions[farm.id] && (
                    <Link
                      to={`/farms/${farm.id}/edit`}
                      className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Edit
                    </Link>
                  )}
                  <Link
                    to={`/farms/${farm.id}`}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </Layout>
  );
};

export default FarmsList;
