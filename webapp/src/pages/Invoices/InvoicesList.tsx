import { useState, useEffect, useContext } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import axios from 'axios';
import { format } from 'date-fns';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Customer {
  id: string;
  name: string;
}

interface Farm {
  id: string;
  name: string;
}

interface Invoice {
  id: string;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  status: string;
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  Customer?: Customer;
  recipientFarm?: Farm;
  Farm?: Farm; // The farm that created the invoice (for received invoices)
}

interface RecurringInvoice {
  id: string;
  invoice_id: string;
  frequency: string;
  start_date: string;
  end_date?: string;
  day_of_month?: number;
  day_of_week?: number;
  week_of_month?: number;
  month_of_year?: number;
  last_generated_date?: string;
  next_due_date: string;
  auto_send: boolean;
  additional_recipient_emails?: string[];
  created_at: string;
  updated_at: string;
  Invoice?: Invoice;
}

const InvoicesList = () => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [recurringInvoices, setRecurringInvoices] = useState<RecurringInvoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [recurringLoading, setRecurringLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recurringError, setRecurringError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<'regular' | 'recurring'>('regular');

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Redirect to login if no user
  useEffect(() => {
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);

  // Fetch invoices for the selected farm
  useEffect(() => {
    if (!selectedFarm?.id) return;

    const fetchInvoices = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get(`${API_URL}/invoices/farm/${selectedFarm.id}`);
        setInvoices(Array.isArray(response.data) ? response.data : response.data.invoices || []);
      } catch (err: any) {
        console.error('Error fetching invoices:', err);
        setError(err.response?.data?.error || 'Failed to load invoices');
      } finally {
        setLoading(false);
      }
    };

    const fetchRecurringInvoices = async () => {
      try {
        setRecurringLoading(true);
        setRecurringError(null);

        const response = await axios.get(`${API_URL}/recurring-invoices/farm/${selectedFarm.id}`);
        setRecurringInvoices(response.data.recurringInvoices || []);
      } catch (err: any) {
        console.error('Error fetching recurring invoices:', err);
        setRecurringError(err.response?.data?.error || 'Failed to load recurring invoices');
      } finally {
        setRecurringLoading(false);
      }
    };

    fetchInvoices();
    fetchRecurringInvoices();
  }, [selectedFarm]);


  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
  };

  // Filter invoices by status
  const filteredInvoices = statusFilter === 'all'
    ? invoices
    : invoices.filter(invoice => invoice.status === statusFilter);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM d, yyyy');
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Invoices</h1>
        <Link
          to="/invoices/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Create New Invoice
        </Link>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            onClick={() => setActiveTab('regular')}
            className={`${
              activeTab === 'regular'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            aria-current={activeTab === 'regular' ? 'page' : undefined}
          >
            Regular Invoices
          </button>
          <button
            onClick={() => setActiveTab('recurring')}
            className={`${
              activeTab === 'recurring'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            aria-current={activeTab === 'recurring' ? 'page' : undefined}
          >
            Recurring Invoices
          </button>
        </nav>
      </div>

      {/* Filters - Only show for regular invoices */}
      {activeTab === 'regular' && (
        <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-1">
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status-filter"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              value={statusFilter}
              onChange={handleStatusFilterChange}
            >
              <option value="all">All Statuses</option>
              <option value="draft">Draft</option>
              <option value="sent">Sent</option>
              <option value="paid">Paid</option>
              <option value="overdue">Overdue</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        </div>
      )}

      {/* Legend for invoice types - Only show for regular invoices */}
      {activeTab === 'regular' && (
        <div className="mb-6 bg-white p-4 rounded-md shadow-sm">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Invoice Types:</h3>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 mr-2">
                Customer
              </span>
              <span className="text-sm text-gray-600">Invoices you sent to customers</span>
            </div>
            <div className="flex items-center">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                To Farm
              </span>
              <span className="text-sm text-gray-600">Invoices you sent to other farms</span>
            </div>
            <div className="flex items-center">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 mr-2">
                From Farm
              </span>
              <span className="text-sm text-gray-600">Invoices sent to you by other farms</span>
            </div>
          </div>
        </div>
      )}

      {/* Legend for recurring invoice frequencies - Only show for recurring invoices */}
      {activeTab === 'recurring' && (
        <div className="mb-6 bg-white p-4 rounded-md shadow-sm">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Frequency Types:</h3>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 mr-2">
                Daily
              </span>
              <span className="text-sm text-gray-600">Generated every day</span>
            </div>
            <div className="flex items-center">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                Weekly
              </span>
              <span className="text-sm text-gray-600">Generated once a week</span>
            </div>
            <div className="flex items-center">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                Monthly
              </span>
              <span className="text-sm text-gray-600">Generated once a month</span>
            </div>
            <div className="flex items-center">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 mr-2">
                Quarterly
              </span>
              <span className="text-sm text-gray-600">Generated every three months</span>
            </div>
            <div className="flex items-center">
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 mr-2">
                Yearly
              </span>
              <span className="text-sm text-gray-600">Generated once a year</span>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'regular' && error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {activeTab === 'recurring' && recurringError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{recurringError}</span>
        </div>
      )}

      {!selectedFarm ? (
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No farms available</h3>
          <p className="text-gray-500 mb-6">You need to create a farm before creating invoices.</p>
          <Link
            to="/farms/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Add New Farm
          </Link>
        </div>
      ) : (
        <>
          {/* Regular Invoices Tab */}
          {activeTab === 'regular' && (
            <>
              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">Loading invoices...</p>
                </div>
              ) : filteredInvoices.length === 0 ? (
                <div className="bg-white shadow rounded-lg p-8 text-center">
                  <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
                  <p className="text-gray-500 mb-6">
                    {statusFilter !== 'all'
                      ? `No invoices with status "${statusFilter}" found. Try changing the filter or create a new invoice.`
                      : 'Get started by creating your first invoice.'}
                  </p>
                  <Link
                    to="/invoices/new"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Create New Invoice
                  </Link>
                </div>
              ) : (
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Invoice #
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Recipient/Sender
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Issue Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Due Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="relative px-6 py-3">
                          <span className="sr-only">View</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredInvoices.map((invoice) => (
                        <tr 
                          key={invoice.id} 
                          className={`hover:bg-gray-50 ${invoice.Farm ? 'bg-purple-50' : ''}`}
                        >
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {invoice.invoice_number}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {invoice.Farm ? (
                              // This is an invoice received from another farm
                              <span className="flex items-center">
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 mr-2">
                                  From Farm
                                </span>
                                {invoice.Farm.name}
                              </span>
                            ) : invoice.recipientFarm ? (
                              // This is an invoice sent to another farm
                              <span className="flex items-center">
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                                  To Farm
                                </span>
                                {invoice.recipientFarm.name}
                              </span>
                            ) : invoice.Customer && invoice.Customer.name ? (
                              // This is an invoice sent to a customer
                              <span className="flex items-center">
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 mr-2">
                                  Customer
                                </span>
                                {invoice.Customer.name}
                              </span>
                            ) : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(invoice.issue_date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(invoice.due_date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                              ${invoice.status === 'paid' ? 'bg-green-100 text-green-800' :
                                invoice.status === 'overdue' ? 'bg-red-100 text-red-800' :
                                invoice.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                                invoice.status === 'cancelled' ? 'bg-gray-100 text-gray-800' :
                                'bg-yellow-100 text-yellow-800'}`}>
                              {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                            {formatCurrency(invoice.total_amount)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link to={`/invoices/${invoice.id}`} className="text-primary-600 hover:text-primary-900">
                              View
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          )}

          {/* Recurring Invoices Tab */}
          {activeTab === 'recurring' && (
            <>
              {recurringLoading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">Loading recurring invoices...</p>
                </div>
              ) : recurringInvoices.length === 0 ? (
                <div className="bg-white shadow rounded-lg p-8 text-center">
                  <svg className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No recurring invoices found</h3>
                  <p className="text-gray-500 mb-6">
                    Get started by creating an invoice with recurring settings.
                  </p>
                  <Link
                    to="/invoices/new"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Create New Invoice
                  </Link>
                </div>
              ) : (
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Invoice #
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Recipient
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Frequency
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Next Due Date
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Auto Send
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="relative px-6 py-3">
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {recurringInvoices.map((recurringInvoice) => (
                        <tr key={recurringInvoice.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {recurringInvoice.Invoice?.invoice_number || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {recurringInvoice.Invoice?.Customer?.name || 
                             recurringInvoice.Invoice?.recipientFarm?.name || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                              ${recurringInvoice.frequency === 'daily' ? 'bg-green-100 text-green-800' :
                                recurringInvoice.frequency === 'weekly' ? 'bg-blue-100 text-blue-800' :
                                recurringInvoice.frequency === 'monthly' ? 'bg-yellow-100 text-yellow-800' :
                                recurringInvoice.frequency === 'quarterly' ? 'bg-purple-100 text-purple-800' :
                                'bg-red-100 text-red-800'}`}>
                              {recurringInvoice.frequency.charAt(0).toUpperCase() + recurringInvoice.frequency.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(recurringInvoice.next_due_date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {recurringInvoice.auto_send ? (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                Enabled
                              </span>
                            ) : (
                              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                Disabled
                              </span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                            {recurringInvoice.Invoice ? formatCurrency(recurringInvoice.Invoice.total_amount) : 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link to={`/invoices/${recurringInvoice.invoice_id}`} className="text-primary-600 hover:text-primary-900 mr-4">
                              View Invoice
                            </Link>
                            <Link to={`/invoices/${recurringInvoice.invoice_id}/edit`} className="text-primary-600 hover:text-primary-900">
                              Edit
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          )}
        </>
      )}
    </Layout>
  );
};

export default InvoicesList;
