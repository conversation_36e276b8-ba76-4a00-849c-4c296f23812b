import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Task {
  id?: string;
  farm_id: string;
  title: string;
  description: string;
  category: string;
  priority: string;
  status: string;
  due_date: string;
  assigned_to: string;
  is_recurring: boolean;
  recurrence_pattern: string;
  notes: string;
}

const TaskForm = () => {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!taskId;

  const [task, setTask] = useState<Task>({
    farm_id: '',
    title: '',
    description: '',
    category: '',
    priority: 'medium',
    status: 'pending',
    due_date: '',
    assigned_to: '',
    is_recurring: false,
    recurrence_pattern: '',
    notes: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [employees, setEmployees] = useState<{ id: string; name: string }[]>([]);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  // Console log for debugging
  useEffect(() => {
    console.log("TaskForm rendered", { user, currentFarm, task });
  }, [user, currentFarm, task]);

  // Set farm_id from currentFarm
  useEffect(() => {
    if (currentFarm && !isEditMode) {
      setTask(prev => ({ ...prev, farm_id: currentFarm.id }));
    }
  }, [currentFarm, isEditMode]);

  // Fetch employees
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        if (!currentFarm) return;
        const response = await axios.get(`${API_URL}/employees?farm_id=${currentFarm.id}&include_farm_owners=true`);
        const employeeOptions = response.data.map((emp: any) => ({
          id: emp.id,
          name: `${emp.first_name} ${emp.last_name}`
        }));
        setEmployees(employeeOptions);
      } catch (err: any) {
        console.error('Error fetching employees:', err);
        // Don't set error here as it's not critical for task creation
      }
    };

    if (currentFarm) {
      fetchEmployees();
    }
  }, [currentFarm]);

  // Fetch task data if in edit mode
  useEffect(() => {
    const fetchTask = async () => {
      if (!taskId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/tasks/${taskId}`);

        // Format the date for the input field
        const formattedData = {
          ...response.data,
          due_date: response.data.due_date ? 
            new Date(response.data.due_date).toISOString().split('T')[0] : 
            ''
        };

        setTask(formattedData);
      } catch (err: any) {
        console.error('Error fetching task:', err);
        setError('Failed to load task data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchTask();
    }
  }, [taskId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setTask(prev => ({ ...prev, [name]: checked }));
    } else {
      setTask(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setTask(prev => ({ ...prev, [name]: checked }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!currentFarm) {
        setError('Please select a farm from the header dropdown.');
        setLoading(false);
        return;
      }

      if (!task.title) {
        setError('Task title is required.');
        setLoading(false);
        return;
      }

      if (isEditMode) {
        // Update existing task
        await axios.put(`${API_URL}/tasks/${taskId}`, task);
      } else {
        // Create new task
        await axios.post(`${API_URL}/tasks`, task);
      }

      // Redirect to task list
      navigate('/tasks');
    } catch (err: any) {
      console.error('Error saving task:', err);
      setError('Failed to save task. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Task' : 'Add New Task'}
        </h1>
        <Link
          to="/tasks"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Tasks
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Farm information - using global farm selector */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Farm <span className="text-red-500">*</span>
              </label>
              <div className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-700 sm:text-sm">
                {currentFarm ? currentFarm.name : 'Please select a farm from the header dropdown'}
              </div>
              <input type="hidden" name="farm_id" value={task.farm_id} />
            </div>

            {/* Task Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Task Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={task.title}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Category */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category"
                name="category"
                value={task.category}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select a category</option>
                <option value="field_work">Field Work</option>
                <option value="equipment_maintenance">Equipment Maintenance</option>
                <option value="livestock_care">Livestock Care</option>
                <option value="administrative">Administrative</option>
                <option value="purchasing">Purchasing</option>
                <option value="sales">Sales</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Priority */}
            <div>
              <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
                Priority
              </label>
              <select
                id="priority"
                name="priority"
                value={task.priority}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={task.status}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="pending">Pending</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            {/* Due Date */}
            <div>
              <label htmlFor="due_date" className="block text-sm font-medium text-gray-700 mb-1">
                Due Date
              </label>
              <input
                type="date"
                id="due_date"
                name="due_date"
                value={task.due_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Assigned To */}
            <div>
              <label htmlFor="assigned_to" className="block text-sm font-medium text-gray-700 mb-1">
                Assign To
              </label>
              <select
                id="assigned_to"
                name="assigned_to"
                value={task.assigned_to}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Unassigned</option>
                {employees.map(employee => (
                  <option key={employee.id} value={employee.id}>{employee.name}</option>
                ))}
              </select>
            </div>

            {/* Recurring Task */}
            <div className="col-span-1 sm:col-span-2">
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="is_recurring"
                    name="is_recurring"
                    type="checkbox"
                    checked={task.is_recurring}
                    onChange={handleCheckboxChange}
                    className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="is_recurring" className="font-medium text-gray-700">Recurring Task</label>
                  <p className="text-gray-500">Check if this task repeats on a schedule</p>
                </div>
              </div>
            </div>

            {/* Recurrence Pattern (only shown if is_recurring is true) */}
            {task.is_recurring && (
              <div className="col-span-1 sm:col-span-2">
                <label htmlFor="recurrence_pattern" className="block text-sm font-medium text-gray-700 mb-1">
                  Recurrence Pattern
                </label>
                <select
                  id="recurrence_pattern"
                  name="recurrence_pattern"
                  value={task.recurrence_pattern}
                  onChange={handleChange}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                >
                  <option value="">Select a pattern</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="biweekly">Bi-weekly</option>
                  <option value="monthly">Monthly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="yearly">Yearly</option>
                  <option value="custom">Custom</option>
                </select>
              </div>
            )}
          </div>

          {/* Description */}
          <div className="mt-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={task.description}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="Detailed description of the task"
            ></textarea>
          </div>

          {/* Notes */}
          <div className="mt-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={task.notes}
              onChange={handleChange}
              rows={3}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              placeholder="Additional notes or instructions"
            ></textarea>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/tasks"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Task'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default TaskForm;
