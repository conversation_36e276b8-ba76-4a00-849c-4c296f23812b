import { useState, useEffect, useContext } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import NewTimeEntryForm from "./NewTimeEntryForm.tsx";
import { API_URL } from '../../config';

const NewTimeOffRequestForm = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [employees, setEmployees] = useState<{ id: string; name: string }[]>([]);
  const [requestTypes] = useState<string[]>([
    'vacation',
    'sick',
    'personal',
    'bereavement',
    'other'
  ]);

  // Form state
  const [formData, setFormData] = useState({
    employee_id: '',
    request_type: '',
    start_date: '',
    end_date: '',
    total_days: '',
    reason: '',
    notes: ''
  });

  // Fetch employees
  useEffect(() => {
    const fetchEmployees = async () => {
      if (!currentFarm) return;

      try {
        const response = await axios.get(`${API_URL}/employees?farm_id=${currentFarm.id}&include_farm_owners=true`);
        const employeeOptions = response.data.map((emp: any) => ({
          id: emp.id,
          name: `${emp.first_name} ${emp.last_name}`
        }));
        setEmployees(employeeOptions);

        // If the user is an employee, pre-select them
        if (user) {
          const userEmployee = response.data.find((emp: any) => emp.email === user.email);
          if (userEmployee) {
            setFormData(prev => ({ ...prev, employee_id: userEmployee.id }));
          }
        }
      } catch (err: any) {
        console.error('Error fetching employees:', err);
        setError('Failed to load employees. Please try again later.');
      }
    };

    fetchEmployees();
  }, [currentFarm, user]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // If start_date or end_date changes, calculate total_days
    if ((name === 'start_date' || name === 'end_date') && formData.start_date && formData.end_date) {
      const startDate = new Date(name === 'start_date' ? value : formData.start_date);
      const endDate = new Date(name === 'end_date' ? value : formData.end_date);

      // Only calculate if both dates are valid and end date is not before start date
      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime()) && endDate >= startDate) {
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days
        setFormData(prev => ({ ...prev, total_days: diffDays.toString() }));
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.employee_id) {
      setError('Please select an employee.');
      return;
    }

    if (!formData.request_type) {
      setError('Please select a request type.');
      return;
    }

    if (!formData.start_date || !formData.end_date) {
      setError('Start date and end date are required.');
      return;
    }

    const startDate = new Date(formData.start_date);
    const endDate = new Date(formData.end_date);

    if (endDate < startDate) {
      setError('End date cannot be before start date.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Prepare data for submission
      const timeOffRequestData = {
        ...formData,
        // Convert string to number for total_days
        total_days: formData.total_days ? parseFloat(formData.total_days) : null,
        // Set default status to pending
        status: 'pending'
      };

      // Submit the time off request
      await axios.post(`${API_URL}/time-off-requests`, timeOffRequestData);

      // Redirect to time off requests list
      navigate('/hr/time-off-requests');
    } catch (err: any) {
      console.error('Error creating time off request:', err);
      setError('Failed to create time off request. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Request Time Off</h1>
        <Link
          to="/hr/time-off-requests"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Employee */}
            <div>
              <label htmlFor="employee_id" className="block text-sm font-medium text-gray-700 mb-1">
                Employee <span className="text-red-500">*</span>
              </label>
              <select
                id="employee_id"
                name="employee_id"
                value={formData.employee_id}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="">Select Employee</option>
                {employees.map(emp => (
                  <option key={emp.id} value={emp.id}>{emp.name}</option>
                ))}
              </select>
            </div>

            {/* Request Type */}
            <div>
              <label htmlFor="request_type" className="block text-sm font-medium text-gray-700 mb-1">
                Request Type <span className="text-red-500">*</span>
              </label>
              <select
                id="request_type"
                name="request_type"
                value={formData.request_type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="">Select Request Type</option>
                {requestTypes.map(type => (
                  <option key={type} value={type}>{type.charAt(0).toUpperCase() + type.slice(1)}</option>
                ))}
              </select>
            </div>

            {/* Start Date */}
            <div>
              <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="start_date"
                name="start_date"
                value={formData.start_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* End Date */}
            <div>
              <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-1">
                End Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="end_date"
                name="end_date"
                value={formData.end_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Total Days (calculated) */}
            <div>
              <label htmlFor="total_days" className="block text-sm font-medium text-gray-700 mb-1">
                Total Days
              </label>
              <input
                type="number"
                id="total_days"
                name="total_days"
                value={formData.total_days}
                onChange={handleChange}
                min="0.5"
                step="0.5"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                readOnly
              />
              <p className="mt-1 text-xs text-gray-500">Auto-calculated based on start and end dates</p>
            </div>

            {/* Reason */}
            <div className="sm:col-span-2">
              <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
                Reason
              </label>
              <textarea
                id="reason"
                name="reason"
                value={formData.reason}
                onChange={handleChange}
                rows={3}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Briefly explain the reason for your time off request"
              ></textarea>
            </div>

            {/* Notes */}
            <div className="sm:col-span-2">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Additional Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={2}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="Any additional information"
              ></textarea>
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <Link
              to="/hr/time-off-requests"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mr-3"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              {loading ? 'Submitting...' : 'Submit Request'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default NewTimeOffRequestForm;
