import { useState, useContext, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import { API_URL, MAIN_DOMAIN } from '../config';
import RegistrationSuccess from '../components/RegistrationSuccess';

const Register = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [farmName, setFarmName] = useState('');
  const [subdomain, setSubdomain] = useState('');
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [subdomainStatus, setSubdomainStatus] = useState<{
    checking: boolean;
    available: boolean | null;
    message: string;
  }>({
    checking: false,
    available: null,
    message: '',
  });
  const [farmData, setFarmData] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // Get the location to extract query parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const farmSubdomainParam = queryParams.get('farmSubdomain');

  // Determine user type based on whether this is a farm employee registration
  const userType = farmSubdomainParam ? 'farm_employee' : 'farmer';
  const isBusinessOwner = false;

  const { register, loading, error: contextError, clearError, user } = useContext(AuthContext);
  const navigate = useNavigate();

  // Check if user is already logged in
  useEffect(() => {
    if (user) {
      // User is already logged in, redirect to dashboard
      navigate('/dashboard');
    }
  }, [user, navigate]);

  // Function to generate a subdomain from farm name
  const generateSubdomainFromName = (name: string): string => {
    if (!name) return '';

    // Convert to lowercase, replace spaces with hyphens, and remove special characters
    return name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '')
      .replace(/-+/g, '-') // Replace multiple hyphens with a single one
      .replace(/^-|-$/g, ''); // Remove leading and trailing hyphens
  };

  // Function to check subdomain availability
  const checkSubdomainAvailability = async (subdomain: string, callback?: (available: boolean) => void) => {
    if (!subdomain) {
      setSubdomainStatus({
        checking: false,
        available: null,
        message: '',
      });
      if (callback) callback(false);
      return;
    }

    try {
      setSubdomainStatus({
        checking: true,
        available: null,
        message: 'Checking availability...',
      });

      const response = await axios.get(`${API_URL}/farms/subdomain/${subdomain}/check`);

      setSubdomainStatus({
        checking: false,
        available: response.data.available,
        message: response.data.message,
      });

      if (callback) callback(response.data.available);
    } catch (err: any) {
      console.error('Error checking subdomain availability:', err);
      setSubdomainStatus({
        checking: false,
        available: false,
        message: err.response?.data?.message || 'Error checking subdomain availability',
      });

      if (callback) callback(false);
    }
  };

  // Check subdomain and handle redirects
  useEffect(() => {
    const hostname = window.location.hostname;
    const parts = hostname.split('.');

    // Skip for localhost and IP addresses during development
    if (hostname === 'localhost' || /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname)) {
      return;
    }

    // Check if we're on a subdomain
    if (parts.length > 2) {
      const currentSubdomain = parts[0];

      // If we're on the store subdomain, we should use the store-specific register
      if (currentSubdomain === 'store') {
        // Redirect to store register page
        window.location.href = `https://store.${parts.slice(1).join('.')}/store-register`;
        return;
      }

      // If we're on the admin subdomain, redirect to app subdomain for registration
      if (currentSubdomain === 'admin') {
        window.location.href = `https://app.${parts.slice(1).join('.')}/register`;
        return;
      }

      // If we're on a farm subdomain that's not app, redirect to app subdomain
      if (currentSubdomain !== 'www' && currentSubdomain !== 'api' && currentSubdomain !== 'app') {
        window.location.href = `https://app.${parts.slice(1).join('.')}/register?farmSubdomain=${currentSubdomain}`;
        return;
      }
    }
  }, []);

  // Effect to fetch farm data when farmSubdomainParam is present
  useEffect(() => {
    if (farmSubdomainParam) {
      const fetchFarmData = async () => {
        try {
          const response = await axios.get(`${API_URL}/farms/by-subdomain/${farmSubdomainParam}`);
          if (response.data) {
            setFarmData({
              id: response.data.id,
              name: response.data.name
            });
          }
        } catch (err) {
          console.error('Error fetching farm data:', err);
          setError('Could not find farm information. Please try again.');
        }
      };

      fetchFarmData();
    }
  }, [farmSubdomainParam]);

  // Effect to generate subdomain when farm name changes (only for farm owner registration)
  useEffect(() => {
    if (!farmSubdomainParam && farmName) {
      const generatedSubdomain = generateSubdomainFromName(farmName);
      setSubdomain(generatedSubdomain);
      checkSubdomainAvailability(generatedSubdomain, (available) => {
        console.log(`Generated subdomain "${generatedSubdomain}" availability:`, available);
      });
    }
  }, [farmName, farmSubdomainParam]);

  const validatePassword = () => {
    if (password !== confirmPassword) {
      setPasswordError('Passwords do not match');
      return false;
    }

    if (password.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return false;
    }

    if (!acceptTerms) {
      setPasswordError('You must accept the terms and conditions');
      return false;
    }

    setPasswordError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    if (!validatePassword()) {
      return;
    }

    // Different validation based on registration type
    if (!farmSubdomainParam) {
      // Farm owner registration - validate farm name and subdomain
      if (!farmName) {
        setError('Farm name is required');
        return;
      }

      if (!subdomain) {
        setError('Subdomain is required');
        return;
      }

      // Check if subdomain is available
      if (subdomainStatus.available === false) {
        setError('This subdomain is already taken. Please choose another one.');
        return;
      }
    } else {
      // Farm employee registration - validate farm data
      if (!farmData) {
        setError('Farm information could not be loaded. Please try again.');
        return;
      }
    }

    try {
      // Register the user
      const response = await register(email, password, firstName, lastName, phoneNumber, userType, isBusinessOwner, farmName, subdomain);

      // Store email in localStorage for verification page
      localStorage.setItem('userEmail', email);

      if (!farmSubdomainParam) {
        // Farm owner registration - create the farm
        try {
          // Check if response and user exist
          if (!response || !response.user || !response.user.id) {
            console.error('User ID not available after registration');
            setRegistrationSuccess(true); // Still show success even if farm creation might fail
            return;
          }

          const userId = response.user.id;
          const token = response.token;

          // Set registration success to show verification prompt
          setRegistrationSuccess(true);
        } catch (farmErr: any) {
          console.error('Error creating farm:', farmErr);
          // Even if farm creation fails, the user is registered, so show verification prompt
          setRegistrationSuccess(true);
        }
      } else {
        // Farm employee registration - associate user with the farm
        try {
          if (!response || !response.user || !response.user.id) {
            console.error('User ID not available after registration');
            setRegistrationSuccess(true);
            return;
          }

          const userId = response.user.id;
          const token = response.token;

          // Create UserFarm association with the farm as an employee (pending approval)
          await axios.post(`${API_URL}/user-farms`, {
            user_id: userId,
            farm_id: farmData?.id,
            role: 'farm_employee',
            is_approved: false
          }, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });

          // Set registration success to show verification prompt
          setRegistrationSuccess(true);
        } catch (empErr: any) {
          console.error('Error associating user with farm:', empErr);
          // Even if association fails, the user is registered, so show verification prompt
          setRegistrationSuccess(true);
        }
      }
    } catch (err) {
      // Error is handled by the context
      console.error('Registration error:', err);
    }
  };

  if (registrationSuccess) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-card">
          <RegistrationSuccess />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-card">
        <div className="flex flex-col items-center">
          <img
            src="/logo.svg"
            alt="nxtAcre Logo"
            className="h-12 w-auto mb-4"
            onError={(e) => {
              // Fallback if logo doesn't exist
              const target = e.currentTarget;
              target.onerror = null;
              target.style.display = 'none';
              const parent = target.parentElement;
              if (parent) {
                const span = document.createElement('span');
                span.className = 'text-3xl font-display font-bold text-primary-600';
                span.textContent = 'nxtAcre';
                parent.appendChild(span);
              }
            }}
          />
          <h2 className="text-center text-2xl font-display font-bold text-gray-900">
            {registrationSuccess ? 'Registration Successful!' : farmSubdomainParam 
              ? `Join ${farmData?.name || 'Farm'} as an Employee`
              : 'Create your account'
            }
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {registrationSuccess
              ? 'Please check your email to verify your account'
              : farmSubdomainParam
                ? `Sign up to join ${farmData?.name || 'the farm'} as an employee`
                : 'Start managing your farm today with a free 30-day trial'
            }
          </p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {passwordError && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-md" role="alert">
            <span className="block sm:inline">{passwordError}</span>
          </div>
        )}

        <form className="mt-6 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label htmlFor="first-name" className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                <input
                  id="first-name"
                  name="firstName"
                  type="text"
                  required
                  className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                  placeholder="John"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                />
              </div>
              <div>
                <label htmlFor="last-name" className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                <input
                  id="last-name"
                  name="lastName"
                  type="text"
                  required
                  className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                  placeholder="Doe"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                />
              </div>
            </div>

            {!farmSubdomainParam && (
              <>
                <div>
                  <label htmlFor="farm-name" className="block text-sm font-medium text-gray-700 mb-1">Farm Name</label>
                  <input
                    id="farm-name"
                    name="farmName"
                    type="text"
                    required
                    className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                    placeholder="Your Farm Name"
                    value={farmName}
                    onChange={(e) => setFarmName(e.target.value)}
                  />
                </div>

                <div>
                  <label htmlFor="subdomain" className="block text-sm font-medium text-gray-700 mb-1">Farm Subdomain</label>
                  <div className="flex items-center">
                    <input
                      id="subdomain"
                      name="subdomain"
                      type="text"
                      required
                      className={`appearance-none relative block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm ${
                        subdomainStatus.available === false ? 'border-red-300 text-red-900' :
                        subdomainStatus.available === true ? 'border-green-300 text-green-900' :
                        'border-gray-300 text-gray-900'
                      }`}
                      placeholder="your-farm-name"
                      value={subdomain}
                      onChange={(e) => {
                        const value = e.target.value;
                        setSubdomain(value);
                        if (value) {
                          checkSubdomainAvailability(value, (available) => {
                            console.log(`User entered subdomain "${value}" availability:`, available);
                          });
                        }
                      }}
                      pattern="[a-zA-Z0-9-]+"
                      title="Subdomain can only contain letters, numbers, and hyphens"
                    />
                    <span className="ml-2 text-sm">.{MAIN_DOMAIN}</span>
                  </div>
                  {subdomainStatus.checking && (
                    <p className="mt-1 text-xs text-gray-500">
                      Checking availability...
                    </p>
                  )}
                  {!subdomainStatus.checking && subdomainStatus.message && (
                    <p className={`mt-1 text-xs ${
                      subdomainStatus.available ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {subdomainStatus.message}
                    </p>
                  )}
                  {!subdomainStatus.checking && !subdomainStatus.message && (
                    <p className="mt-1 text-xs text-gray-500">
                      Choose a subdomain for your farm. This will be used to access your farm's dashboard.
                    </p>
                  )}
                </div>
              </>
            )}

            {farmSubdomainParam && farmData && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                <p className="text-sm text-blue-800">
                  You are registering as an employee for <strong>{farmData.name}</strong>
                </p>
              </div>
            )}

            <div>
              <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 mb-1">Email address</label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="phone-number" className="block text-sm font-medium text-gray-700 mb-1">Phone Number (optional)</label>
              <input
                id="phone-number"
                name="phoneNumber"
                type="tel"
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="(*************"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">Password</label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
              <p className="mt-1 text-xs text-gray-500">Must be at least 8 characters long</p>
            </div>
            <div>
              <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
              <input
                id="confirm-password"
                name="confirmPassword"
                type="password"
                autoComplete="new-password"
                required
                className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
                placeholder="••••••••"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>

            <div className="mt-4">
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="terms"
                    name="terms"
                    type="checkbox"
                    checked={acceptTerms}
                    onChange={(e) => setAcceptTerms(e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="terms" className="font-medium text-gray-700">
                    I accept the{' '}
                    <a href="https://www.nxtacre.com/terms-and-conditions" target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-700">
                      Terms and Conditions
                    </a>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className={`btn btn-primary w-full ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Creating account...' : 'Create account'}
            </button>
          </div>

          <div className="text-center pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-4">
              Already have an account?{' '}
              <Link to="/login" className="font-medium text-primary-600 hover:text-primary-700">
                Sign in
              </Link>
            </p>

            {!farmSubdomainParam && (
              <p className="text-sm text-gray-600 mt-4 pt-4 border-t border-gray-200">
                Are you a supplier, vendor, or vet?{' '}<br/>
                <Link to="/business-register" className="font-medium text-primary-600 hover:text-primary-700">
                  Register a business account
                </Link>
              </p>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default Register;
