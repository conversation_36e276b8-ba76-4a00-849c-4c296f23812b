import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';
import { HelpTip } from '../../context/HelpContext';
import { QuestionMarkCircleIcon, PencilIcon, TrashIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';

const HelpTipManagement: React.FC = () => {
  const [helpTips, setHelpTips] = useState<HelpTip[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [currentTip, setCurrentTip] = useState<HelpTip | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    pagePath: '',
    elementSelector: '',
    position: 'top' as 'top' | 'right' | 'bottom' | 'left',
    order: 1,
    isActive: true
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch help tips
        const response = await axios.get(`${API_URL}/help/tips`);
        setHelpTips(Array.isArray(response.data.tips) ? response.data.tips : []);

        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching help tips:', err);
        setError(err.response?.data?.error || 'Failed to load help tips');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleAddTip = () => {
    setFormData({
      title: '',
      content: '',
      pagePath: '',
      elementSelector: '',
      position: 'top',
      order: 1,
      isActive: true
    });
    setShowAddModal(true);
  };

  const handleEditTip = (tip: HelpTip) => {
    setCurrentTip(tip);
    setFormData({
      title: tip.title,
      content: tip.content,
      pagePath: tip.pagePath,
      elementSelector: tip.elementSelector || '',
      position: tip.position,
      order: tip.order,
      isActive: tip.isActive
    });
    setShowEditModal(true);
  };

  const handleDeleteTip = (tip: HelpTip) => {
    setCurrentTip(tip);
    setShowDeleteModal(true);
  };

  const confirmDeleteTip = async () => {
    if (!currentTip) return;

    try {
      setError(null);

      // Delete the tip
      await axios.delete(`${API_URL}/help/tips/${currentTip.id}`);

      // Update the tips list
      setHelpTips(helpTips.filter(tip => tip.id !== currentTip.id));
      setShowDeleteModal(false);
    } catch (err: any) {
      console.error('Error deleting help tip:', err);
      setError(err.response?.data?.error || 'Failed to delete help tip');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleSubmitAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError(null);

      // Create the help tip
      const response = await axios.post(`${API_URL}/help/tips`, {
        title: formData.title,
        content: formData.content,
        pagePath: formData.pagePath,
        elementSelector: formData.elementSelector || null,
        position: formData.position,
        order: formData.order,
        isActive: formData.isActive
      });

      // Add the new tip to the list
      setHelpTips([...helpTips, response.data.tip]);
      setShowAddModal(false);
    } catch (err: any) {
      console.error('Error creating help tip:', err);
      setError(err.response?.data?.error || 'Failed to create help tip');
    }
  };

  const handleSubmitEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentTip) return;

    try {
      setError(null);

      // Update the help tip
      const response = await axios.put(`${API_URL}/help/tips/${currentTip.id}`, {
        title: formData.title,
        content: formData.content,
        pagePath: formData.pagePath,
        elementSelector: formData.elementSelector || null,
        position: formData.position,
        order: formData.order,
        isActive: formData.isActive
      });

      // Update the tips list
      setHelpTips(helpTips.map(tip => (tip.id === currentTip.id ? response.data.tip : tip)));
      setShowEditModal(false);
    } catch (err: any) {
      console.error('Error updating help tip:', err);
      setError(err.response?.data?.error || 'Failed to update help tip');
    }
  };

  const toggleTipActive = async (tip: HelpTip) => {
    try {
      setError(null);

      // Update the help tip's active status
      const response = await axios.put(`${API_URL}/help/tips/${tip.id}`, {
        ...tip,
        isActive: !tip.isActive
      });

      // Update the tips list
      setHelpTips(helpTips.map(t => (t.id === tip.id ? response.data.tip : t)));
    } catch (err: any) {
      console.error('Error toggling help tip active status:', err);
      setError(err.response?.data?.error || 'Failed to update help tip');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Help Tip Management</h1>
        <button
          onClick={handleAddTip}
          className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
        >
          Add New Help Tip
        </button>
      </div>

      {loading ? (
        <div className="text-center py-4">
          <p>Loading help tips...</p>
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Title
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Page Path
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Position
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {helpTips.map(tip => (
                <tr key={tip.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <QuestionMarkCircleIcon className="h-5 w-5 text-primary-500 mr-2" />
                      <div className="text-sm font-medium text-gray-900">{tip.title}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{tip.pagePath}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{tip.position}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{tip.order}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        tip.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {tip.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => toggleTipActive(tip)}
                      className={`text-${tip.isActive ? 'red' : 'green'}-600 hover:text-${
                        tip.isActive ? 'red' : 'green'
                      }-900 mr-3`}
                    >
                      {tip.isActive ? (
                        <XCircleIcon className="h-5 w-5" title="Deactivate" />
                      ) : (
                        <CheckCircleIcon className="h-5 w-5" title="Activate" />
                      )}
                    </button>
                    <button
                      onClick={() => handleEditTip(tip)}
                      className="text-indigo-600 hover:text-indigo-900 mr-3"
                    >
                      <PencilIcon className="h-5 w-5" title="Edit" />
                    </button>
                    <button
                      onClick={() => handleDeleteTip(tip)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <TrashIcon className="h-5 w-5" title="Delete" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Add Help Tip Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Add New Help Tip</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>
            <form onSubmit={handleSubmitAdd}>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="title">
                  Title
                </label>
                <input
                  type="text"
                  name="title"
                  id="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="content">
                  Content
                </label>
                <textarea
                  name="content"
                  id="content"
                  value={formData.content}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  rows={4}
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="pagePath">
                  Page Path
                </label>
                <input
                  type="text"
                  name="pagePath"
                  id="pagePath"
                  value={formData.pagePath}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                  placeholder="/dashboard"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="elementSelector">
                  Element Selector (optional)
                </label>
                <input
                  type="text"
                  name="elementSelector"
                  id="elementSelector"
                  value={formData.elementSelector}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  placeholder=".dashboard-header"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="position">
                  Position
                </label>
                <select
                  name="position"
                  id="position"
                  value={formData.position}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                >
                  <option value="top">Top</option>
                  <option value="right">Right</option>
                  <option value="bottom">Bottom</option>
                  <option value="left">Left</option>
                </select>
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="order">
                  Order
                </label>
                <input
                  type="number"
                  name="order"
                  id="order"
                  value={formData.order}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                  min="1"
                />
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleCheckboxChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Active</span>
                </label>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Add Help Tip
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Help Tip Modal */}
      {showEditModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Edit Help Tip</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <XCircleIcon className="h-6 w-6" />
              </button>
            </div>
            <form onSubmit={handleSubmitEdit}>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-title">
                  Title
                </label>
                <input
                  type="text"
                  name="title"
                  id="edit-title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-content">
                  Content
                </label>
                <textarea
                  name="content"
                  id="edit-content"
                  value={formData.content}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  rows={4}
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-pagePath">
                  Page Path
                </label>
                <input
                  type="text"
                  name="pagePath"
                  id="edit-pagePath"
                  value={formData.pagePath}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-elementSelector">
                  Element Selector (optional)
                </label>
                <input
                  type="text"
                  name="elementSelector"
                  id="edit-elementSelector"
                  value={formData.elementSelector}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-position">
                  Position
                </label>
                <select
                  name="position"
                  id="edit-position"
                  value={formData.position}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                >
                  <option value="top">Top</option>
                  <option value="right">Right</option>
                  <option value="bottom">Bottom</option>
                  <option value="left">Left</option>
                </select>
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="edit-order">
                  Order
                </label>
                <input
                  type="number"
                  name="order"
                  id="edit-order"
                  value={formData.order}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                  min="1"
                />
              </div>
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleCheckboxChange}
                    className="form-checkbox h-5 w-5 text-primary-600"
                  />
                  <span className="ml-2 text-gray-700">Active</span>
                </label>
              </div>
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
                >
                  Update Help Tip
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Help Tip Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <TrashIcon className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mt-2">Delete Help Tip</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete this help tip? This action cannot be undone.
                </p>
              </div>
              <div className="flex justify-center mt-4">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(false)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={confirmDeleteTip}
                  className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HelpTipManagement;