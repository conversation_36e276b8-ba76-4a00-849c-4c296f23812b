import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { Link } from 'react-router-dom';
import LoadingSpinner from '../../components/LoadingSpinner';
import axios from 'axios';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';
import {
  getFarmHarvestSchedules, 
  createHarvestSchedule, 
  updateHarvestSchedule, 
  getStatusBadgeColor,
  formatDate,
  HarvestSchedule as HarvestScheduleType
} from '../../services/harvestScheduleService';

interface HarvestScheduleAnalysis {
  id: string;
  farm_id: string;
  field_id?: string;
  crop_id?: string;
  planting_date?: string;
  crop_maturity_data?: any;
  recommended_harvest_window: {
    start_date: string;
    end_date: string;
  };
  optimal_harvest_date: string;
  weather_considerations: string;
  equipment_availability_impact: string;
  quality_impact: string;
  yield_impact: string;
  confidence_score: number;
  is_applied: boolean;
  created_at: string;
  updated_at: string;
}

interface Crop {
  id: string;
  name: string;
  crop_type: string;
  field_id: string;
  field_name?: string;
  planted_date?: string;
  expected_harvest_date?: string;
}

// Using HarvestScheduleType from the service

const HarvestScheduling = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [crops, setCrops] = useState<Crop[]>([]);
  const [schedules, setSchedules] = useState<HarvestScheduleType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCropId, setSelectedCropId] = useState<string>('');
  const [harvestDate, setHarvestDate] = useState<string>('');
  const [estimatedYield, setEstimatedYield] = useState<string>('');
  const [yieldUnit, setYieldUnit] = useState<string>('bushels');
  const [notes, setNotes] = useState<string>('');

  // AI Analysis state
  const [analyses, setAnalyses] = useState<HarvestScheduleAnalysis[]>([]);
  const [analysisLoading, setAnalysisLoading] = useState<boolean>(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [selectedFieldId, setSelectedFieldId] = useState<string>('');

  useEffect(() => {
    if (user && currentFarm) {
      fetchCrops();
      fetchHarvestSchedules();
      fetchHarvestScheduleAnalyses();
    }
  }, [user, currentFarm]);

  const fetchHarvestScheduleAnalyses = async () => {
    try {
      setAnalysisLoading(true);
      if (!currentFarm) {
        setAnalysisError('No farm selected');
        return;
      }

      const response = await axios.get(`${API_URL}/ai-analysis/harvest-schedule/${currentFarm.id}/all`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      if (response.data.success) {
        setAnalyses(response.data.analyses || []);
      } else {
        throw new Error(response.data.message || 'Failed to fetch harvest schedule analyses');
      }
    } catch (err) {
      console.error('Error fetching harvest schedule analyses:', err);
      setAnalysisError('Failed to load harvest schedule analyses. Please try again later.');

      // Fallback to empty array if API call fails
      setAnalyses([]);
    } finally {
      setAnalysisLoading(false);
    }
  };

  const generateHarvestScheduleAnalysis = async () => {
    if (!selectedCropId && !selectedFieldId) {
      setAnalysisError('Please select a crop or field to generate a harvest schedule analysis');
      return;
    }

    try {
      setAnalysisLoading(true);

      // Get field ID from selected crop if not directly selected
      let fieldId = selectedFieldId;
      if (!fieldId && selectedCropId) {
        const selectedCrop = crops.find(crop => crop.id === selectedCropId);
        if (selectedCrop && selectedCrop.field_id) {
          fieldId = selectedCrop.field_id;
        }
      }

      // Send request to the backend to generate a new harvest schedule analysis
      const response = await axios.post(`${API_URL}/ai-analysis/harvest-schedule`, {
        farmId: currentFarm.id,
        fieldId: fieldId || null,
        cropId: selectedCropId || null,
        harvestData: {
          plantingDate: null, // Could be added as an input field if needed
          cropMaturityData: {} // Could be added as input fields if needed
        }
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      if (response.data.success) {
        // Add the new analysis to the beginning of the list
        setAnalyses([response.data.analysis, ...analyses]);
        setSelectedFieldId('');
        setSelectedCropId('');
        setAnalysisError(null);
      } else {
        throw new Error(response.data.message || 'Failed to generate harvest schedule analysis');
      }
    } catch (err) {
      console.error('Error generating harvest schedule analysis:', err);
      setAnalysisError('Failed to generate harvest schedule analysis. Please try again later.');
    } finally {
      setAnalysisLoading(false);
    }
  };

  const applyHarvestScheduleAnalysis = async (analysisId: string) => {
    try {
      setAnalysisLoading(true);

      // Send request to apply the analysis
      const response = await axios.put(`${API_URL}/ai-analysis/harvest-schedule/${analysisId}`, {
        isApplied: true
      }, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });

      if (response.data.success) {
        // Update the analyses list to reflect the change
        setAnalyses(analyses.map(analysis => 
          analysis.id === analysisId 
            ? { ...analysis, is_applied: true } 
            : analysis
        ));

        // Create a harvest schedule based on the analysis
        const analysis = analyses.find(a => a.id === analysisId);
        if (analysis) {
          // Find the crop associated with the analysis
          let cropId = analysis.crop_id;
          if (!cropId && analysis.field_id) {
            // If no crop ID is directly associated, try to find a crop in the field
            const fieldCrops = crops.filter(crop => crop.field_id === analysis.field_id);
            if (fieldCrops.length > 0) {
              cropId = fieldCrops[0].id;
            }
          }

          if (cropId && analysis.field_id) {
            await createHarvestSchedule({
              farmId: currentFarm.id,
              fieldId: analysis.field_id,
              cropId: cropId,
              scheduledDate: analysis.optimal_harvest_date,
              status: 'planned',
              weatherDependent: true,
              notes: `AI-recommended harvest date. Weather considerations: ${analysis.weather_considerations}`,
              optimalConditions: {
                min_temperature: 55,
                max_temperature: 85,
                max_precipitation_chance: 20,
                max_wind_speed: 15,
                preferred_conditions: ['Clear', 'Clouds']
              }
            });

            // Refresh the schedules list
            await fetchHarvestSchedules();
          }
        }

        setAnalysisError(null);
      } else {
        throw new Error(response.data.message || 'Failed to apply harvest schedule analysis');
      }
    } catch (err) {
      console.error('Error applying harvest schedule analysis:', err);
      setAnalysisError('Failed to apply harvest schedule analysis. Please try again later.');
    } finally {
      setAnalysisLoading(false);
    }
  };

  const fetchCrops = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_URL}/crops?farm_id=${currentFarm.id}`, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`
        }
      });
      setCrops(Array.isArray(response.data) ? response.data : response.data.crops || []);
    } catch (err) {
      console.error('Error fetching crops:', err);
      setError('Failed to load crops. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const fetchHarvestSchedules = async () => {
    try {
      setLoading(true);
      if (!currentFarm) {
        setError('No farm selected');
        return;
      }

      const harvestSchedules = await getFarmHarvestSchedules(currentFarm.id);
      setSchedules(harvestSchedules);
      setError(null);
    } catch (err) {
      console.error('Error fetching harvest schedules:', err);
      setError('Failed to load harvest schedules. Please try again later.');
      setSchedules([]);
    } finally {
      setLoading(false);
    }
  };

  const createHarvestScheduleHandler = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedCropId || !harvestDate || !currentFarm) {
      setError('Please select a crop and harvest date');
      return;
    }

    try {
      setLoading(true);
      const selectedCrop = crops.find(crop => crop.id === selectedCropId);

      if (!selectedCrop || !selectedCrop.field_id) {
        setError('Selected crop does not have a valid field');
        return;
      }

      const response = await createHarvestSchedule({
        farmId: currentFarm.id,
        fieldId: selectedCrop.field_id,
        cropId: selectedCropId,
        scheduledDate: harvestDate,
        status: 'planned',
        weatherDependent: true,
        notes: notes,
        optimalConditions: {
          min_temperature: 55,
          max_temperature: 85,
          max_precipitation_chance: 20,
          max_wind_speed: 15,
          preferred_conditions: ['Clear', 'Clouds']
        }
      });

      // Refresh the schedules list
      await fetchHarvestSchedules();

      // Reset form
      setSelectedCropId('');
      setHarvestDate('');
      setEstimatedYield('');
      setYieldUnit('bushels');
      setNotes('');
      setError(null);
    } catch (err) {
      console.error('Error creating harvest schedule:', err);
      setError('Failed to create harvest schedule. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const updateScheduleStatus = async (scheduleId: string, newStatus: HarvestScheduleType['status']) => {
    try {
      setLoading(true);

      // Update the schedule status in the backend
      await updateHarvestSchedule(scheduleId, { status: newStatus });

      // Refresh the schedules list
      await fetchHarvestSchedules();

      setError(null);
    } catch (err) {
      console.error('Error updating harvest schedule status:', err);
      setError('Failed to update harvest schedule status. Please try again later.');

      // Update the UI optimistically even if the API call fails
      setSchedules(schedules.map(schedule => 
        schedule.id === scheduleId 
          ? { ...schedule, status: newStatus } 
          : schedule
      ));
    } finally {
      setLoading(false);
    }
  };

  // Using getStatusBadgeColor from the service

  if (loading && crops.length === 0 && schedules.length === 0) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-full">
          <LoadingSpinner />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Harvest Scheduling</h1>
            <p className="mt-2 text-sm text-gray-700">
              Plan and manage harvest schedules for your crops.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <Link
              to="/crops"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              View All Crops
            </Link>
          </div>
        </div>

        {error && (
          <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Create New Harvest Schedule</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>Schedule a harvest for one of your crops.</p>
            </div>
            <form onSubmit={createHarvestScheduleHandler} className="mt-5 space-y-4">
              <div className="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6">
                <div className="sm:col-span-3">
                  <label htmlFor="crop" className="block text-sm font-medium text-gray-700">Crop</label>
                  <select
                    id="crop"
                    name="crop"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    value={selectedCropId}
                    onChange={(e) => setSelectedCropId(e.target.value)}
                    required
                  >
                    <option value="">Select a crop</option>
                    {crops.map((crop) => (
                      <option key={crop.id} value={crop.id}>
                        {crop.name} ({crop.field_name || 'Unknown Field'})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="sm:col-span-3">
                  <label htmlFor="harvest-date" className="block text-sm font-medium text-gray-700">Planned Harvest Date</label>
                  <input
                    type="date"
                    name="harvest-date"
                    id="harvest-date"
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={harvestDate}
                    onChange={(e) => setHarvestDate(e.target.value)}
                    required
                  />
                </div>

                <div className="sm:col-span-2">
                  <label htmlFor="estimated-yield" className="block text-sm font-medium text-gray-700">Estimated Yield</label>
                  <input
                    type="number"
                    name="estimated-yield"
                    id="estimated-yield"
                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={estimatedYield}
                    onChange={(e) => setEstimatedYield(e.target.value)}
                    placeholder="0"
                    min="0"
                    step="0.1"
                  />
                </div>

                <div className="sm:col-span-2">
                  <label htmlFor="yield-unit" className="block text-sm font-medium text-gray-700">Yield Unit</label>
                  <select
                    id="yield-unit"
                    name="yield-unit"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    value={yieldUnit}
                    onChange={(e) => setYieldUnit(e.target.value)}
                  >
                    <option value="bushels">Bushels</option>
                    <option value="bushels/acre">Bushels/Acre</option>
                    <option value="tons">Tons</option>
                    <option value="tons/acre">Tons/Acre</option>
                    <option value="pounds">Pounds</option>
                    <option value="pounds/acre">Pounds/Acre</option>
                    <option value="kilograms">Kilograms</option>
                    <option value="kilograms/hectare">Kilograms/Hectare</option>
                  </select>
                </div>

                <div className="sm:col-span-6">
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700">Notes</label>
                  <textarea
                    id="notes"
                    name="notes"
                    rows={3}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Add any notes about this harvest schedule"
                  />
                </div>
              </div>

              <div className="pt-5">
                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedCropId('');
                      setHarvestDate('');
                      setEstimatedYield('');
                      setYieldUnit('bushels');
                      setNotes('');
                    }}
                    className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Create Schedule
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>

        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">AI-Driven Harvest Schedule Recommendations</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>Generate AI-powered recommendations for optimal harvest timing based on crop, field, and weather data.</p>
            </div>

            {analysisError && (
              <div className="mt-4 bg-red-50 border-l-4 border-red-400 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-700">{analysisError}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="mt-5 sm:flex sm:items-center">
              <div className="w-full sm:max-w-xs">
                <label htmlFor="field" className="sr-only">Field</label>
                <select
                  id="field"
                  name="field"
                  className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                  value={selectedFieldId}
                  onChange={(e) => setSelectedFieldId(e.target.value)}
                >
                  <option value="">Select a field</option>
                  {crops.reduce((fields, crop) => {
                    if (crop.field_id && !fields.some(field => field.id === crop.field_id)) {
                      fields.push({ id: crop.field_id, name: crop.field_name || 'Unknown Field' });
                    }
                    return fields;
                  }, [] as { id: string; name: string }[]).map((field) => (
                    <option key={field.id} value={field.id}>
                      {field.name}
                    </option>
                  ))}
                </select>
              </div>
              <button
                type="button"
                onClick={generateHarvestScheduleAnalysis}
                disabled={analysisLoading}
                className="mt-3 w-full inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                {analysisLoading ? 'Generating...' : 'Generate Recommendations'}
              </button>
            </div>
          </div>
        </div>

        {analyses.length > 0 && (
          <div className="mt-8">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Harvest Schedule Recommendations</h3>
            <div className="space-y-6">
              {analyses.map((analysis) => (
                <div key={analysis.id} className="bg-white shadow overflow-hidden sm:rounded-lg">
                  <div className="px-4 py-5 sm:px-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Harvest Schedule Analysis
                      {analysis.field_id && crops.find(crop => crop.field_id === analysis.field_id) && 
                        ` - ${crops.find(crop => crop.field_id === analysis.field_id)?.field_name || 'Unknown Field'}`}
                    </h3>
                    <p className="mt-1 max-w-2xl text-sm text-gray-500">
                      Created: {new Date(analysis.created_at).toLocaleDateString()}
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        AI Confidence: {Math.round(analysis.confidence_score)}%
                      </span>
                    </p>
                  </div>
                  <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
                    <dl className="sm:divide-y sm:divide-gray-200">
                      <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Recommended Harvest Window</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {new Date(analysis.recommended_harvest_window.start_date).toLocaleDateString()} to {new Date(analysis.recommended_harvest_window.end_date).toLocaleDateString()}
                        </dd>
                      </div>
                      <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Optimal Harvest Date</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          <span className="px-2 py-1 bg-primary-100 text-primary-800 rounded">
                            {new Date(analysis.optimal_harvest_date).toLocaleDateString()}
                          </span>
                        </dd>
                      </div>
                      <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Weather Considerations</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {analysis.weather_considerations}
                        </dd>
                      </div>
                      <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Equipment Availability Impact</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {analysis.equipment_availability_impact}
                        </dd>
                      </div>
                      <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Quality Impact</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {analysis.quality_impact}
                        </dd>
                      </div>
                      <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt className="text-sm font-medium text-gray-500">Yield Impact</dt>
                        <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                          {analysis.yield_impact}
                        </dd>
                      </div>
                    </dl>
                  </div>
                  <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
                    {!analysis.is_applied && (
                      <button
                        type="button"
                        onClick={() => applyHarvestScheduleAnalysis(analysis.id)}
                        disabled={analysisLoading}
                        className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        Apply Recommendations
                      </button>
                    )}
                    {analysis.is_applied && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Applied
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="mt-8">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Harvest Schedules</h3>
          {schedules.length === 0 ? (
            <div className="bg-white shadow overflow-hidden sm:rounded-lg">
              <div className="px-4 py-5 sm:p-6 text-center text-gray-500">
                No harvest schedules available. Create a new schedule to get started.
              </div>
            </div>
          ) : (
            <div className="flex flex-col">
              <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                  <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Crop
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Field
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Harvest Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Estimated Yield
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {schedules.map((schedule) => (
                          <tr key={schedule.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {schedule.crop?.name || 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {schedule.field?.name || 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatDate(schedule.scheduled_date)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {schedule.yield_amount || 'N/A'} {schedule.yield_unit || ''}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(schedule.status)}`}>
                                {schedule.status.replace('_', ' ').charAt(0).toUpperCase() + schedule.status.replace('_', ' ').slice(1)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <div className="flex space-x-2">
                                {schedule.status === 'planned' && (
                                  <>
                                    <button
                                      onClick={() => updateScheduleStatus(schedule.id, 'in_progress')}
                                      className="text-indigo-600 hover:text-indigo-900"
                                    >
                                      Start
                                    </button>
                                    <button
                                      onClick={() => updateScheduleStatus(schedule.id, 'cancelled')}
                                      className="text-red-600 hover:text-red-900"
                                    >
                                      Cancel
                                    </button>
                                  </>
                                )}
                                {schedule.status === 'in_progress' && (
                                  <button
                                    onClick={() => updateScheduleStatus(schedule.id, 'completed')}
                                    className="text-green-600 hover:text-green-900"
                                  >
                                    Complete
                                  </button>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default HarvestScheduling;
