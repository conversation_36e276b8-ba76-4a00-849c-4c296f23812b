import React, { createContext, useState, useEffect, useContext, ReactNode, useCallback, useRef } from 'react';
import axios from 'axios';
import { useLocation } from 'react-router-dom';
import { AuthContext } from './AuthContext';
import { FarmContext } from './FarmContext';
import { API_URL } from '../config';
import { checkPermission } from '../services/rolePermissionService';
import { getPluginWidgets } from '../services/pluginWidgetService';

// Define the shape of a widget
export interface Widget {
  id: string;
  type: string;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  title: string;
  [key: string]: any; // Allow additional properties
}

// Define the shape of the layout configuration
export interface LayoutConfig {
  widgets: Widget[];
  layout: {
    cols: number;
    rowHeight: number;
    gap: number;
  };
}

// Define the shape of a saved layout
export interface SavedLayout {
  id: string;
  name: string;
  isActive: boolean;
  updatedAt: string;
}

// Define the shape of our context
interface DashboardContextType {
  dashboardLayout: LayoutConfig | null;
  isDefaultLayout: boolean;
  isLoading: boolean;
  error: string | null;
  availableLayouts: SavedLayout[];
  currentLayoutName: string;
  currentLayoutId: string | null;
  saveDashboardLayout: (layout: LayoutConfig) => Promise<void>;
  saveAsNewLayout: (layout: LayoutConfig, name: string) => Promise<void>;
  setActiveLayout: (layoutId: string) => Promise<void>;
  fetchAvailableLayouts: () => Promise<void>;
  resetToFarmDefault: () => Promise<void>;
  deleteLayout: (layoutId: string) => Promise<void>;
  addWidget: (widget: Widget) => void;
  removeWidget: (widgetId: string) => void;
  updateWidgetPosition: (widgetId: string, position: { x: number; y: number; w: number; h: number }) => void;
  clearError: () => void;
}

// Create the context with a default value
export const DashboardContext = createContext<DashboardContextType>({
  dashboardLayout: null,
  isDefaultLayout: true,
  isLoading: false,
  error: null,
  availableLayouts: [],
  currentLayoutName: 'Default',
  currentLayoutId: null,
  saveDashboardLayout: async () => {},
  saveAsNewLayout: async () => {},
  setActiveLayout: async () => {},
  fetchAvailableLayouts: async () => {},
  resetToFarmDefault: async () => {},
  deleteLayout: async () => {},
  addWidget: () => {},
  removeWidget: () => {},
  updateWidgetPosition: () => {},
  clearError: () => {},
});

// Provider component
export const DashboardProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, token } = useContext(AuthContext);
  const { currentFarm } = useContext(FarmContext);
  const location = useLocation();

  const [dashboardLayout, setDashboardLayout] = useState<LayoutConfig | null>(null);
  const [isDefaultLayout, setIsDefaultLayout] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [availableLayouts, setAvailableLayouts] = useState<SavedLayout[]>([]);
  const [currentLayoutId, setCurrentLayoutId] = useState<string | null>(null);
  const [currentLayoutName, setCurrentLayoutName] = useState<string>('Default');

  // Fetch dashboard layout when user, current farm, or location changes
  useEffect(() => {
    // Skip dashboard API call if on a global admin page
    const isGlobalAdminPage = location.pathname.startsWith('/admin/');

    if (user && token && !isGlobalAdminPage) {
      fetchDashboardLayout();
      fetchAvailableLayouts();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, token, currentFarm, location.pathname]);

  // Default dashboard layout
  const getDefaultDashboardLayout = (): LayoutConfig => {
    return {
      widgets: [
        {
          id: 'getting-started',
          type: 'getting-started',
          position: { x: 0, y: 0, w: 12, h: 6 },
          title: 'Getting Started'
        },
        {
          id: 'accounts',
          type: 'accounts',
          position: { x: 0, y: 6, w: 8, h: 4 },
          title: 'Accounts'
        },
        {
          id: 'quick-actions',
          type: 'quick-actions',
          position: { x: 8, y: 6, w: 4, h: 4 },
          title: 'Quick Actions',
          actions: [
            { id: '1', label: 'View Transactions', link: '/transactions' },
            { id: '2', label: 'Manage Invoices', link: '/invoices' },
            { id: '3', label: 'View Customers', link: '/customers' },
            { id: '4', label: 'Manage Farms', link: '/farms' }
          ]
        },
        {
          id: 'weather',
          type: 'weather',
          position: { x: 0, y: 10, w: 12, h: 4 },
          title: 'Weather'
        },
        {
          id: 'recent-transactions',
          type: 'recent-transactions',
          position: { x: 0, y: 14, w: 12, h: 4 },
          title: 'Recent Transactions'
        }
      ],
      layout: {
        cols: 12,
        rowHeight: 50,
        gap: 20
      }
    };
  };

  // Map widget types to features for permission checking
  const widgetFeatureMap: { [key: string]: string } = {
    'weather': 'weather',
    'accounts': 'finances',
    'recent-transactions': 'finances',
    'field-health': 'fields',
    'market-prices': 'market_prices',
    'task-status': 'tasks',
    'inventory-alerts': 'inventory',
    'financial-summary': 'finances',
    'inventory-levels': 'inventory',
    'task-completion': 'tasks',
    'weather-trends': 'weather'
    // 'quick-actions' has no feature restriction
  };

  // Filter widgets based on user permissions
  const filterWidgetsByPermission = async (widgets: Widget[]): Promise<Widget[]> => {
    if (!user || !token || !currentFarm) return widgets;

    try {
      const filteredWidgets = [];

      for (const widget of widgets) {
        // If the widget type has no feature mapping, everyone has access
        if (!widgetFeatureMap[widget.type]) {
          filteredWidgets.push(widget);
          continue;
        }

        // Check if user has permission to view this feature
        try {
          const hasPermission = await checkPermission(
            currentFarm.id,
            user.id,
            widgetFeatureMap[widget.type],
            'view'
          );

          if (hasPermission) {
            filteredWidgets.push(widget);
          }
        } catch (err) {
          console.error(`Error checking permission for ${widgetFeatureMap[widget.type]}:`, err);
          // If there's an error checking permission, assume the user has access
          // This is a fallback to ensure widgets aren't hidden incorrectly
          filteredWidgets.push(widget);
        }
      }

      return filteredWidgets;
    } catch (err) {
      console.error('Error filtering widgets by permission:', err);
      // If there's an error, show all widgets as a fallback
      return widgets;
    }
  };

  // Fetch all available layouts for the user
  const fetchAvailableLayouts = async () => {
    if (!user || !token) return;

    // Double-check we're not on a global admin page
    if (location.pathname.startsWith('/admin/')) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(`${API_URL}/dashboard/user/${user.id}/layouts`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data && response.data.layouts) {
        setAvailableLayouts(response.data.layouts);
      }
    } catch (err: any) {
      console.error('Error fetching available layouts:', err);
      setError(err.response?.data?.error || 'Failed to load available layouts');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch dashboard layout from the API
  const fetchDashboardLayout = async (layoutId?: string) => {
    if (!user) return;

    // Double-check we're not on a global admin page
    if (location.pathname.startsWith('/admin/')) {
      console.log('Skipping dashboard API call on global admin page:', location.pathname);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Fetch plugin widgets from enabled plugins
      let pluginWidgets: Widget[] = [];
      try {
        pluginWidgets = await getPluginWidgets(currentFarm?.id);
        console.log('Loaded widgets from plugins:', pluginWidgets);
      } catch (pluginErr) {
        console.error('Error loading plugin widgets:', pluginErr);
      }

      // Construct URL with optional layoutId parameter
      const url = layoutId
        ? `${API_URL}/dashboard/user/${user.id}?layoutId=${layoutId}`
        : `${API_URL}/dashboard/user/${user.id}`;

      const response = await axios.get(url, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.layout && response.data.layout.widgets && response.data.layout.widgets.length > 0) {
        // Filter widgets based on user permissions
        const filteredWidgets = await filterWidgetsByPermission(response.data.layout.widgets);

        // Remove any plugin widgets that might be hardcoded in the layout
        const nonPluginWidgets = filteredWidgets.filter(widget =>
          widget.type !== 'field-health' && widget.type !== 'market-prices'
        );

        // Combine user widgets with plugin widgets
        const combinedWidgets = [...nonPluginWidgets, ...pluginWidgets];

        // Update the layout with filtered and plugin widgets
        const filteredLayout = {
          ...response.data.layout,
          widgets: combinedWidgets
        };

        setDashboardLayout(filteredLayout);
        setIsDefaultLayout(response.data.isDefault);

        // Update current layout info if available
        if (response.data.id) {
          setCurrentLayoutId(response.data.id);
          setCurrentLayoutName(response.data.name || 'Default');
        } else {
          setCurrentLayoutId(null);
          setCurrentLayoutName('Default');
        }
      } else {
        // This should never happen as the backend should always return a layout
        // (either user custom, farm default, global default, or hardcoded default)
        console.error('API returned empty layout, which should not happen');
        const defaultLayout = getDefaultDashboardLayout();

        // Filter default widgets based on user permissions
        const filteredWidgets = await filterWidgetsByPermission(defaultLayout.widgets);

        // Remove any plugin widgets that might be hardcoded in the default layout
        const nonPluginWidgets = filteredWidgets.filter(widget =>
          widget.type !== 'field-health' && widget.type !== 'market-prices'
        );

        // Combine default widgets with plugin widgets
        const combinedWidgets = [...nonPluginWidgets, ...pluginWidgets];

        // Update the default layout with filtered and plugin widgets
        const filteredLayout = {
          ...defaultLayout,
          widgets: combinedWidgets
        };

        setDashboardLayout(filteredLayout);
        setIsDefaultLayout(true);
        setCurrentLayoutId(null);
        setCurrentLayoutName('Default');
      }
    } catch (err: any) {
      console.error('Error fetching dashboard layout:', err);
      setError(err.response?.data?.error || 'Failed to load dashboard layout');

      // If there's an error, use the default layout
      console.log('Error fetching layout, using default layout');
      const defaultLayout = getDefaultDashboardLayout();

      // Filter default widgets based on user permissions
      const filteredWidgets = await filterWidgetsByPermission(defaultLayout.widgets);

      // Fetch plugin widgets from enabled plugins
      let pluginWidgets: Widget[] = [];
      try {
        pluginWidgets = await getPluginWidgets(currentFarm?.id);
      } catch (pluginErr) {
        console.error('Error loading plugin widgets:', pluginErr);
      }

      // Remove any plugin widgets that might be hardcoded in the default layout
      const nonPluginWidgets = filteredWidgets.filter(widget =>
        widget.type !== 'field-health' && widget.type !== 'market-prices'
      );

      // Combine default widgets with plugin widgets
      const combinedWidgets = [...nonPluginWidgets, ...pluginWidgets];

      // Update the default layout with filtered and plugin widgets
      const filteredLayout = {
        ...defaultLayout,
        widgets: combinedWidgets
      };

      setDashboardLayout(filteredLayout);
      setIsDefaultLayout(true);
      setCurrentLayoutId(null);
      setCurrentLayoutName('Default');
    } finally {
      setIsLoading(false);
    }
  };

  // Save dashboard layout to the API
  const saveDashboardLayout = async (layout: LayoutConfig) => {
    if (!user || !token) return;

    try {
      setIsLoading(true);
      setError(null);

      // If we have a current layout ID, update that layout
      const payload = currentLayoutId ? {
        layout,
        layoutId: currentLayoutId,
        setAsActive: true
      } : {
        layout,
        name: currentLayoutName,
        setAsActive: true
      };

      const response = await axios.post(`${API_URL}/dashboard/user/${user.id}`, payload, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setDashboardLayout(layout);
      setIsDefaultLayout(false);

      // Update current layout info
      if (response.data.id) {
        setCurrentLayoutId(response.data.id);
        setCurrentLayoutName(response.data.name || 'Default');
      }

      // Refresh available layouts
      fetchAvailableLayouts();
    } catch (err: any) {
      console.error('Error saving dashboard layout:', err);
      setError(err.response?.data?.error || 'Failed to save dashboard layout');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Save dashboard layout as a new named layout
  const saveAsNewLayout = async (layout: LayoutConfig, name: string) => {
    if (!user || !token) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.post(`${API_URL}/dashboard/user/${user.id}`, {
        layout,
        name,
        setAsActive: true
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setDashboardLayout(layout);
      setIsDefaultLayout(false);

      // Update current layout info
      if (response.data.id) {
        setCurrentLayoutId(response.data.id);
        setCurrentLayoutName(response.data.name || name);
      }

      // Refresh available layouts
      fetchAvailableLayouts();
    } catch (err: any) {
      console.error('Error saving new dashboard layout:', err);
      setError(err.response?.data?.error || 'Failed to save new dashboard layout');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Set a layout as active
  const setActiveLayout = async (layoutId: string, setAsDefault: boolean = false) => {
    if (!user || !token) return;

    try {
      setIsLoading(true);
      setError(null);

      // Find the selected layout in available layouts to update UI immediately
      const selectedLayout = availableLayouts.find(layout => layout.id === layoutId);
      if (selectedLayout) {
        setCurrentLayoutId(layoutId);
        setCurrentLayoutName(selectedLayout.name || 'Custom Layout');
      }

      // Fetch the layout data immediately to update the UI
      try {
        const layoutResponse = await axios.get(`${API_URL}/dashboard/user/${user.id}?layoutId=${layoutId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (layoutResponse.data.layout && layoutResponse.data.layout.widgets && layoutResponse.data.layout.widgets.length > 0) {
          // Filter widgets based on user permissions
          const filteredWidgets = await filterWidgetsByPermission(layoutResponse.data.layout.widgets);

          // Fetch plugin widgets from enabled plugins
          let pluginWidgets: Widget[] = [];
          try {
            pluginWidgets = await getPluginWidgets(currentFarm?.id);
          } catch (pluginErr) {
            console.error('Error loading plugin widgets:', pluginErr);
          }

          // Remove any plugin widgets that might be hardcoded in the layout
          const nonPluginWidgets = filteredWidgets.filter(widget =>
            widget.type !== 'field-health' && widget.type !== 'market-prices'
          );

          // Combine user widgets with plugin widgets
          const combinedWidgets = [...nonPluginWidgets, ...pluginWidgets];

          // Update the layout with filtered and plugin widgets
          const filteredLayout = {
            ...layoutResponse.data.layout,
            widgets: combinedWidgets
          };

          // Update the dashboard layout immediately
          setDashboardLayout(filteredLayout);
          setIsDefaultLayout(layoutResponse.data.isDefault || false);
        }
      } catch (layoutErr) {
        console.error('Error fetching layout data:', layoutErr);
      }

      // Make API call to set active layout
      await axios.post(`${API_URL}/dashboard/user/${user.id}/active`, {
        layoutId,
        setAsDefault
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Refresh available layouts
      fetchAvailableLayouts();
    } catch (err: any) {
      console.error('Error setting active layout:', err);
      setError(err.response?.data?.error || 'Failed to set active layout');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Reset to farm default layout
  const resetToFarmDefault = async () => {
    if (!user || !token || !currentFarm) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.post(`${API_URL}/dashboard/user/${user.id}/reset`, {
        farmId: currentFarm.id
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      setDashboardLayout(response.data.layout);
      setIsDefaultLayout(true);
    } catch (err: any) {
      console.error('Error resetting dashboard layout:', err);
      setError(err.response?.data?.error || 'Failed to reset dashboard layout');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Add a widget to the dashboard
  const addWidget = (widget: Widget) => {
    if (!dashboardLayout) return;

    const updatedLayout = {
      ...dashboardLayout,
      widgets: [...dashboardLayout.widgets, widget]
    };

    setDashboardLayout(updatedLayout);
    saveDashboardLayout(updatedLayout).catch(err => {
      console.error('Error saving layout after adding widget:', err);
    });
  };

  // Remove a widget from the dashboard
  const removeWidget = (widgetId: string) => {
    if (!dashboardLayout) return;

    const updatedLayout = {
      ...dashboardLayout,
      widgets: dashboardLayout.widgets.filter(widget => widget.id !== widgetId)
    };

    setDashboardLayout(updatedLayout);
    saveDashboardLayout(updatedLayout).catch(err => {
      console.error('Error saving layout after removing widget:', err);
    });
  };

  // Update a widget's position
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clean up the timeout when the component unmounts
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  const updateWidgetPosition = (widgetId: string, position: { x: number; y: number; w: number; h: number }) => {
    if (!dashboardLayout) return;

    const updatedLayout = {
      ...dashboardLayout,
      widgets: dashboardLayout.widgets.map(widget =>
        widget.id === widgetId ? { ...widget, position } : widget
      )
    };

    // Update the state immediately for a responsive UI
    setDashboardLayout(updatedLayout);

    // Don't auto-save during editing to prevent refresh issues
    // The save will happen when the user clicks the Save button
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  // Delete a dashboard layout
  const deleteLayout = async (layoutId: string) => {
    if (!user || !token) return;

    try {
      setIsLoading(true);
      setError(null);

      // Make API call to delete the layout
      await axios.delete(`${API_URL}/dashboard/user/${user.id}/layout/${layoutId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // If the deleted layout was the current layout, fetch the new active layout
      if (layoutId === currentLayoutId) {
        await fetchDashboardLayout();
      }

      // Refresh available layouts
      fetchAvailableLayouts();
    } catch (err: any) {
      console.error('Error deleting dashboard layout:', err);
      setError(err.response?.data?.error || 'Failed to delete dashboard layout');
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Context value
  const contextValue: DashboardContextType = {
    dashboardLayout,
    isDefaultLayout,
    isLoading,
    error,
    availableLayouts,
    currentLayoutName,
    currentLayoutId,
    saveDashboardLayout,
    saveAsNewLayout,
    setActiveLayout,
    fetchAvailableLayouts,
    resetToFarmDefault,
    deleteLayout,
    addWidget,
    removeWidget,
    updateWidgetPosition,
    clearError,
  };

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
};

// Custom hook to use the dashboard context
export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};
