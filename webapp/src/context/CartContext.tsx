import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ShoppingCart, getCurrentCart, getAllCarts, addItemToCart, updateCartItemQuantity, removeCartItem, saveCartForLater, checkout } from '../services/shoppingCartService';

interface CartContextType {
  cart: ShoppingCart | null;
  carts: ShoppingCart[];
  loading: boolean;
  error: string | null;
  fetchCart: (farmId: string) => Promise<void>;
  fetchAllCarts: () => Promise<void>;
  addItem: (productId: string, farmId: string, quantity?: number) => Promise<void>;
  updateItemQuantity: (itemId: string, quantity: number) => Promise<void>;
  removeItem: (itemId: string) => Promise<void>;
  saveCart: (cartId: string) => Promise<void>;
  checkoutCart: (cartId: string, notes?: string) => Promise<any>;
  checkoutAllCarts: (notes?: string) => Promise<any[]>;
  clearCart: () => void;
  getTotalItemCount: () => number;
  getTotalPrice: () => number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [cart, setCart] = useState<ShoppingCart | null>(null);
  const [carts, setCarts] = useState<ShoppingCart[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCart = async (farmId: string) => {
    setLoading(true);
    setError(null);
    try {
      const cartData = await getCurrentCart(farmId);
      setCart(cartData);
    } catch (err) {
      console.error('Error fetching cart:', err);
      setError('Failed to load cart. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchAllCarts = async () => {
    setLoading(true);
    setError(null);
    try {
      const cartsData = await getAllCarts();
      setCarts(cartsData);
    } catch (err) {
      console.error('Error fetching all carts:', err);
      setError('Failed to load carts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const addItem = async (productId: string, farmId: string, quantity: number = 1) => {
    setLoading(true);
    setError(null);
    try {
      const updatedCart = await addItemToCart(productId, farmId, quantity);
      setCart(updatedCart);

      // Update the carts array if it contains this cart
      setCarts(prevCarts => {
        const cartIndex = prevCarts.findIndex(c => c.id === updatedCart.id);
        if (cartIndex >= 0) {
          const newCarts = [...prevCarts];
          newCarts[cartIndex] = updatedCart;
          return newCarts;
        } else {
          // If this cart wasn't in the array, add it
          return [...prevCarts, updatedCart];
        }
      });
    } catch (err) {
      console.error('Error adding item to cart:', err);
      setError('Failed to add item to cart. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateItemQuantity = async (itemId: string, quantity: number) => {
    setLoading(true);
    setError(null);
    try {
      const updatedCart = await updateCartItemQuantity(itemId, quantity);
      setCart(updatedCart);

      // Update the carts array if it contains this cart
      setCarts(prevCarts => {
        const cartIndex = prevCarts.findIndex(c => c.id === updatedCart.id);
        if (cartIndex >= 0) {
          const newCarts = [...prevCarts];
          newCarts[cartIndex] = updatedCart;
          return newCarts;
        }
        return prevCarts;
      });
    } catch (err) {
      console.error('Error updating cart item:', err);
      setError('Failed to update item quantity. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const removeItem = async (itemId: string) => {
    setLoading(true);
    setError(null);
    try {
      const updatedCart = await removeCartItem(itemId);
      setCart(updatedCart);

      // Update the carts array if it contains this cart
      setCarts(prevCarts => {
        const cartIndex = prevCarts.findIndex(c => c.id === updatedCart.id);
        if (cartIndex >= 0) {
          const newCarts = [...prevCarts];
          newCarts[cartIndex] = updatedCart;
          return newCarts;
        }
        return prevCarts;
      });
    } catch (err) {
      console.error('Error removing cart item:', err);
      setError('Failed to remove item from cart. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const saveCart = async (cartId: string) => {
    setLoading(true);
    setError(null);
    try {
      await saveCartForLater(cartId);

      // Remove the saved cart from the carts array
      setCarts(prevCarts => prevCarts.filter(c => c.id !== cartId));

      // If the current cart was saved, clear it
      if (cart && cart.id === cartId) {
        setCart(null);
      }
    } catch (err) {
      console.error('Error saving cart:', err);
      setError('Failed to save cart. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const checkoutCart = async (cartId: string, notes?: string) => {
    setLoading(true);
    setError(null);
    try {
      const result = await checkout(cartId, notes);

      // Remove the checked out cart from the carts array
      setCarts(prevCarts => prevCarts.filter(c => c.id !== cartId));

      // If the current cart was checked out, clear it
      if (cart && cart.id === cartId) {
        setCart(null);
      }

      return result;
    } catch (err) {
      console.error('Error checking out:', err);
      setError('Failed to complete checkout. Please try again.');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const checkoutAllCarts = async (notes?: string) => {
    setLoading(true);
    setError(null);
    try {
      const results = [];

      // Checkout each cart one by one
      for (const cart of carts) {
        try {
          const result = await checkout(cart.id, notes);
          results.push(result);
        } catch (err) {
          console.error(`Error checking out cart ${cart.id}:`, err);
          // Continue with other carts even if one fails
        }
      }

      // Refresh the carts list after checkout
      await fetchAllCarts();

      return results;
    } catch (err) {
      console.error('Error checking out all carts:', err);
      setError('Failed to complete checkout for all carts. Please try again.');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const clearCart = () => {
    setCart(null);
  };

  const getTotalItemCount = () => {
    return carts.reduce((total, cart) => total + (cart.itemCount || 0), 0);
  };

  const getTotalPrice = () => {
    return carts.reduce((total, cart) => total + (cart.totalPrice || 0), 0);
  };

  return (
    <CartContext.Provider
      value={{
        cart,
        carts,
        loading,
        error,
        fetchCart,
        fetchAllCarts,
        addItem,
        updateItemQuantity,
        removeItem,
        saveCart,
        checkoutCart,
        checkoutAllCarts,
        clearCart,
        getTotalItemCount,
        getTotalPrice
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
