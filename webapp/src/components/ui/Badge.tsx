import React, { HTMLAttributes } from 'react';

export interface BadgeProps extends HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'outline' | 'success' | 'warning' | 'destructive';
}

const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className = '', variant = 'default', children, ...props }, ref) => {
    // Base styles
    let variantClasses = '';

    // Variant styles
    switch (variant) {
      case 'primary':
        variantClasses = 'bg-primary-100 text-primary-800 border-primary-200';
        break;
      case 'secondary':
        variantClasses = 'bg-secondary-100 text-secondary-800 border-secondary-200';
        break;
      case 'outline':
        variantClasses = 'bg-transparent border-gray-300 text-gray-700';
        break;
      case 'success':
        variantClasses = 'bg-green-100 text-green-800 border-green-200';
        break;
      case 'warning':
        variantClasses = 'bg-yellow-100 text-yellow-800 border-yellow-200';
        break;
      case 'destructive':
        variantClasses = 'bg-red-100 text-red-800 border-red-200';
        break;
      default: // 'default'
        variantClasses = 'bg-gray-100 text-gray-800 border-gray-200';
        break;
    }

    return (
      <span
        ref={ref}
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${variantClasses} ${className}`}
        {...props}
      >
        {children}
      </span>
    );
  }
);

Badge.displayName = 'Badge';

export { Badge };