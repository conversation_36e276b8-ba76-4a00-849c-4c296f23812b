import React, { HTMLAttributes, forwardRef } from 'react';

export interface LabelProps extends HTMLAttributes<HTMLLabelElement> {
  htmlFor?: string;
}

const Label = forwardRef<HTMLLabelElement, LabelProps>(
  ({ className, htmlFor, ...props }, ref) => (
    <label
      ref={ref}
      htmlFor={htmlFor}
      className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className || ''}`}
      {...props}
    />
  )
);
Label.displayName = "Label";

export { Label };