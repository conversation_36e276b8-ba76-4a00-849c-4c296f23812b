import { useState, useEffect } from 'react';

interface CameraViewerProps {
  serialNumber: string;
  username: string;
  password: string;
  width?: number;
  height?: number;
}

const CameraViewer = ({ 
  serialNumber, 
  username, 
  password, 
  width = 640, 
  height = 480 
}: CameraViewerProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // URL for the amcrestwebview service
  const viewerUrl = `https://www.amcrestwebview.com/view/?serial=${encodeURIComponent(serialNumber)}&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;

  // Handle iframe load events
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  // Handle iframe error events
  const handleIframeError = () => {
    setIsLoading(false);
    setError('Failed to load camera feed. Please check your camera credentials and try again.');
  };

  return (
    <div className="camera-viewer">
      {isLoading && (
        <div className="flex justify-center items-center" style={{ width, height }}>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-500">Loading camera feed...</p>
        </div>
      )}
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" style={{ width, height: 'auto' }}>
          <span className="block sm:inline">{error}</span>
        </div>
      )}
      
      <iframe
        src={viewerUrl}
        width={width}
        height={height}
        frameBorder="0"
        allowFullScreen
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        style={{ display: isLoading ? 'none' : 'block' }}
        title={`Camera ${serialNumber}`}
      ></iframe>
    </div>
  );
};

export default CameraViewer;