import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../config';

const RegistrationSuccess: React.FC = () => {
  const [verificationCode, setVerificationCode] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<{
    success: boolean;
    message: string;
  } | null>(null);
  const [email, setEmail] = useState<string>(() => {
    // Try to get email from localStorage
    return localStorage.getItem('userEmail') || '';
  });
  const [resendLoading, setResendLoading] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const [resendError, setResendError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(0);

  // Function to handle countdown timer
  const startCountdown = useCallback(() => {
    // Set initial countdown to 3 minutes (180 seconds)
    setCountdown(180);

    // Start countdown interval
    const interval = setInterval(() => {
      setCountdown(prevCountdown => {
        if (prevCountdown <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prevCountdown - 1;
      });
    }, 1000);

    // Clean up interval on component unmount
    return () => clearInterval(interval);
  }, []);

  // Format countdown time as MM:SS
  const formatCountdown = () => {
    const minutes = Math.floor(countdown / 60);
    const seconds = countdown % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  // Reset resend error
  const resetResendError = () => {
    setResendError(null);
  };

  // Check for previous resend requests and start the countdown timer if needed
  useEffect(() => {
    const lastResendTime = localStorage.getItem('lastResendTime');
    if (lastResendTime) {
      const elapsedSeconds = Math.floor((Date.now() - parseInt(lastResendTime)) / 1000);
      const remainingSeconds = 180 - elapsedSeconds; // 3 minutes (180 seconds)

      if (remainingSeconds > 0) {
        setCountdown(remainingSeconds);
        startCountdown();
      } else {
        // Clear the stored time if the countdown has expired
        localStorage.removeItem('lastResendTime');
      }
    }
  }, [startCountdown]);

  // Function to handle resend verification email and store timestamp
  const handleResendVerification = async () => {
    try {
      if (!email) {
        setResendError('Email address not found. Please try logging in again.');
        return;
      }

      setResendLoading(true);
      setResendError(null);

      const response = await axios.post(`${API_URL}/auth/resend-email-verification`, { email });

      if (response.data.success) {
        setResendSuccess(true);
        // Start countdown timer
        startCountdown();
        // Store the current timestamp only when a verification email is successfully sent
        localStorage.setItem('lastResendTime', Date.now().toString());
      } else {
        setResendError('Failed to resend verification email. Please try again.');
      }
    } catch (err: any) {
      setResendError(err.response?.data?.error || 'Failed to resend verification email. Please try again.');
    } finally {
      setResendLoading(false);
    }
  };

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!verificationCode.trim()) {
      setVerificationStatus({
        success: false,
        message: 'Please enter the verification code from your email'
      });
      return;
    }

    try {
      setVerifying(true);
      const response = await axios.post(`${API_URL}/auth/verify-email-code`, {
        code: verificationCode
      });

      if (response.data.success) {
        setVerificationStatus({
          success: true,
          message: 'Email verified successfully! You can now log in to your account.'
        });
      } else {
        setVerificationStatus({
          success: false,
          message: response.data.error || 'Verification failed. Please try again.'
        });
      }
    } catch (error: any) {
      setVerificationStatus({
        success: false,
        message: error.response?.data?.error || 'Verification failed. Please try again.'
      });
    } finally {
      setVerifying(false);
    }
  };

  return (
    <div>
      <div className="flex flex-col items-center">
        <img 
          src="/logo.svg" 
          alt="nxtAcre Logo"
          className="h-12 w-auto mb-4"
          onError={(e) => {
            // Fallback if logo doesn't exist
            const target = e.currentTarget as HTMLImageElement;
            target.onerror = null;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              const span = document.createElement('span');
              span.className = 'text-3xl font-display font-bold text-primary-600';
              span.textContent = 'nxtAcre';
              parent.appendChild(span);
            }
          }}
        />
        <h2 className="text-center text-2xl font-display font-bold text-gray-900">
          Registration Successful!
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Please check your email to verify your account
        </p>
      </div>

      <div className="text-center py-6">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
          <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="mt-3 text-lg font-medium text-gray-900">Account Created Successfully!</h3>
        <p className="mt-2 text-sm text-gray-600">
          We've sent a verification code to your email address. Please enter the 6-digit code below to verify your account.
        </p>

        {verificationStatus && (
          <div className={`mt-4 p-3 rounded-md ${verificationStatus.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {verificationStatus.message}
          </div>
        )}

        <form onSubmit={handleVerify} className="mt-6">
          <div className="mb-4">
            <label htmlFor="verification-code" className="block text-sm font-medium text-gray-700 mb-1">
              Verification Code
            </label>
            <input
              id="verification-code"
              type="text"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              placeholder="Enter 6-digit code"
              className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm"
              maxLength={6}
            />
          </div>
          <button
            type="submit"
            disabled={verifying}
            className={`btn btn-primary w-full mb-4 ${verifying ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {verifying ? 'Verifying...' : 'Verify Email'}
          </button>
        </form>

        <p className="mt-4 text-sm text-gray-600">
          If you don't see the email, please check your spam folder or request a new verification code.
        </p>

        {/* Resend verification section */}
        <div className="mt-4 border-t border-gray-200 pt-4">
          {resendSuccess ? (
            <div className="text-sm text-green-600 mb-3">
              Verification email sent! Please check your inbox.
            </div>
          ) : countdown > 0 ? (
            <div className="text-sm text-gray-600 mb-3">
              You can request a new verification email in {formatCountdown()}
            </div>
          ) : (
            <div>
              {resendError && (
                <div className="text-sm text-red-600 mb-3">
                  {resendError}
                </div>
              )}

              <button
                onClick={handleResendVerification}
                disabled={resendLoading}
                className={`btn btn-primary w-full mb-4 ${resendLoading ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                {resendLoading ? 'Sending...' : 'Resend Verification Email'}
              </button>
            </div>
          )}
        </div>

        <div className="mt-6">
          <Link
            to="/login"
            className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Login
          </Link>
        </div>
      </div>
    </div>
  );
};

export default RegistrationSuccess;
