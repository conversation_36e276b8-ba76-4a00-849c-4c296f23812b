import React, {useState, useEffect, useRef} from 'react';
import axios from 'axios';
import {API_URL} from '../../config';
import {useAuth} from '../../context/AuthContext';
import TaxDocument from "../../../server/models/TaxDocument";

interface TaxDocument {
    id?: string;
    title: string;
    description: string;
    documentType: string;
    taxYear: number;
    status?: string;
    fileName?: string;
    fileSize?: number;
    fileType?: string;
}

interface TaxDocumentFormProps {
    farmId: string,
    documentId?: string,
    onSave?: (document: TaxDocument, file?: File) => void,
    onCancel?: () => void,
    year?: number,
    onDocumentAdded?: () => Promise<void>
}

const TaxDocumentForm: React.FC<TaxDocumentFormProps> = ({
                                                             farmId,
                                                             documentId,
                                                             onSave,
                                                             onCancel,
                                                             year,
                                                             onDocumentAdded
                                                         }) => {
    const {user, token} = useAuth();
    const fileInputRef = useRef<HTMLInputElement>(null);

    const [document, setDocument] = useState<TaxDocument>({
        title: '',
        description: '',
        documentType: '',
        taxYear: new Date().getFullYear(),
        status: 'draft'
    });

    const [file, setFile] = useState<File | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const documentTypes = [
        'w2', '1099', 'schedule_f', 'depreciation', 'expense', 'income', 'other'
    ];

    const statusOptions = [
        'draft', 'pending', 'submitted', 'approved', 'rejected'
    ];

    // Fetch tax document if editing
    useEffect(() => {
        const fetchDocument = async () => {
            if (!documentId) return;

            setLoading(true);
            setError(null);

            try {
                const response = await axios.get(`${API_URL}/api/tax/tax-documents/${documentId}`, {
                    headers: {Authorization: `Bearer ${token}`}
                });

                if (response.data && response.data.taxDocument) {
                    const fetchedDocument = response.data.taxDocument;
                    setDocument({
                        id: fetchedDocument.id,
                        title: fetchedDocument.title,
                        description: fetchedDocument.description || '',
                        documentType: fetchedDocument.documentType,
                        taxYear: fetchedDocument.taxYear,
                        status: fetchedDocument.status,
                        fileName: fetchedDocument.fileName,
                        fileSize: fetchedDocument.fileSize,
                        fileType: fetchedDocument.fileType
                    });
                } else {
                    throw new Error('Invalid response format from server');
                }
            } catch (err) {
                console.error('Error fetching tax document:', err);
                setError('Failed to load tax document. Please try again later.');
            } finally {
                setLoading(false);
            }
        };

        if (documentId) {
            fetchDocument();
        }
    }, [documentId, user]);

    // Handle form input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const {name, value} = e.target;

        if (name === 'taxYear') {
            setDocument(prev => ({...prev, [name]: parseInt(value) || new Date().getFullYear()}));
        } else {
            setDocument(prev => ({...prev, [name]: value}));
        }
    };

    // Handle file input changes
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            const selectedFile = e.target.files[0];
            setFile(selectedFile);

            // Update document with file info
            setDocument(prev => ({
                ...prev,
                fileName: selectedFile.name,
                fileSize: selectedFile.size,
                fileType: selectedFile.type
            }));
        }
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!document.title || !document.documentType) {
            setError('Please fill in all required fields.');
            return;
        }

        // If creating a new document, require a file
        if (!documentId && !file) {
            setError('Please select a file to upload.');
            return;
        }

        try {
            setLoading(true);

            if (onSave) {
                // Use provided onSave function
                onSave(document, file || undefined);
            } else {
                // Default save behavior - save directly to server
                const formData = new FormData();
                formData.append('title', document.title);
                formData.append('description', document.description);
                formData.append('documentType', document.documentType);
                formData.append('taxYear', document.taxYear.toString());

                if (file) {
                    formData.append('file', file);
                }

                const url = documentId 
                    ? `${API_URL}/api/tax/tax-documents/${documentId}`
                    : `${API_URL}/api/tax/farms/${farmId}/tax-documents`;

                const method = documentId ? 'PUT' : 'POST';

                const response = await axios({
                    method,
                    url,
                    data: formData,
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'multipart/form-data'
                    }
                });

                if (response.status !== 200 && response.status !== 201) {
                    throw new Error('Failed to save tax document');
                }

                // Reset form after successful save
                if (!documentId) {
                    setDocument({
                        title: '',
                        description: '',
                        documentType: '',
                        taxYear: new Date().getFullYear(),
                        status: 'draft'
                    });
                    setFile(null);
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                }
            }

            // Call onDocumentAdded if provided
            if (onDocumentAdded) {
                await onDocumentAdded();
            }

            setError(null);
        } catch (err) {
            console.error('Error saving tax document:', err);
            setError('Failed to save tax document. Please try again later.');
        } finally {
            setLoading(false);
        }
    };

    // Format file size for display
    const formatFileSize = (bytes?: number): string => {
        if (!bytes) return 'N/A';

        if (bytes < 1024) return bytes + ' bytes';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    };

    // Handle cancel button click
    const handleCancel = () => {
        if (onCancel) {
            // Use provided onCancel function
            onCancel();
        } else {
            // Default cancel behavior - reset form
            if (!documentId) {
                setDocument({
                    title: '',
                    description: '',
                    documentType: '',
                    taxYear: new Date().getFullYear(),
                    status: 'draft'
                });
                setFile(null);
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            }
            setError(null);
        }
    };

    // Get document type display name
    const getDocumentTypeDisplayName = (type: string): string => {
        switch (type) {
            case 'w2':
                return 'W-2 Form';
            case '1099':
                return '1099 Form';
            case 'schedule_f':
                return 'Schedule F';
            case 'depreciation':
                return 'Depreciation Schedule';
            case 'expense':
                return 'Expense Document';
            case 'income':
                return 'Income Document';
            case 'other':
                return 'Other';
            default:
                return type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ');
        }
    };

    return (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {documentId ? 'Edit Tax Document' : 'Upload Tax Document'}
                </h3>
                <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    {documentId
                        ? 'Update the details of this tax document.'
                        : 'Upload a new tax document for your farm business.'}
                </p>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mx-6 mb-4"
                     role="alert">
                    <span className="block sm:inline">{error}</span>
                </div>
            )}

            <form onSubmit={handleSubmit} className="border-t border-gray-200 px-4 py-5 sm:px-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    {/* Document Title */}
                    <div>
                        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                            Document Title <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="text"
                            id="title"
                            name="title"
                            value={document.title}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        />
                    </div>

                    {/* Document Type */}
                    <div>
                        <label htmlFor="documentType" className="block text-sm font-medium text-gray-700 mb-1">
                            Document Type <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="documentType"
                            name="documentType"
                            value={document.documentType}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            <option value="">Select a document type</option>
                            {documentTypes.map(type => (
                                <option key={type} value={type}>
                                    {getDocumentTypeDisplayName(type)}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Tax Year */}
                    <div>
                        <label htmlFor="taxYear" className="block text-sm font-medium text-gray-700 mb-1">
                            Tax Year <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="taxYear"
                            name="taxYear"
                            value={document.taxYear}
                            onChange={handleChange}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            required
                        >
                            {[...Array(5)].map((_, i) => {
                                const year = new Date().getFullYear() - 2 + i;
                                return (
                                    <option key={year} value={year}>
                                        {year}
                                    </option>
                                );
                            })}
                        </select>
                    </div>

                    {/* Status (only for editing) */}
                    {documentId && (
                        <div>
                            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                                Status
                            </label>
                            <select
                                id="status"
                                name="status"
                                value={document.status}
                                onChange={handleChange}
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                            >
                                {statusOptions.map(status => (
                                    <option key={status} value={status}>
                                        {status.charAt(0).toUpperCase() + status.slice(1)}
                                    </option>
                                ))}
                            </select>
                        </div>
                    )}
                </div>

                {/* Description */}
                <div className="mt-6">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                    </label>
                    <textarea
                        id="description"
                        name="description"
                        value={document.description}
                        onChange={handleChange}
                        rows={4}
                        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
                    ></textarea>
                </div>

                {/* File Upload */}
                <div className="mt-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        Document File {!documentId && <span className="text-red-500">*</span>}
                    </label>

                    {document.fileName ? (
                        <div className="mt-2 flex items-center space-x-2">
                            <div className="flex-1 bg-gray-100 rounded-md p-3">
                                <p className="text-sm font-medium text-gray-900">{document.fileName}</p>
                                <p className="text-xs text-gray-500">{formatFileSize(document.fileSize)}</p>
                            </div>
                            <button
                                type="button"
                                onClick={() => fileInputRef.current?.click()}
                                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                            >
                                Replace
                            </button>
                        </div>
                    ) : (
                        <div
                            className="mt-2 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div className="space-y-1 text-center">
                                <svg
                                    className="mx-auto h-12 w-12 text-gray-400"
                                    stroke="currentColor"
                                    fill="none"
                                    viewBox="0 0 48 48"
                                    aria-hidden="true"
                                >
                                    <path
                                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                        strokeWidth={2}
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                                <div className="flex text-sm text-gray-600">
                                    <label
                                        htmlFor="file-upload"
                                        className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
                                    >
                                        <span>Upload a file</span>
                                        <input
                                            id="file-upload"
                                            name="file-upload"
                                            type="file"
                                            className="sr-only"
                                            ref={fileInputRef}
                                            onChange={handleFileChange}
                                            accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.jpg,.jpeg,.png"
                                        />
                                    </label>
                                    <p className="pl-1">or drag and drop</p>
                                </div>
                                <p className="text-xs text-gray-500">PDF, Word, Excel, CSV, or image files up to
                                    10MB</p>
                            </div>
                        </div>
                    )}
                    <input
                        type="file"
                        className="hidden"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.jpg,.jpeg,.png"
                    />
                </div>

                {/* Form Actions */}
                <div className="mt-8 flex justify-end space-x-3">
                    <button
                        type="button"
                        onClick={handleCancel}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        disabled={loading}
                        className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                            loading ? 'opacity-70 cursor-not-allowed' : ''
                        }`}
                    >
                        {loading ? 'Saving...' : documentId ? 'Update Document' : 'Upload Document'}
                    </button>
                </div>
            </form>
        </div>
    );
};

export default TaxDocumentForm;
