import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { GridStack, GridStackNode, GridItemHTMLElement } from 'gridstack';
import axios from 'axios';
import 'gridstack/dist/gridstack.min.css';
import 'gridstack/dist/gridstack-extra.min.css';
import '../styles/gridstack-custom.css';
import { useDashboard, Widget, LayoutConfig } from '../context/DashboardContext';
import { createRoot } from 'react-dom/client';
import { useAuth } from '../context/AuthContext';
import { useFarm } from '../context/FarmContext';
import { API_URL } from '../config';

interface DashboardGridProps {
  editMode: boolean;
  onEditModeChange: (editMode: boolean) => void;
  renderWidget: (widget: Widget) => React.ReactNode;
}

const DashboardGrid: React.FC<DashboardGridProps> = ({
  editMode,
  onEditModeChange,
  renderWidget
}) => {
  const {
    dashboardLayout,
    isDefaultLayout,
    isLoading,
    updateWidgetPosition,
    resetToFarmDefault,
    saveDashboardLayout,
    availableLayouts,
    currentLayoutName,
    currentLayoutId,
    saveAsNewLayout,
    setActiveLayout,
    fetchAvailableLayouts,
    deleteLayout
  } = useDashboard();

  const { user, token } = useAuth();
  const { selectedFarm, userRole } = useFarm();

  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [showSaveAsModal, setShowSaveAsModal] = useState(false);
  const [newLayoutName, setNewLayoutName] = useState('');
  const [showSetFarmDefaultConfirm, setShowSetFarmDefaultConfirm] = useState(false);
  const [showSetDefaultConfirm, setShowSetDefaultConfirm] = useState(false);
  const [layoutToDelete, setLayoutToDelete] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Check if user is a farm admin
  const isFarmAdmin = userRole === 'admin' || userRole === 'owner';
  const gridRef = useRef<HTMLDivElement>(null);
  const gridInstanceRef = useRef<GridStack | null>(null);

  // Custom resize to content callback - disabled to fix layout issues
  const customResizeToContent = useCallback((el: GridItemHTMLElement) => {
    // Do nothing - we're disabling auto-resize to fix layout issues
    return;
  }, []);

  // Create a default layout if none is available
  const effectiveLayout = useMemo(() => {
    if (dashboardLayout) return dashboardLayout;

    // Default layout to use when no layout is available
    return {
      widgets: [
        {
          id: 'getting-started',
          type: 'getting-started',
          position: { x: 0, y: 0, w: 12, h: 6 },
          title: 'Getting Started'
        },
        {
          id: 'quick-actions',
          type: 'quick-actions',
          position: { x: 0, y: 6, w: 12, h: 4 },
          title: 'Quick Actions',
          actions: [
            { id: '1', label: 'View Transactions', link: '/transactions' },
            { id: '2', label: 'Manage Invoices', link: '/invoices' },
            { id: '3', label: 'View Customers', link: '/customers' },
            { id: '4', label: 'Manage Farms', link: '/farms' }
          ]
        }
      ],
      layout: {
        cols: 12,
        rowHeight: 50,
        gap: 20
      }
    };
  }, [dashboardLayout]);

  // Initialize GridStack
  useEffect(() => {
    if (!gridRef.current) return;

    // Clean up any existing grid instance
    if (gridInstanceRef.current) {
      gridInstanceRef.current.destroy(false);
    }

    // Initialize GridStack with modified configuration to prevent inline style conflicts
    const grid = GridStack.init({
      column: effectiveLayout.layout.cols,
      margin: effectiveLayout.layout.gap,
      cellHeight: effectiveLayout.layout.rowHeight,
      float: true,
      animate: true,
      draggable: { handle: '.widget-header' },
      resizable: { handles: 'e,se,s,sw,w' },
      staticGrid: !editMode,
      disableDrag: !editMode,
      disableResize: !editMode,
      alwaysShowResizeHandle: editMode, // Only show resize handles in edit mode
      columnOpts: { breakpoints: [{w:768, c:1}] },
      // Use CSS classes instead of inline styles where possible
      styleInHead: true, // Put styles in the head to avoid inline styles
      removable: false, // Don't add remove button
      // Disable auto-height behavior to fix layout issues
      cellHeightUnit: 'px',
      disableOneColumnMode: false,
      // Disable automatic content resizing which causes layout issues
      disableAutoResize: true,
    }, gridRef.current);

    // Add change event listener
    grid.on('change', (event: Event, items: GridStackNode[]) => {
      if (!editMode) return;

      items.forEach((item) => {
        const widgetId = item.id as string;
        updateWidgetPosition(widgetId, {
          x: item.x || 0,
          y: item.y || 0,
          w: item.w || 2,
          h: item.h || 2
        });
      });
    });

    // Add dragstart event listener to improve move handler experience
    grid.on('dragstart', (event: Event, el: GridItemHTMLElement) => {
      if (!editMode) return;

      // Add a class to indicate dragging is in progress
      el.classList.add('widget-dragging');
    });

    // Add dragstop event listener to clean up after dragging
    grid.on('dragstop', (event: Event, el: GridItemHTMLElement) => {
      if (!editMode) return;

      // Remove the dragging class
      el.classList.remove('widget-dragging');
    });

    gridInstanceRef.current = grid;

    // Add widgets to the grid
    effectiveLayout.widgets?.forEach(widget => {
      const existingNode = grid.engine.nodes?.find(n => n.id === widget.id);
      if (!existingNode) {
        grid.addWidget({
          id: widget.id,
          x: widget.position.x,
          y: widget.position.y,
          w: widget.position.w,
          h: widget.position.h,
          minW: 2,
          minH: 2,
          content: `<div class="widget-content" id="widget-${widget.id}"></div>`
        });
      }
    });

    return () => {
      if (gridInstanceRef.current) {
        gridInstanceRef.current.destroy(false);
        gridInstanceRef.current = null;
      }
    };
  }, [effectiveLayout, effectiveLayout.layout, editMode, updateWidgetPosition]);

  // Handle widget additions and removals
  useEffect(() => {
    if (!gridInstanceRef.current) return;

    // Get current widget IDs in the grid
    const gridWidgetIds = gridInstanceRef.current.engine.nodes?.map(n => n.id) || [];

    // Find widgets that need to be added (in effectiveLayout but not in grid)
    const widgetsToAdd = effectiveLayout.widgets?.filter(
      widget => !gridWidgetIds.includes(widget.id)
    ) || [];

    // Add new widgets to the grid
    widgetsToAdd.forEach(widget => {
      gridInstanceRef.current?.addWidget({
        id: widget.id,
        x: widget.position.x,
        y: widget.position.y,
        w: widget.position.w,
        h: widget.position.h,
        minW: 2,
        minH: 2,
        content: `<div class="widget-content" id="widget-${widget.id}"></div>`
      });
    });

    // Find widgets that need to be removed (in grid but not in effectiveLayout)
    const dashboardWidgetIds = effectiveLayout.widgets?.map(w => w.id) || [];
    const widgetsToRemove = gridInstanceRef.current.engine.nodes?.filter(
      node => !dashboardWidgetIds.includes(node.id as string)
    ) || [];

    // Remove widgets from the grid
    widgetsToRemove.forEach(node => {
      gridInstanceRef.current?.removeWidget(node.el as GridItemHTMLElement);
    });

    // Only render widgets if not in edit mode
    if (!editMode) {
      // Render widgets after grid is updated
      effectiveLayout.widgets?.forEach(widget => {
        const container = document.getElementById(`widget-${widget.id}`);
        if (container && container.childNodes.length === 0) {
          // Create a wrapper div for the widget content
          const wrapper = document.createElement('div');
          wrapper.className = 'widget-wrapper';

          // Render the widget into the wrapper
          const widgetElement = renderWidget(widget);
          if (React.isValidElement(widgetElement)) {
            const root = document.createElement('div');
            wrapper.appendChild(root);
            container.appendChild(wrapper);

            // Use ReactDOM to render the widget
            const reactRoot = createRoot(root);
            reactRoot.render(widgetElement);
          }
        }
      });
    }
  }, [effectiveLayout, effectiveLayout.widgets, editMode, renderWidget]);

  // Update grid edit mode
  useEffect(() => {
    if (!gridInstanceRef.current) return;

    gridInstanceRef.current.setStatic(!editMode);
  }, [editMode]);

  // Add window resize event listener to handle window resizing
  useEffect(() => {
    const handleResize = () => {
      if (!gridInstanceRef.current) return;

      // Trigger onResize to handle column changes and other responsive behavior
      gridInstanceRef.current.onResize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Render widgets into their containers
  useEffect(() => {
    // Skip rerendering widgets during edit mode unless save is clicked
    // This prevents refresh issues during layout editing
    if (editMode) return;

    effectiveLayout.widgets?.forEach(widget => {
      const container = document.getElementById(`widget-${widget.id}`);
      if (container) {
        // Clear existing content
        while (container.firstChild) {
          container.removeChild(container.firstChild);
        }

        // Create a wrapper div for the widget content
        const wrapper = document.createElement('div');
        wrapper.className = 'widget-wrapper';

        // Render the widget into the wrapper
        const widgetElement = renderWidget(widget);
        if (React.isValidElement(widgetElement)) {
          const root = document.createElement('div');
          wrapper.appendChild(root);
          container.appendChild(wrapper);

          // Use ReactDOM to render the widget
          const reactRoot = createRoot(root);
          reactRoot.render(widgetElement);
        }
      }
    });
  }, [effectiveLayout, effectiveLayout.widgets, renderWidget, editMode]);

  const handleResetLayout = async () => {
    try {
      await resetToFarmDefault();
      setShowResetConfirm(false);
      onEditModeChange(false);
    } catch (error) {
      console.error('Error resetting layout:', error);
    }
  };

  const handleDeleteLayout = async () => {
    try {
      if (layoutToDelete) {
        await deleteLayout(layoutToDelete);
        setShowDeleteConfirm(false);
        setLayoutToDelete(null);
      }
    } catch (error) {
      console.error('Error deleting layout:', error);
    }
  };

  const handleSetAsDefault = async () => {
    try {
      if (currentLayoutId) {
        await setActiveLayout(currentLayoutId);
        setShowSetDefaultConfirm(false);
      }
    } catch (error) {
      console.error('Error setting layout as default:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        <p className="ml-3 text-gray-500">Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      {/* Dashboard controls */}
      <div className="mb-4 flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0">
        <div className="flex items-center">
          <h1 className="text-xl md:text-2xl font-bold text-gray-900">Dashboard</h1>
          {(isDefaultLayout || !dashboardLayout) && (
            <span className="ml-2 px-2 py-1 text-xs bg-primary-100 text-primary-800 rounded-full">
              Default Layout
            </span>
          )}
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
          {/* Layout selection dropdown */}
          <div className="flex items-center w-full sm:w-auto">
            <label htmlFor="layout-select" className="mr-2 text-sm font-medium text-gray-700">
              Layout:
            </label>
            <div className="relative flex-grow sm:flex-grow-0">
              <div className="flex items-center">
                <select
                  id="layout-select"
                  className="form-select w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                  value={currentLayoutId || ''}
                  onChange={(e) => {
                    const selectedValue = e.target.value;
                    if (selectedValue === 'default') {
                      resetToFarmDefault();
                    } else {
                      // Set the selected value in the dropdown immediately
                      const select = document.getElementById('layout-select') as HTMLSelectElement;
                      if (select) {
                        select.value = selectedValue;
                      }

                      // Call setActiveLayout to update the layout
                      setActiveLayout(selectedValue);
                    }
                  }}
                >
                  <option value="default">Farm Default</option>
                  {availableLayouts?.map((layout) => (
                    <option key={layout.id} value={layout.id}>
                      {layout.name}
                    </option>
                  ))}
                </select>
                {currentLayoutId && !isDefaultLayout && (
                  <button
                    onClick={() => {
                      setLayoutToDelete(currentLayoutId);
                      setShowDeleteConfirm(true);
                    }}
                    className="ml-2 p-1 text-red-600 hover:text-red-800 rounded-full hover:bg-red-100 transition-colors"
                    title="Delete this layout"
                    disabled={editMode}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-2 w-full sm:w-auto">
            {/* Save As New Layout button */}
            <button
              onClick={() => {
                setNewLayoutName('');
                setShowSaveAsModal(true);
              }}
              className="btn btn-outline btn-sm"
              disabled={editMode}
            >
              Save As New
            </button>

            {/* Farm admin controls */}
            {isFarmAdmin && currentLayoutId && !isDefaultLayout && (
              <button
                onClick={() => setShowSetFarmDefaultConfirm(true)}
                className="btn btn-outline btn-sm"
                disabled={editMode}
              >
                Set as Farm Default
              </button>
            )}

            {/* Set as personal default button */}
            {currentLayoutId && !isDefaultLayout && (
              <button
                onClick={() => setShowSetDefaultConfirm(true)}
                className="btn btn-outline btn-sm"
                disabled={editMode}
              >
                Set as My Default
              </button>
            )}

            {/* Edit/Save Layout button */}
            {editMode ? (
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    // Cancel edit mode without saving
                    onEditModeChange(false);
                  }}
                  className="btn btn-outline btn-sm"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    if (dashboardLayout) {
                      // Save the layout and exit edit mode
                      saveDashboardLayout(dashboardLayout)
                        .then(() => {
                          // Force a rerender of widgets after saving
                          if (gridInstanceRef.current) {
                            // First exit edit mode
                            onEditModeChange(false);

                            // Then trigger a rerender by updating the grid
                            setTimeout(() => {
                              if (gridInstanceRef.current) {
                                gridInstanceRef.current.onResize();
                              }
                            }, 100);
                          } else {
                            onEditModeChange(false);
                          }
                        })
                        .catch(err => console.error('Error saving dashboard layout:', err));
                    }
                  }}
                  className="btn btn-primary btn-sm"
                >
                  Save Layout
                </button>
              </div>
            ) : (
              <button
                onClick={() => onEditModeChange(true)}
                className="btn btn-outline btn-sm"
              >
                Customize Layout
              </button>
            )}

            {/* Reset to Default button */}
            {dashboardLayout && !isDefaultLayout && (
              <button
                onClick={() => setShowResetConfirm(true)}
                className="btn btn-outline btn-sm"
                disabled={editMode}
              >
                Reset to Default
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Reset confirmation dialog */}
      {showResetConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 sm:p-6 max-w-md w-full">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Reset Dashboard Layout</h3>
            <p className="text-sm sm:text-base text-gray-500 mb-4">
              Are you sure you want to reset your dashboard to the farm default layout? This action cannot be undone.
            </p>
            <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-2">
              <button
                onClick={() => setShowResetConfirm(false)}
                className="btn btn-outline btn-sm sm:btn"
              >
                Cancel
              </button>
              <button
                onClick={handleResetLayout}
                className="btn btn-accent btn-sm sm:btn"
              >
                Reset Layout
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Save As New Layout modal */}
      {showSaveAsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 sm:p-6 max-w-md w-full">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Save Layout As</h3>
            <div className="mb-3 sm:mb-4">
              <label htmlFor="layout-name" className="block text-sm font-medium text-gray-700 mb-1">
                Layout Name
              </label>
              <input
                type="text"
                id="layout-name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-base"
                value={newLayoutName}
                onChange={(e) => setNewLayoutName(e.target.value)}
                placeholder="My Custom Layout"
              />
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-2">
              <button
                onClick={() => setShowSaveAsModal(false)}
                className="btn btn-outline btn-sm sm:btn"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  if (newLayoutName.trim() && dashboardLayout) {
                    saveAsNewLayout(dashboardLayout, newLayoutName.trim())
                      .then(() => {
                        setShowSaveAsModal(false);
                        fetchAvailableLayouts();
                      })
                      .catch(err => console.error('Error saving new layout:', err));
                  }
                }}
                className="btn btn-primary btn-sm sm:btn"
                disabled={!newLayoutName.trim()}
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Set as Farm Default confirmation dialog */}
      {showSetFarmDefaultConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 sm:p-6 max-w-md w-full">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Set as Farm Default Layout</h3>
            <p className="text-sm sm:text-base text-gray-500 mb-4">
              Are you sure you want to set this layout as the default for all users in your farm? This will affect new users and users who reset to the farm default.
            </p>
            <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-2">
              <button
                onClick={() => setShowSetFarmDefaultConfirm(false)}
                className="btn btn-outline btn-sm sm:btn"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  if (dashboardLayout && selectedFarm) {
                    // Call API to set as farm default
                    axios.post(`${API_URL}/dashboard/farm/${selectedFarm.id}`, {
                      layout: dashboardLayout
                    }, {
                      headers: { Authorization: `Bearer ${token}` }
                    })
                    .then(() => {
                      setShowSetFarmDefaultConfirm(false);
                      // Show success message
                      alert('Layout set as farm default successfully');
                    })
                    .catch(err => {
                      console.error('Error setting farm default layout:', err);
                      alert('Failed to set layout as farm default');
                    });
                  }
                }}
                className="btn btn-accent btn-sm sm:btn"
              >
                Set as Default
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Layout confirmation dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 sm:p-6 max-w-md w-full">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Delete Layout</h3>
            <p className="text-sm sm:text-base text-gray-500 mb-4">
              Are you sure you want to delete this layout? This action cannot be undone.
            </p>
            <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-2">
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setLayoutToDelete(null);
                }}
                className="btn btn-outline btn-sm sm:btn"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteLayout}
                className="btn btn-error btn-sm sm:btn"
              >
                Delete Layout
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Set as Default confirmation dialog */}
      {showSetDefaultConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-4 sm:p-6 max-w-md w-full">
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Set as Default Layout</h3>
            <p className="text-sm sm:text-base text-gray-500 mb-4">
              Are you sure you want to set this layout as your default? This layout will be loaded automatically when you log in.
            </p>
            <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-2">
              <button
                onClick={() => setShowSetDefaultConfirm(false)}
                className="btn btn-outline btn-sm sm:btn"
              >
                Cancel
              </button>
              <button
                onClick={handleSetAsDefault}
                className="btn btn-accent btn-sm sm:btn"
              >
                Set as Default
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Dashboard grid */}
      <div className="grid-stack" ref={gridRef}></div>
    </div>
  );
};

export default DashboardGrid;
