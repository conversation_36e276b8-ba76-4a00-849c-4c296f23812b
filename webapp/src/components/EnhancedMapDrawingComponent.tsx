import React, { useState, useEffect, useRef, useCallback } from 'react';
import {GoogleMap, DrawingManager, Polygon, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, InfoWindow} from '@react-google-maps/api';
import { GOOGLE_MAPS_API_KEY, API_URL } from '../config';
import { loadGoogleMapsApi, isGoogleMapsLoaded } from '../utils/googleMapsLoader';

// Define the shape of the coordinates for a polygon
interface PolygonCoordinates {
  lat: number;
  lng: number;
}

// No additional interfaces needed for the simplified component

interface EnhancedMapDrawingComponentProps {
  value: string; // GeoJSON string for field boundaries
  onChange: (value: string) => void;
  center?: { lat: number; lng: number };
  height?: string;
  width?: string;
}

// Default map center (can be overridden by props)
const defaultCenter = { lat: 39.8283, lng: -98.5795 }; // Center of the US

// Available colors for drawing
const colorOptions = [
  '#22c55e', // Green
  '#ef4444', // Red
  '#3b82f6', // Blue
  '#f59e0b', // Yellow
  '#8b5cf6', // Purple
  '#ec4899', // Pink
  '#14b8a6', // Teal
  '#f97316', // Orange
];

const EnhancedMapDrawingComponent: React.FC<EnhancedMapDrawingComponentProps> = ({
  value,
  onChange,
  center,
  height = '400px',
  width = '100%',
}) => {
  // Reference to the map instance
  const fieldMapRef = useRef<google.maps.Map | null>(null);

  // Reference to the harvest map instance
  const harvestMapRef = useRef<google.maps.Map | null>(null);

  // State for the drawn polygon paths (field boundaries)
  const [fieldPaths, setFieldPaths] = useState<PolygonCoordinates[][]>([]);

  // References to the polygon instances
  const polygonRefs = useRef<(google.maps.Polygon | null)[]>([]);

  // State for the map center
  const [mapCenter, setMapCenter] = useState(center || defaultCenter);

  // State for the drawing mode
  const [drawingMode, setDrawingMode] = useState<any>(null);

  // State for harvest maps
  const [harvestMaps, setHarvestMaps] = useState<any[]>([]);

  // State for active harvest map index
  const [activeHarvestMapIndex, setActiveHarvestMapIndex] = useState<number>(-1);

  // State for field boundary maps
  const [fieldBoundaryMaps, setFieldBoundaryMaps] = useState<any[]>([]);

  // State for active field boundary map index
  const [activeFieldBoundaryMapIndex, setActiveFieldBoundaryMapIndex] = useState<number>(-1);

  // State for selected color
  const [selectedColor, setSelectedColor] = useState<string>(colorOptions[0]);

  // Define available symbols with custom paths for better visual distinction
  const symbolOptions = [
    { 
      id: 'arrow', 
      name: 'Arrow', 
      path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
      scale: 5
    },
    { 
      id: 'rock', 
      name: 'Rock', 
      // Custom path for a rock-like shape
      path: 'M -5,-5 C -7,-3 -7,3 -5,5 C -3,7 3,7 5,5 C 7,3 7,-3 5,-5 C 3,-7 -3,-7 -5,-5 z',
      scale: 1
    },
    { 
      id: 'hole', 
      name: 'Hole', 
      // Circle with a hollow center for a hole
      path: google.maps.SymbolPath.CIRCLE,
      scale: 8,
      fillOpacity: 0.4
    },
    { 
      id: 'tree', 
      name: 'Tree', 
      // Custom path for a tree-like shape
      path: 'M 0,-10 L 3,-5 L 1,-5 L 3,0 L 1,0 L 2,5 L -2,5 L -1,0 L -3,0 L -1,-5 L -3,-5 Z',
      scale: 1
    },
    { 
      id: 'water', 
      name: 'Water Source', 
      // Custom path for a water drop
      path: 'M 0,-10 C -5,-5 -5,5 0,10 C 5,5 5,-5 0,-10 z',
      scale: 1
    },
    { 
      id: 'equipment', 
      name: 'Equipment', 
      // Custom path for equipment/tractor
      path: 'M -8,-3 L -8,3 L 8,3 L 8,-3 Z M -6,-6 L -6,-3 L -2,-3 L -2,-6 Z M 2,-6 L 2,-3 L 6,-3 L 6,-6 Z',
      scale: 1
    },
    { 
      id: 'danger', 
      name: 'Danger', 
      // Triangle for danger/warning
      path: 'M 0,-8 L 8,8 L -8,8 Z',
      scale: 1
    },
    { 
      id: 'flag', 
      name: 'Flag', 
      // Custom path for a flag
      path: 'M -1,-10 L -1,10 M -1,-10 L 5,-8 L -1,-6',
      scale: 1,
      strokeWeight: 2
    },
  ];

  // State for selected symbol
  const [selectedSymbol, setSelectedSymbol] = useState<string>(symbolOptions[0].id);

  // State for active label input
  const [activeLabelInput, setActiveLabelInput] = useState<{position: PolygonCoordinates, text: string} | null>(null);

  // State for map dialog
  const [mapDialogOpen, setMapDialogOpen] = useState<boolean>(false);
  const [mapDialogMode, setMapDialogMode] = useState<'add' | 'edit'>('add');
  const [mapDialogType, setMapDialogType] = useState<'harvest' | 'field'>('harvest');
  const [mapDialogData, setMapDialogData] = useState<{name: string, description: string}>({
    name: '',
    description: ''
  });

  // Custom map container style based on props
  const customMapContainerStyle = {
    width,
    height,
  };

  // Parse GeoJSON from value prop when component mounts or value changes
  useEffect(() => {
    // Clear paths if value is empty
    if (!value || value.trim() === '') {
      setFieldPaths([]);
      setFieldBoundaryMaps([]);
      return;
    }

    try {
      // Check if value is a valid JSON string
      const geoJson = JSON.parse(value);

      // Check if the parsed JSON is a valid GeoJSON FeatureCollection
      if (geoJson && geoJson.type === 'FeatureCollection') {
        const polygons: PolygonCoordinates[][] = [];

        // Handle case where features array might be empty
        if (geoJson.features && geoJson.features.length > 0) {
          geoJson.features.forEach((feature: any) => {
            if (feature.geometry && feature.geometry.type === 'Polygon' && 
                feature.geometry.coordinates && feature.geometry.coordinates.length > 0) {
              // Convert GeoJSON coordinates to Google Maps LatLng format
              // GeoJSON format is [longitude, latitude], but Google Maps expects {lat, lng}
              const polygon = feature.geometry.coordinates[0].map((coord: number[]) => ({
                lat: coord[1],
                lng: coord[0],
              }));

              polygons.push(polygon);
            }
          });
        }

        setFieldPaths(polygons);

        // Initialize field boundary maps if they don't exist yet
        if (fieldBoundaryMaps.length === 0 && polygons.length > 0) {
          const initialMap = {
            id: `field-boundary-${Date.now()}`,
            name: 'Default Field Boundary',
            description: 'Default field boundary map',
            order: 0,
            elements: polygons.map((polygon, index) => ({
              id: `polygon-${Date.now()}-${index}`,
              type: 'polygon',
              color: '#22c55e',
              coordinates: polygon
            }))
          };

          setFieldBoundaryMaps([initialMap]);
          setActiveFieldBoundaryMapIndex(0);
        }
      } else {
        console.warn('Invalid GeoJSON format:', geoJson);
        setFieldPaths([]);
      }
    } catch (error) {
      console.error('Error parsing GeoJSON:', error);
      setFieldPaths([]);
    }
  }, [value, fieldBoundaryMaps.length]);

  // Update map center when center prop changes
  useEffect(() => {
    if (center) {
      setMapCenter(center);

      // If we have map instance, pan to the new center and zoom in
      if (fieldMapRef.current) {
        fieldMapRef.current.panTo(center);
        fieldMapRef.current.setZoom(15); // Zoom in to a reasonable level
      }
    }
  }, [center]);

  // Handle field map load
  const onFieldMapLoad = useCallback((map: google.maps.Map) => {
    fieldMapRef.current = map;
  }, []);

  // Handle polygon complete event from the drawing manager
  const onPolygonComplete = useCallback((polygon: google.maps.Polygon) => {
    // Get the path of the polygon
    const polygonPath = polygon.getPath();
    const coordinates: PolygonCoordinates[] = [];

    // Convert the path to an array of coordinates
    for (let i = 0; i < polygonPath.getLength(); i++) {
      const point = polygonPath.getAt(i);
      coordinates.push({
        lat: point.lat(),
        lng: point.lng(),
      });
    }

    // Add the first point again to close the polygon (GeoJSON requirement)
    if (coordinates.length > 0) {
      coordinates.push({ ...coordinates[0] });
    }

    // If no field boundary maps exist, create one
    if (fieldBoundaryMaps.length === 0) {
      const newMap = {
        id: `field-boundary-${Date.now()}`,
        name: 'Field Boundary',
        description: 'Field boundary map',
        order: 0,
        elements: [{
          id: `polygon-${Date.now()}`,
          type: 'polygon',
          color: selectedColor,
          coordinates
        }]
      };

      setFieldBoundaryMaps([newMap]);
      setActiveFieldBoundaryMapIndex(0);

      // Add the new polygon to the paths state for backward compatibility
      const updatedPaths = [...fieldPaths, coordinates];
      setFieldPaths(updatedPaths);

      // Convert the updated paths to GeoJSON and call onChange
      const geoJson = pathsToGeoJson(updatedPaths);
      onChange(JSON.stringify(geoJson));
    } else if (activeFieldBoundaryMapIndex >= 0) {
      // Add the new polygon to the active field boundary map
      const updatedMaps = [...fieldBoundaryMaps];
      updatedMaps[activeFieldBoundaryMapIndex].elements.push({
        id: `polygon-${Date.now()}`,
        type: 'polygon',
        color: selectedColor,
        coordinates
      });

      setFieldBoundaryMaps(updatedMaps);

      // Update fieldPaths for backward compatibility
      const allPolygons = updatedMaps.flatMap(map => 
        map.elements
          .filter(element => element.type === 'polygon')
          .map(element => element.coordinates)
      );

      setFieldPaths(allPolygons);

      // Convert to GeoJSON and call onChange
      const geoJson = pathsToGeoJson(allPolygons);
      onChange(JSON.stringify(geoJson));
    }

    // Remove the drawn polygon since we're managing our own polygons
    polygon.setMap(null);
  }, [fieldPaths, fieldBoundaryMaps, activeFieldBoundaryMapIndex, selectedColor, onChange]);

  // Convert polygon paths to GeoJSON format
  const pathsToGeoJson = (polygonPaths: PolygonCoordinates[][]) => {
    const features = polygonPaths.map(path => {
      // Convert Google Maps LatLng format to GeoJSON format
      // GeoJSON format is [longitude, latitude], but Google Maps gives {lat, lng}
      const coordinates = [path.map(point => [point.lng, point.lat])];

      return {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates,
        },
        properties: {},
      };
    });

    return {
      type: 'FeatureCollection',
      features,
    };
  };

  // Handle drawing mode change
  const handleDrawingModeChange = useCallback((mode: any) => {
    setDrawingMode(mode);
  }, []);

  // Clear all drawn polygons from the field map
  const clearFieldPolygons = useCallback(() => {
    // Clear polygon references
    polygonRefs.current = [];

    if (activeFieldBoundaryMapIndex >= 0 && activeFieldBoundaryMapIndex < fieldBoundaryMaps.length) {
      // Clear elements in the active field boundary map
      const updatedMaps = [...fieldBoundaryMaps];
      updatedMaps[activeFieldBoundaryMapIndex].elements = [];
      setFieldBoundaryMaps(updatedMaps);
    } else {
      // If no active map, clear all maps
      setFieldBoundaryMaps([]);
    }

    // Clear field paths
    setFieldPaths([]);

    // Create an empty GeoJSON FeatureCollection
    const emptyGeoJson = {
      type: 'FeatureCollection',
      features: []
    };
    onChange(JSON.stringify(emptyGeoJson));

    // Reset drawing mode
    if (window.google && window.google.maps && window.google.maps.drawing) {
      handleDrawingModeChange(null);
    }
  }, [onChange, handleDrawingModeChange, activeFieldBoundaryMapIndex, fieldBoundaryMaps]);

  // Open add field boundary map dialog
  const openAddFieldBoundaryMapDialog = useCallback(() => {
    setMapDialogMode('add');
    setMapDialogType('field');
    setMapDialogData({
      name: '',
      description: ''
    });
    setMapDialogOpen(true);
  }, []);

  // Open edit field boundary map dialog
  const openEditFieldBoundaryMapDialog = useCallback((index: number) => {
    if (index < 0 || index >= fieldBoundaryMaps.length) return;

    setMapDialogMode('edit');
    setMapDialogType('field');
    setMapDialogData({
      name: fieldBoundaryMaps[index].name || '',
      description: fieldBoundaryMaps[index].description || ''
    });
    setMapDialogOpen(true);
  }, [fieldBoundaryMaps]);

  // Move field boundary map up in order
  const moveFieldBoundaryMapUp = useCallback(() => {
    if (activeFieldBoundaryMapIndex <= 0 || activeFieldBoundaryMapIndex >= fieldBoundaryMaps.length) return;

    const updatedMaps = [...fieldBoundaryMaps];
    const temp = updatedMaps[activeFieldBoundaryMapIndex];
    updatedMaps[activeFieldBoundaryMapIndex] = updatedMaps[activeFieldBoundaryMapIndex - 1];
    updatedMaps[activeFieldBoundaryMapIndex - 1] = temp;

    // Update order property if it exists
    if (updatedMaps[activeFieldBoundaryMapIndex].order !== undefined && 
        updatedMaps[activeFieldBoundaryMapIndex - 1].order !== undefined) {
      const tempOrder = updatedMaps[activeFieldBoundaryMapIndex].order;
      updatedMaps[activeFieldBoundaryMapIndex].order = updatedMaps[activeFieldBoundaryMapIndex - 1].order;
      updatedMaps[activeFieldBoundaryMapIndex - 1].order = tempOrder;
    }

    setFieldBoundaryMaps(updatedMaps);
    setActiveFieldBoundaryMapIndex(activeFieldBoundaryMapIndex - 1);

    // Update fieldPaths for backward compatibility
    updateFieldPathsFromMaps(updatedMaps);
  }, [activeFieldBoundaryMapIndex, fieldBoundaryMaps]);

  // Move field boundary map down in order
  const moveFieldBoundaryMapDown = useCallback(() => {
    if (activeFieldBoundaryMapIndex < 0 || activeFieldBoundaryMapIndex >= fieldBoundaryMaps.length - 1) return;

    const updatedMaps = [...fieldBoundaryMaps];
    const temp = updatedMaps[activeFieldBoundaryMapIndex];
    updatedMaps[activeFieldBoundaryMapIndex] = updatedMaps[activeFieldBoundaryMapIndex + 1];
    updatedMaps[activeFieldBoundaryMapIndex + 1] = temp;

    // Update order property if it exists
    if (updatedMaps[activeFieldBoundaryMapIndex].order !== undefined && 
        updatedMaps[activeFieldBoundaryMapIndex + 1].order !== undefined) {
      const tempOrder = updatedMaps[activeFieldBoundaryMapIndex].order;
      updatedMaps[activeFieldBoundaryMapIndex].order = updatedMaps[activeFieldBoundaryMapIndex + 1].order;
      updatedMaps[activeFieldBoundaryMapIndex + 1].order = tempOrder;
    }

    setFieldBoundaryMaps(updatedMaps);
    setActiveFieldBoundaryMapIndex(activeFieldBoundaryMapIndex + 1);

    // Update fieldPaths for backward compatibility
    updateFieldPathsFromMaps(updatedMaps);
  }, [activeFieldBoundaryMapIndex, fieldBoundaryMaps]);

  // Delete field boundary map
  const deleteFieldBoundaryMap = useCallback(() => {
    if (activeFieldBoundaryMapIndex < 0 || activeFieldBoundaryMapIndex >= fieldBoundaryMaps.length) return;

    if (!confirm('Are you sure you want to delete this field boundary map? This action cannot be undone.')) {
      return;
    }

    const updatedMaps = fieldBoundaryMaps.filter((_, index) => index !== activeFieldBoundaryMapIndex);
    setFieldBoundaryMaps(updatedMaps);
    setActiveFieldBoundaryMapIndex(Math.min(activeFieldBoundaryMapIndex, updatedMaps.length - 1));

    // Update fieldPaths for backward compatibility
    updateFieldPathsFromMaps(updatedMaps);
  }, [activeFieldBoundaryMapIndex, fieldBoundaryMaps]);

  // Helper function to update fieldPaths from maps
  const updateFieldPathsFromMaps = useCallback((maps: any[]) => {
    const allPolygons = maps.flatMap(map => 
      map.elements
        .filter((element: any) => element.type === 'polygon')
        .map((element: any) => element.coordinates)
    );

    setFieldPaths(allPolygons);

    // Convert to GeoJSON and call onChange
    const geoJson = pathsToGeoJson(allPolygons);
    onChange(JSON.stringify(geoJson));
  }, [onChange]);

  // Helper function to safely check drawing mode
  const isDrawingMode = useCallback((mode: string): boolean => {
    if (!window.google || !window.google.maps || !window.google.maps.drawing) {
      return false;
    }

    if (mode === 'POLYGON') {
      return drawingMode === google.maps.drawing.OverlayType.POLYGON;
    } else if (mode === 'POLYLINE') {
      return drawingMode === google.maps.drawing.OverlayType.POLYLINE;
    } else if (mode === 'MARKER') {
      return drawingMode === google.maps.drawing.OverlayType.MARKER;
    } else if (mode === 'null') {
      return drawingMode === null;
    }

    return false;
  }, [drawingMode]);

  // Handle harvest map load
  const onHarvestMapLoad = useCallback((map: google.maps.Map) => {
    harvestMapRef.current = map;
  }, []);

  // Move map up in order
  const moveMapUp = useCallback(() => {
    if (activeHarvestMapIndex <= 0 || activeHarvestMapIndex >= harvestMaps.length) return;

    const updatedMaps = [...harvestMaps];
    const temp = updatedMaps[activeHarvestMapIndex];
    updatedMaps[activeHarvestMapIndex] = updatedMaps[activeHarvestMapIndex - 1];
    updatedMaps[activeHarvestMapIndex - 1] = temp;

    // Update order property if it exists
    if (updatedMaps[activeHarvestMapIndex].order !== undefined && 
        updatedMaps[activeHarvestMapIndex - 1].order !== undefined) {
      const tempOrder = updatedMaps[activeHarvestMapIndex].order;
      updatedMaps[activeHarvestMapIndex].order = updatedMaps[activeHarvestMapIndex - 1].order;
      updatedMaps[activeHarvestMapIndex - 1].order = tempOrder;
    }

    setHarvestMaps(updatedMaps);
    setActiveHarvestMapIndex(activeHarvestMapIndex - 1);
  }, [activeHarvestMapIndex, harvestMaps]);

  // Move map down in order
  const moveMapDown = useCallback(() => {
    if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length - 1) return;

    const updatedMaps = [...harvestMaps];
    const temp = updatedMaps[activeHarvestMapIndex];
    updatedMaps[activeHarvestMapIndex] = updatedMaps[activeHarvestMapIndex + 1];
    updatedMaps[activeHarvestMapIndex + 1] = temp;

    // Update order property if it exists
    if (updatedMaps[activeHarvestMapIndex].order !== undefined && 
        updatedMaps[activeHarvestMapIndex + 1].order !== undefined) {
      const tempOrder = updatedMaps[activeHarvestMapIndex].order;
      updatedMaps[activeHarvestMapIndex].order = updatedMaps[activeHarvestMapIndex + 1].order;
      updatedMaps[activeHarvestMapIndex + 1].order = tempOrder;
    }

    setHarvestMaps(updatedMaps);
    setActiveHarvestMapIndex(activeHarvestMapIndex + 1);
  }, [activeHarvestMapIndex, harvestMaps]);

  // Open add map dialog
  const openAddMapDialog = useCallback(() => {
    setMapDialogMode('add');
    setMapDialogType('harvest');
    setMapDialogData({
      name: '',
      description: ''
    });
    setMapDialogOpen(true);
  }, []);

  // Open edit map dialog
  const openEditMapDialog = useCallback((index: number) => {
    if (index < 0 || index >= harvestMaps.length) return;

    setMapDialogMode('edit');
    setMapDialogType('harvest');
    setMapDialogData({
      name: harvestMaps[index].name || '',
      description: harvestMaps[index].description || ''
    });
    setMapDialogOpen(true);
  }, [harvestMaps]);

  // Handle map dialog submit
  const handleMapDialogSubmit = useCallback(() => {
    if (!mapDialogData.name.trim()) {
      alert('Map name is required');
      return;
    }

    if (mapDialogType === 'harvest') {
      // Handle harvest maps
      if (mapDialogMode === 'add') {
        // Create a new harvest map
        const newMap = {
          id: `harvest-map-${Date.now()}`, // Generate a temporary ID
          name: mapDialogData.name,
          description: mapDialogData.description,
          order: harvestMaps.length,
          elements: []
        };

        const updatedMaps = [...harvestMaps, newMap];
        setHarvestMaps(updatedMaps);
        setActiveHarvestMapIndex(updatedMaps.length - 1);
      } else if (mapDialogMode === 'edit' && activeHarvestMapIndex >= 0) {
        // Update existing harvest map
        const updatedMaps = [...harvestMaps];
        updatedMaps[activeHarvestMapIndex] = {
          ...updatedMaps[activeHarvestMapIndex],
          name: mapDialogData.name,
          description: mapDialogData.description
        };
        setHarvestMaps(updatedMaps);
      }
    } else if (mapDialogType === 'field') {
      // Handle field boundary maps
      if (mapDialogMode === 'add') {
        // Create a new field boundary map
        const newMap = {
          id: `field-boundary-${Date.now()}`, // Generate a temporary ID
          name: mapDialogData.name,
          description: mapDialogData.description,
          order: fieldBoundaryMaps.length,
          elements: []
        };

        const updatedMaps = [...fieldBoundaryMaps, newMap];
        setFieldBoundaryMaps(updatedMaps);
        setActiveFieldBoundaryMapIndex(updatedMaps.length - 1);
      } else if (mapDialogMode === 'edit' && activeFieldBoundaryMapIndex >= 0) {
        // Update existing field boundary map
        const updatedMaps = [...fieldBoundaryMaps];
        updatedMaps[activeFieldBoundaryMapIndex] = {
          ...updatedMaps[activeFieldBoundaryMapIndex],
          name: mapDialogData.name,
          description: mapDialogData.description
        };
        setFieldBoundaryMaps(updatedMaps);
      }

      // Update fieldPaths for backward compatibility
      updateFieldPathsFromMaps(fieldBoundaryMaps);
    }

    setMapDialogOpen(false);
  }, [mapDialogMode, mapDialogType, mapDialogData, harvestMaps, activeHarvestMapIndex, fieldBoundaryMaps, activeFieldBoundaryMapIndex, updateFieldPathsFromMaps]);

  // Clear harvest elements
  const clearHarvestElements = useCallback(() => {
    if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length) return;

    const updatedMaps = [...harvestMaps];
    updatedMaps[activeHarvestMapIndex].elements = [];
    setHarvestMaps(updatedMaps);

    // Update in backend would go here
    updateHarvestDirectionMap(updatedMaps[activeHarvestMapIndex].id, {
      elements: []
    }).catch(error => {
      console.error('Error clearing harvest elements:', error);
    });
  }, [activeHarvestMapIndex, harvestMaps]);

  // Delete harvest map
  const deleteHarvestMap = useCallback(() => {
    if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length) return;

    if (!confirm('Are you sure you want to delete this map? This action cannot be undone.')) {
      return;
    }

    const mapId = harvestMaps[activeHarvestMapIndex].id;
    const updatedMaps = harvestMaps.filter((_, index) => index !== activeHarvestMapIndex);

    setHarvestMaps(updatedMaps);
    setActiveHarvestMapIndex(Math.min(activeHarvestMapIndex, updatedMaps.length - 1));

    // Delete from backend would go here
    // API call to delete the map
  }, [activeHarvestMapIndex, harvestMaps]);

  // Handle polyline complete
  const onPolylineComplete = useCallback((polyline: google.maps.Polyline) => {
    if (activeHarvestMapIndex < 0 || activeHarvestMapIndex >= harvestMaps.length) {
      alert('Please select or add a harvest direction map first.');
      polyline.setMap(null);
      return;
    }

    // Get the path of the polyline
    const polylinePath = polyline.getPath();
    const coordinates: PolygonCoordinates[] = [];

    // Convert the path to an array of coordinates
    for (let i = 0; i < polylinePath.getLength(); i++) {
      const point = polylinePath.getAt(i);
      coordinates.push({
        lat: point.lat(),
        lng: point.lng(),
      });
    }

    // Create a new element
    const newElement = {
      id: `polyline-${Date.now()}`,
      type: 'polyline',
      color: selectedColor,
      coordinates
    };

    // Add the new element to the current harvest map
    const updatedMaps = [...harvestMaps];
    updatedMaps[activeHarvestMapIndex].elements = [
      ...updatedMaps[activeHarvestMapIndex].elements,
      newElement
    ];

    setHarvestMaps(updatedMaps);

    // Update in backend would go here
    updateHarvestDirectionMap(updatedMaps[activeHarvestMapIndex].id, {
      elements: updatedMaps[activeHarvestMapIndex].elements
    }).catch(error => {
      console.error('Error adding polyline to harvest direction map:', error);
    });

    // Remove the drawn polyline since we're managing our own
    polyline.setMap(null);
  }, [activeHarvestMapIndex, harvestMaps, selectedColor]);

  // Handle marker complete
  const onMarkerComplete = useCallback((marker: google.maps.Marker) => {
    // Get the position of the marker
    const position = marker.getPosition();
    if (!position) return;

    const coordinates = {
      lat: position.lat(),
      lng: position.lng(),
    };

    // Find the selected symbol object
    const symbolObj = symbolOptions.find(s => s.id === selectedSymbol) || symbolOptions[0];

    // Create a new element with all symbol properties
    const newElement = {
      id: `marker-${Date.now()}`,
      type: 'marker',
      color: selectedColor,
      coordinates,
      label: '',
      symbolType: selectedSymbol,
      symbolName: symbolObj.name,
      symbolProperties: {
        path: symbolObj.path,
        scale: symbolObj.scale || 8,
        fillOpacity: symbolObj.fillOpacity !== undefined ? symbolObj.fillOpacity : 1,
        strokeWeight: symbolObj.strokeWeight || 1
      }
    };

    // Check if we're in the harvest map or field boundary map context
    if (activeHarvestMapIndex >= 0 && activeHarvestMapIndex < harvestMaps.length) {
      // Add the new element to the current harvest map
      const updatedMaps = [...harvestMaps];
      updatedMaps[activeHarvestMapIndex].elements = [
        ...updatedMaps[activeHarvestMapIndex].elements,
        newElement
      ];

      setHarvestMaps(updatedMaps);

      // Update in backend would go here
      updateHarvestDirectionMap(updatedMaps[activeHarvestMapIndex].id, {
        elements: updatedMaps[activeHarvestMapIndex].elements
      }).catch(error => {
        console.error('Error adding marker to harvest direction map:', error);
      });
    } else if (activeFieldBoundaryMapIndex >= 0 && activeFieldBoundaryMapIndex < fieldBoundaryMaps.length) {
      // Add the new element to the current field boundary map
      const updatedMaps = [...fieldBoundaryMaps];
      updatedMaps[activeFieldBoundaryMapIndex].elements = [
        ...updatedMaps[activeFieldBoundaryMapIndex].elements,
        newElement
      ];

      setFieldBoundaryMaps(updatedMaps);

      // Update fieldPaths for backward compatibility
      updateFieldPathsFromMaps(updatedMaps);
    } else {
      console.warn('No active map selected for adding marker');
      alert('Please select or add a harvest direction map first.');
      marker.setMap(null);
      return;
    }

    // Remove the drawn marker since we're managing our own
    marker.setMap(null);

    // Open label input for the new marker
    setActiveLabelInput({
      position: coordinates,
      text: ''
    });
  }, [activeHarvestMapIndex, harvestMaps, activeFieldBoundaryMapIndex, fieldBoundaryMaps, selectedColor, selectedSymbol, symbolOptions]);

  // Handle label submit
  const handleLabelSubmit = useCallback(() => {
    if (!activeLabelInput) return;

    // Check if we're in the harvest map context
    if (activeHarvestMapIndex >= 0 && activeHarvestMapIndex < harvestMaps.length) {
      // Find the marker element with the matching position
      const updatedMaps = [...harvestMaps];
      const elementIndex = updatedMaps[activeHarvestMapIndex].elements.findIndex(
        element => element.type === 'marker' && 
                  element.coordinates && 
                  typeof element.coordinates !== 'string' &&
                  element.coordinates.lat === activeLabelInput.position.lat && 
                  element.coordinates.lng === activeLabelInput.position.lng
      );

      if (elementIndex !== -1) {
        // Update the label
        updatedMaps[activeHarvestMapIndex].elements[elementIndex].label = activeLabelInput.text;

        // Update the harvest map
        setHarvestMaps(updatedMaps);

        // Update in backend would go here
        updateHarvestDirectionMap(updatedMaps[activeHarvestMapIndex].id, {
          elements: updatedMaps[activeHarvestMapIndex].elements
        }).catch(error => {
          console.error('Error updating marker label in harvest direction map:', error);
        });
      }
    } 
    // Check if we're in the field boundary map context
    else if (activeFieldBoundaryMapIndex >= 0 && activeFieldBoundaryMapIndex < fieldBoundaryMaps.length) {
      // Find the marker element with the matching position
      const updatedMaps = [...fieldBoundaryMaps];
      const elementIndex = updatedMaps[activeFieldBoundaryMapIndex].elements.findIndex(
        element => element.type === 'marker' && 
                  element.coordinates && 
                  typeof element.coordinates !== 'string' &&
                  element.coordinates.lat === activeLabelInput.position.lat && 
                  element.coordinates.lng === activeLabelInput.position.lng
      );

      if (elementIndex !== -1) {
        // Update the label
        updatedMaps[activeFieldBoundaryMapIndex].elements[elementIndex].label = activeLabelInput.text;

        // Update the field boundary map
        setFieldBoundaryMaps(updatedMaps);

        // Update fieldPaths for backward compatibility
        updateFieldPathsFromMaps(updatedMaps);
      }
    }

    // Close the label input
    setActiveLabelInput(null);
  }, [activeLabelInput, activeHarvestMapIndex, harvestMaps, activeFieldBoundaryMapIndex, fieldBoundaryMaps]);

  // Update harvest direction map in backend
  const updateHarvestDirectionMap = async (mapId: string, data: any) => {
    try {
      // This would be an API call to update the map in the backend
      // For now, we'll just log the data
      console.log(`Updating map ${mapId} with data:`, data);
      return Promise.resolve();
    } catch (error) {
      console.error('Error updating harvest direction map:', error);
      return Promise.reject(error);
    }
  };

  // State for Google Maps API loading
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<Error | null>(null);

  // Load the Google Maps API using the googleMapsLoader utility
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['drawing', 'places']);
        setIsLoaded(true);
        // Initialize drawing mode after Google Maps API is loaded
        if (window.google && window.google.maps && window.google.maps.drawing) {
          setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
        }
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setLoadError(error instanceof Error ? error : new Error('Failed to load Google Maps API'));
      }
    };

    if (!isGoogleMapsLoaded()) {
      loadMapsApi();
    } else {
      setIsLoaded(true);
      // Initialize drawing mode if Google Maps API is already loaded
      if (window.google && window.google.maps && window.google.maps.drawing) {
        setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
      }
    }
  }, []);

  // Handle loading error
  if (loadError) {
    return (
      <div className="enhanced-map-drawing-component">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">Error loading Google Maps API. Please try again later.</span>
        </div>
      </div>
    );
  }

  // Show loading indicator while the API is loading
  if (!isLoaded) {
    return (
      <div className="enhanced-map-drawing-component">
        <div className="flex justify-center items-center" style={{ height: height }}>
          <div className="text-gray-500">Loading map...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="enhanced-map-drawing-component">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Field Boundaries</h3>

        {/* Field Boundary Maps - Empty State */}
        {fieldBoundaryMaps.length === 0 && (
          <div className="bg-white p-6 rounded-md shadow-sm border border-gray-200 text-center">
            <p className="text-gray-600 mb-4">No field boundary maps have been created yet. Create your first map to get started.</p>
            <button
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              onClick={openAddFieldBoundaryMapDialog}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Create First Field Boundary Map
            </button>
          </div>
        )}

        {/* Field Boundary Maps - With Maps */}
        {fieldBoundaryMaps.length > 0 && (
          <>
            {/* Field Boundary Map Tabs */}
            <div className="mb-4">
              <div className="flex border-b border-gray-200">
                {fieldBoundaryMaps
                  .sort((a, b) => (a.order || 0) - (b.order || 0))
                  .map((map, index) => (
                    <button
                      key={map.id}
                      className={`px-4 py-2 text-sm font-medium ${
                        index === activeFieldBoundaryMapIndex
                          ? 'text-primary-600 border-b-2 border-primary-600'
                          : 'text-gray-500 hover:text-gray-700'
                      }`}
                      onClick={() => setActiveFieldBoundaryMapIndex(index)}
                    >
                      <span className="inline-flex items-center">
                        <span className="w-5 h-5 flex items-center justify-center rounded-full bg-gray-200 text-xs mr-2">
                          {map.order || index + 1}
                        </span>
                        {map.name}
                      </span>
                    </button>
                  ))}
                <button
                  className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700"
                  onClick={openAddFieldBoundaryMapDialog}
                >
                  + Add Map
                </button>
              </div>
            </div>

            {/* Enhanced Drawing Tools */}
            <div className="bg-white p-4 mb-4 rounded-md shadow-sm border border-gray-200">
              <h4 className="text-md font-medium text-gray-900 mb-2">Field Boundary Drawing Tools</h4>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                {/* Drawing Tools */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Drawing Tools</h5>
                  <div className="flex flex-wrap gap-2">
                    <button
                      type="button"
                      className={`inline-flex items-center px-3 py-2 border ${
                        isDrawingMode('POLYGON')
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-300 bg-white text-gray-700'
                      } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                      onClick={() => window.google && window.google.maps && window.google.maps.drawing && handleDrawingModeChange(isDrawingMode('POLYGON') ? null : google.maps.drawing.OverlayType.POLYGON)}
                      title="Click to toggle drawing mode. When active, click points on the map to create a shape, then click the first point again to complete."
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM14 11a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1h-1a1 1 0 110-2h1v-1a1 1 0 011-1z" />
                      </svg>
                      Draw Field
                    </button>
                    <button
                      type="button"
                      className={`inline-flex items-center px-3 py-2 border ${
                        isDrawingMode('MARKER')
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-300 bg-white text-gray-700'
                      } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                      onClick={() => window.google && window.google.maps && window.google.maps.drawing && handleDrawingModeChange(google.maps.drawing.OverlayType.MARKER)}
                      title="Add hazard markers to indicate field dangers like rocks, holes, trees, etc."
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                      </svg>
                      Add Hazard
                    </button>
                    <button
                      type="button"
                      className={`inline-flex items-center px-3 py-2 border ${
                        isDrawingMode('null')
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-300 bg-white text-gray-700'
                      } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                      onClick={() => handleDrawingModeChange(null)}
                      title="Select and edit existing elements"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6.672 1.911a1 1 0 10-1.932.518l.259.966a1 1 0 001.932-.518l-.26-.966zM2.429 4.74a1 1 0 10-.517 1.932l.966.259a1 1 0 00.517-1.932l-.966-.26zm8.814-.569a1 1 0 00-1.415-1.414l-.707.707a1 1 0 101.415 1.415l.707-.708zm-7.071 7.072l.707-.707A1 1 0 003.465 9.12l-.708.707a1 1 0 001.415 1.415zm3.2-5.171a1 1 0 00-1.3 1.3l4 10a1 1 0 001.823.075l1.38-2.759 3.018 3.02a1 1 0 001.414-1.415l-3.019-3.02 2.76-1.379a1 1 0 00-.076-1.822l-10-4z" clipRule="evenodd" />
                      </svg>
                      Select/Edit
                    </button>
                  </div>
                </div>

                {/* Color Selector */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Field Color</h5>
                  <div className="grid grid-cols-4 gap-2">
                    {colorOptions.map(color => (
                      <button
                        key={color}
                        type="button"
                        className={`w-full h-8 rounded-md flex items-center justify-center ${
                          selectedColor === color ? 'ring-2 ring-offset-2 ring-gray-500' : 'ring-1 ring-gray-200'
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => setSelectedColor(color)}
                        title={`Select ${color} color`}
                      >
                        {selectedColor === color && (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Hazard Symbol Selector */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Hazard Symbols</h5>
                  <div className="grid grid-cols-2 gap-2">
                    {symbolOptions.map(symbol => (
                      <button
                        key={symbol.id}
                        type="button"
                        className={`w-full h-8 rounded-md flex items-center justify-center ${
                          selectedSymbol === symbol.id ? 'bg-primary-100 border-primary-500 border-2' : 'bg-white border border-gray-300'
                        }`}
                        onClick={() => setSelectedSymbol(symbol.id)}
                        title={`Add ${symbol.name}`}
                      >
                        <span className="text-sm">{symbol.name}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Map Actions */}
                <div className="bg-gray-50 p-3 rounded-md">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Map Actions</h5>
                  <div className="flex flex-col space-y-2">
                    <button
                      type="button"
                      onClick={clearFieldPolygons}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      title="Clear all elements from the current map"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      Clear Current Map
                    </button>
                    <button
                      type="button"
                      onClick={deleteFieldBoundaryMap}
                      className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      title="Delete the current map (cannot be undone)"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      Delete Map
                    </button>
                  </div>
                </div>
              </div>

              <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md border border-blue-100">
                <h5 className="font-medium text-blue-800 mb-1">How to Use Field Boundary Maps</h5>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Use <strong>Draw Field</strong> to create field boundaries</li>
                  <li>Use <strong>Add Hazard</strong> to mark field dangers like rocks, holes, trees, etc.</li>
                  <li>Select different <strong>hazard symbols</strong> to indicate specific types of field dangers</li>
                  <li>Select different <strong>colors</strong> to indicate different types of fields or hazards</li>
                  <li>Create <strong>multiple maps</strong> for different field layouts or scenarios</li>
                  <li>Use the <strong>Select/Edit</strong> tool to modify existing boundaries and hazards</li>
                  <li><strong>Edit existing:</strong> Click and drag any point to adjust the shape. Click and drag a line to add a new point.</li>
                </ul>
              </div>
            </div>
          </>
        )}

        <div className="relative">
          <GoogleMap
            mapContainerStyle={customMapContainerStyle}
            center={mapCenter}
            zoom={5}
            onLoad={onFieldMapLoad}
            mapTypeId="satellite"
            options={{
              mapTypeControl: true,
              streetViewControl: false,
              fullscreenControl: true,
              mapTypeControlOptions: {
                mapTypeIds: ["roadmap", "satellite", "hybrid", "terrain"],
                position: window.google?.maps?.ControlPosition?.TOP_RIGHT
              }
            }}
          >
            {/* Drawing Manager for field boundary elements */}
            <DrawingManager
              key={`field-drawing-manager-${selectedSymbol}-${selectedColor}`} // Force re-render when symbol or color changes
              drawingMode={drawingMode}
              onPolygonComplete={onPolygonComplete}
              onMarkerComplete={onMarkerComplete}
              options={{
                drawingControl: false, // Hide default controls, we're using our own
                polygonOptions: {
                  fillColor: selectedColor,
                  fillOpacity: 0.3,
                  strokeWeight: 2,
                  strokeColor: selectedColor,
                  clickable: true,
                  editable: true,
                  zIndex: 1,
                },
                markerOptions: {
                  icon: (() => {
                    const symbolObj = symbolOptions.find(s => s.id === selectedSymbol) || symbolOptions[0];
                    return {
                      path: symbolObj.path,
                      scale: symbolObj.scale || 8,
                      fillColor: selectedColor,
                      fillOpacity: symbolObj.fillOpacity !== undefined ? symbolObj.fillOpacity : 1,
                      strokeWeight: symbolObj.strokeWeight || 1,
                      strokeColor: '#ffffff',
                    };
                  })(),
                  clickable: true,
                  draggable: true,
                  zIndex: 2,
                },
              }}
            />

            {/* Render elements from active field boundary map */}
            {activeFieldBoundaryMapIndex >= 0 && 
             activeFieldBoundaryMapIndex < fieldBoundaryMaps.length && 
             fieldBoundaryMaps[activeFieldBoundaryMapIndex].elements.map((element: any, elementIndex: number) => {
               if (element.type === 'polygon') {
                 return (
                   <Polygon
                     key={element.id}
                     paths={element.coordinates}
                     options={{
                       fillColor: element.color || '#22c55e',
                       fillOpacity: 0.3,
                       strokeWeight: 2,
                       strokeColor: element.color || '#22c55e',
                       clickable: true,
                       editable: true,
                       zIndex: 1,
                       draggable: true
                     }}
                     onLoad={(polygon) => {
                       // Store reference to the polygon instance
                       if (elementIndex < polygonRefs.current.length) {
                         polygonRefs.current[elementIndex] = polygon;
                       } else {
                         polygonRefs.current.push(polygon);
                       }
                     }}
                     onMouseUp={() => {
                       // Update the paths when the polygon is edited
                       if (!window.google || !window.google.maps) return;

                       const polygon = polygonRefs.current[elementIndex];
                       if (!polygon) return;

                       // Get the updated path from the polygon
                       const path = polygon.getPath();
                       const updatedPath: PolygonCoordinates[] = [];

                       for (let i = 0; i < path.getLength(); i++) {
                         const point = path.getAt(i);
                         updatedPath.push({
                           lat: point.lat(),
                           lng: point.lng()
                         });
                       }

                       // Add the first point again to close the polygon (GeoJSON requirement)
                       if (updatedPath.length > 0) {
                         updatedPath.push({ ...updatedPath[0] });
                       }

                       // Update the element in the field boundary map
                       const updatedMaps = [...fieldBoundaryMaps];
                       updatedMaps[activeFieldBoundaryMapIndex].elements[elementIndex].coordinates = updatedPath;
                       setFieldBoundaryMaps(updatedMaps);

                       // Update fieldPaths for backward compatibility
                       updateFieldPathsFromMaps(updatedMaps);
                     }}
                     onDragEnd={() => {
                       // Update the paths when the polygon is dragged
                       if (!window.google || !window.google.maps) return;

                       const polygon = polygonRefs.current[elementIndex];
                       if (!polygon) return;

                       // Get the updated path from the polygon
                       const path = polygon.getPath();
                       const updatedPath: PolygonCoordinates[] = [];

                       for (let i = 0; i < path.getLength(); i++) {
                         const point = path.getAt(i);
                         updatedPath.push({
                           lat: point.lat(),
                           lng: point.lng()
                         });
                       }

                       // Add the first point again to close the polygon (GeoJSON requirement)
                       if (updatedPath.length > 0) {
                         updatedPath.push({ ...updatedPath[0] });
                       }

                       // Update the element in the field boundary map
                       const updatedMaps = [...fieldBoundaryMaps];
                       updatedMaps[activeFieldBoundaryMapIndex].elements[elementIndex].coordinates = updatedPath;
                       setFieldBoundaryMaps(updatedMaps);

                       // Update fieldPaths for backward compatibility
                       updateFieldPathsFromMaps(updatedMaps);
                     }}
                   />
                 );
               } else if (element.type === 'marker') {
                 // Render marker with hazard symbol
                 return (
                   <Marker
                     key={element.id}
                     position={element.coordinates as PolygonCoordinates}
                     icon={{
                       path: element.symbolProperties?.path || google.maps.SymbolPath.CIRCLE,
                       scale: element.symbolProperties?.scale || 8,
                       fillColor: element.color,
                       fillOpacity: element.symbolProperties?.fillOpacity !== undefined ? element.symbolProperties.fillOpacity : 1,
                       strokeWeight: element.symbolProperties?.strokeWeight || 1,
                       strokeColor: '#ffffff',
                     }}
                     clickable={true}
                     draggable={true}
                     onDragEnd={(e) => {
                       // Update the marker position when dragged
                       if (!e || !e.latLng) return;

                       const updatedPosition = {
                         lat: e.latLng.lat(),
                         lng: e.latLng.lng()
                       };

                       // Update the element in the field boundary map
                       const updatedMaps = [...fieldBoundaryMaps];
                       updatedMaps[activeFieldBoundaryMapIndex].elements[elementIndex].coordinates = updatedPosition;
                       setFieldBoundaryMaps(updatedMaps);

                       // Update fieldPaths for backward compatibility
                       updateFieldPathsFromMaps(updatedMaps);
                     }}
                     onClick={() => {
                       // Open label input when marker is clicked
                       if (element.coordinates && typeof element.coordinates !== 'string') {
                         const position = element.coordinates as PolygonCoordinates;
                         setActiveLabelInput({
                           position,
                           text: element.label || '',
                         });
                       }
                     }}
                   >
                     {element.label && (
                       <InfoWindow
                         position={element.coordinates as PolygonCoordinates}
                         options={{
                           pixelOffset: new google.maps.Size(0, -30),
                         }}
                       >
                         <div className="p-1">
                           <p className="text-sm">{element.label}</p>
                         </div>
                       </InfoWindow>
                     )}
                   </Marker>
                 );
               }
               return null;
             })}
          </GoogleMap>
        </div>

        <p className="mt-1 text-sm text-gray-500">
          Draw field boundaries by clicking the polygon tool and then clicking on the map to create points. 
          Click the first point again to complete the polygon. Create multiple field boundary maps for different scenarios.
        </p>
      </div>

      {/* Harvest Direction Map - Empty State */}
      {fieldBoundaryMaps.length > 0 && harvestMaps.length === 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Harvest Direction Maps</h3>
          <div className="bg-white p-6 rounded-md shadow-sm border border-gray-200 text-center">
            <p className="text-gray-600 mb-4">No harvest direction maps have been created yet. Create your first map to get started.</p>
            <button
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              onClick={openAddMapDialog}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Create First Map
            </button>
          </div>
        </div>
      )}

      {/* Harvest Direction Map - With Maps */}
      {fieldBoundaryMaps.length > 0 && harvestMaps.length > 0 && (
        <div className="mt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Harvest Direction Maps</h3>

          {/* Harvest Map Tabs */}
          <div className="mb-4">
            <div className="flex border-b border-gray-200">
              {harvestMaps
                .sort((a, b) => (a.order || 0) - (b.order || 0))
                .map((map, index) => (
                  <button
                    key={map.id}
                    className={`px-4 py-2 text-sm font-medium ${
                      index === activeHarvestMapIndex
                        ? 'text-primary-600 border-b-2 border-primary-600'
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={() => setActiveHarvestMapIndex(index)}
                  >
                    <span className="inline-flex items-center">
                      <span className="w-5 h-5 flex items-center justify-center rounded-full bg-gray-200 text-xs mr-2">
                        {map.order || index + 1}
                      </span>
                      {map.name}
                    </span>
                  </button>
                ))}
              <button
                className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700"
                onClick={openAddMapDialog}
              >
                + Add Map
              </button>
            </div>

            {/* Active Map Info */}
            {activeHarvestMapIndex >= 0 && activeHarvestMapIndex < harvestMaps.length && (
              <div className="mt-2 p-3 bg-gray-50 rounded-md">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="text-md font-medium text-gray-900">
                      {harvestMaps[activeHarvestMapIndex].name}
                    </h4>
                    {harvestMaps[activeHarvestMapIndex].description && (
                      <p className="text-sm text-gray-600 mt-1">
                        {harvestMaps[activeHarvestMapIndex].description}
                      </p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={() => openEditMapDialog(activeHarvestMapIndex)}
                      className="p-1 text-gray-500 hover:text-gray-700"
                      title="Edit Map Details"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                    </button>
                    <button
                      type="button"
                      onClick={moveMapUp}
                      disabled={activeHarvestMapIndex <= 0}
                      className={`p-1 ${activeHarvestMapIndex <= 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-gray-700'}`}
                      title="Move Up"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                    <button
                      type="button"
                      onClick={moveMapDown}
                      disabled={activeHarvestMapIndex >= harvestMaps.length - 1}
                      className={`p-1 ${activeHarvestMapIndex >= harvestMaps.length - 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-gray-700'}`}
                      title="Move Down"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Drawing Tools */}
          <div className="bg-white p-4 mb-4 rounded-md shadow-sm border border-gray-200">
            <h4 className="text-md font-medium text-gray-900 mb-2">Harvest Direction Drawing Tools</h4>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
              {/* Drawing Tools */}
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Drawing Tools</h5>
                <div className="flex flex-wrap gap-2">
                  <button
                    type="button"
                    className={`inline-flex items-center px-3 py-2 border ${
                      isDrawingMode('POLYLINE')
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-300 bg-white text-gray-700'
                    } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                    onClick={() => window.google && window.google.maps && window.google.maps.drawing && handleDrawingModeChange(google.maps.drawing.OverlayType.POLYLINE)}
                    title="Draw directional lines to show harvest paths"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                    Draw Path
                  </button>
                  <button
                    type="button"
                    className={`inline-flex items-center px-3 py-2 border ${
                      isDrawingMode('MARKER')
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-300 bg-white text-gray-700'
                    } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                    onClick={() => window.google && window.google.maps && window.google.maps.drawing && handleDrawingModeChange(google.maps.drawing.OverlayType.MARKER)}
                    title="Add markers with labels for important points"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                    </svg>
                    Add Marker
                  </button>
                  <button
                    type="button"
                    className={`inline-flex items-center px-3 py-2 border ${
                      isDrawingMode('null')
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-300 bg-white text-gray-700'
                    } shadow-sm text-sm leading-4 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500`}
                    onClick={() => handleDrawingModeChange(null)}
                    title="Select and edit existing elements"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M6.672 1.911a1 1 0 10-1.932.518l.259.966a1 1 0 001.932-.518l-.26-.966zM2.429 4.74a1 1 0 10-.517 1.932l.966.259a1 1 0 00.517-1.932l-.966-.26zm8.814-.569a1 1 0 00-1.415-1.414l-.707.707a1 1 0 101.415 1.415l.707-.708zm-7.071 7.072l.707-.707A1 1 0 003.465 9.12l-.708.707a1 1 0 001.415 1.415zm3.2-5.171a1 1 0 00-1.3 1.3l4 10a1 1 0 001.823.075l1.38-2.759 3.018 3.02a1 1 0 001.414-1.415l-3.019-3.02 2.76-1.379a1 1 0 00-.076-1.822l-10-4z" clipRule="evenodd" />
                    </svg>
                    Select/Edit
                  </button>
                </div>
              </div>

              {/* Color Selector */}
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Element Color</h5>
                <div className="grid grid-cols-4 gap-2">
                  {colorOptions.map(color => (
                    <button
                      key={color}
                      type="button"
                      className={`w-full h-8 rounded-md flex items-center justify-center ${
                        selectedColor === color ? 'ring-2 ring-offset-2 ring-gray-500' : 'ring-1 ring-gray-200'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setSelectedColor(color)}
                      title={`Select ${color} color`}
                    >
                      {selectedColor === color && (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Symbol Selector */}
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Map Symbols</h5>
                <div className="grid grid-cols-2 gap-2">
                  {symbolOptions.map(symbol => (
                    <button
                      key={symbol.id}
                      type="button"
                      className={`w-full h-8 rounded-md flex items-center justify-center ${
                        selectedSymbol === symbol.id ? 'bg-primary-100 border-primary-500 border-2' : 'bg-white border border-gray-300'
                      }`}
                      onClick={() => setSelectedSymbol(symbol.id)}
                      title={`Add ${symbol.name}`}
                    >
                      <span className="text-sm">{symbol.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Map Actions */}
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700 mb-2">Map Actions</h5>
                <div className="flex flex-col space-y-2">
                  <button
                    type="button"
                    onClick={clearHarvestElements}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    title="Clear all elements from the current map"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    Clear Current Map
                  </button>
                  <button
                    type="button"
                    onClick={deleteHarvestMap}
                    className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    title="Delete the current map (cannot be undone)"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    Delete Map
                  </button>
                </div>
              </div>
            </div>

            {/* Help Text */}
            <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md border border-blue-100">
              <h5 className="font-medium text-blue-800 mb-1">How to Use Harvest Direction Maps</h5>
              <ul className="list-disc pl-5 space-y-1">
                <li>Use <strong>Draw Path</strong> to create directional lines showing harvest routes</li>
                <li>Add <strong>Markers</strong> to indicate important points (starting positions, obstacles, etc.)</li>
                <li>Select different <strong>Map Symbols</strong> (arrows, rocks, holes, trees, etc.) to mark specific features</li>
                <li>Click a marker after placing it to add a <strong>label</strong></li>
                <li>Use different <strong>colors</strong> to indicate different types of directions or features</li>
                <li>Create <strong>multiple maps</strong> for different stages of the harvesting process</li>
                <li>Use the <strong>Select/Edit</strong> tool to modify existing elements</li>
              </ul>
            </div>
          </div>

          {/* Harvest Direction Map */}
          <div className="relative">
            <GoogleMap
              mapContainerStyle={customMapContainerStyle}
              center={mapCenter}
              zoom={15}
              onLoad={onHarvestMapLoad}
              mapTypeId="satellite"
              options={{
                mapTypeControl: true,
                streetViewControl: false,
                fullscreenControl: true,
                mapTypeControlOptions: {
                  mapTypeIds: ["roadmap", "satellite", "hybrid", "terrain"],
                  position: window.google?.maps?.ControlPosition?.TOP_RIGHT
                }
              }}
            >
              {/* Field boundaries as reference (non-editable) */}
              {fieldPaths.map((path, index) => (
                <Polygon
                  key={`field-${index}`}
                  paths={path}
                  options={{
                    fillColor: '#22c55e',
                    fillOpacity: 0.1,
                    strokeWeight: 1,
                    strokeColor: '#22c55e',
                    clickable: false,
                    editable: false,
                    zIndex: 1,
                  }}
                />
              ))}

              {/* Drawing Manager for harvest directions */}
              <DrawingManager
                key={`drawing-manager-${selectedSymbol}-${selectedColor}`} // Force re-render when symbol or color changes
                onPolylineComplete={onPolylineComplete}
                onMarkerComplete={onMarkerComplete}
                drawingMode={drawingMode}
                options={{
                  drawingControl: false, // We're using our own UI controls
                  polylineOptions: {
                    strokeColor: selectedColor,
                    strokeWeight: 3,
                    strokeOpacity: 1,
                    clickable: true,
                    editable: true,
                    zIndex: 2,
                  },
                  markerOptions: {
                    icon: (() => {
                      const symbolObj = symbolOptions.find(s => s.id === selectedSymbol) || symbolOptions[0];
                      return {
                        path: symbolObj.path,
                        scale: symbolObj.scale || 8,
                        fillColor: selectedColor,
                        fillOpacity: symbolObj.fillOpacity !== undefined ? symbolObj.fillOpacity : 1,
                        strokeWeight: symbolObj.strokeWeight || 1,
                        strokeColor: '#ffffff',
                      };
                    })(),
                    clickable: true,
                    draggable: true,
                    zIndex: 2,
                  },
                }}
              />

              {/* Render harvest direction elements */}
              {activeHarvestMapIndex >= 0 && activeHarvestMapIndex < harvestMaps.length && 
                harvestMaps[activeHarvestMapIndex].elements.map(element => {
                  if (element.type === 'polyline') {
                    return (
                      <Polyline
                        key={element.id}
                        path={element.coordinates as PolygonCoordinates[]}
                        options={{
                          strokeColor: element.color,
                          strokeWeight: 3,
                          strokeOpacity: 1,
                          clickable: true,
                          editable: true,
                          draggable: true,
                          zIndex: 2,
                        }}
                        onMouseUp={(e) => {
                          // Update the element when the polyline is edited or modified
                          if (!e) return;

                          // Cast MapMouseEvent to Polyline by converting to unknown first
                          const polyline = e as unknown as google.maps.Polyline;
                          if (!polyline.getPath) return;

                          const path = polyline.getPath();
                          const updatedCoordinates: PolygonCoordinates[] = [];

                          for (let i = 0; i < path.getLength(); i++) {
                            const point = path.getAt(i);
                            updatedCoordinates.push({
                              lat: point.lat(),
                              lng: point.lng()
                            });
                          }

                          // Update the element in the harvest map
                          const updatedHarvestMaps = [...harvestMaps];
                          const elementIndex = updatedHarvestMaps[activeHarvestMapIndex].elements.findIndex(
                            e => e.id === element.id
                          );

                          if (elementIndex !== -1) {
                            updatedHarvestMaps[activeHarvestMapIndex].elements[elementIndex].coordinates = updatedCoordinates;

                            // Update the harvest map in the backend
                            updateHarvestDirectionMap(updatedHarvestMaps[activeHarvestMapIndex].id, {
                              elements: updatedHarvestMaps[activeHarvestMapIndex].elements
                            }).catch(error => {
                              console.error('Error updating polyline in harvest direction map:', error);
                              alert('Failed to save changes. Please try again.');
                            });

                            setHarvestMaps(updatedHarvestMaps);
                          }
                        }}
                        onDragEnd={(e) => {
                          // Update the element when the polyline is dragged
                          if (!e) return;

                          // Cast MapMouseEvent to Polyline by converting to unknown first
                          const polyline = e as unknown as google.maps.Polyline;
                          if (!polyline.getPath) return;

                          const path = polyline.getPath();
                          const updatedCoordinates: PolygonCoordinates[] = [];

                          for (let i = 0; i < path.getLength(); i++) {
                            const point = path.getAt(i);
                            updatedCoordinates.push({
                              lat: point.lat(),
                              lng: point.lng()
                            });
                          }

                          // Update the element in the harvest map
                          const updatedHarvestMaps = [...harvestMaps];
                          const elementIndex = updatedHarvestMaps[activeHarvestMapIndex].elements.findIndex(
                            e => e.id === element.id
                          );

                          if (elementIndex !== -1) {
                            updatedHarvestMaps[activeHarvestMapIndex].elements[elementIndex].coordinates = updatedCoordinates;

                            // Update the harvest map in the backend
                            updateHarvestDirectionMap(updatedHarvestMaps[activeHarvestMapIndex].id, {
                              elements: updatedHarvestMaps[activeHarvestMapIndex].elements
                            }).catch(error => {
                              console.error('Error updating polyline in harvest direction map:', error);
                              alert('Failed to save changes. Please try again.');
                            });

                            setHarvestMaps(updatedHarvestMaps);
                          }
                        }}
                      />
                    );
                  } else if (element.type === 'marker') {
                    return (
                      <Marker
                        key={element.id}
                        position={element.coordinates as PolygonCoordinates}
                        icon={(() => {
                          // If element has stored symbol properties, use those
                          if (element.symbolProperties) {
                            return {
                              path: element.symbolProperties.path,
                              scale: element.symbolProperties.scale || 8,
                              fillColor: element.color,
                              fillOpacity: element.symbolProperties.fillOpacity !== undefined ? element.symbolProperties.fillOpacity : 1,
                              strokeWeight: element.symbolProperties.strokeWeight || 1,
                              strokeColor: '#ffffff',
                            };
                          } else {
                            // Otherwise, look up the symbol type in the symbolOptions array
                            const symbolObj = element.symbolType ? 
                              (symbolOptions.find(s => s.id === element.symbolType) || symbolOptions[0]) : 
                              symbolOptions[0];

                            return {
                              path: symbolObj.path,
                              scale: symbolObj.scale || 8,
                              fillColor: element.color,
                              fillOpacity: symbolObj.fillOpacity !== undefined ? symbolObj.fillOpacity : 1,
                              strokeWeight: symbolObj.strokeWeight || 1,
                              strokeColor: '#ffffff',
                            };
                          }
                        })()}
                        clickable={true}
                        draggable={true}
                        onDragEnd={(e) => {
                          // Update the element when the marker is dragged
                          if (!e || !e.latLng) return;

                          const updatedPosition = {
                            lat: e.latLng.lat(),
                            lng: e.latLng.lng()
                          };

                          // Update the element in the harvest map
                          const updatedHarvestMaps = [...harvestMaps];
                          const elementIndex = updatedHarvestMaps[activeHarvestMapIndex].elements.findIndex(
                            el => el.id === element.id
                          );

                          if (elementIndex !== -1) {
                            updatedHarvestMaps[activeHarvestMapIndex].elements[elementIndex].coordinates = updatedPosition;

                            // Update the harvest map in the backend
                            updateHarvestDirectionMap(updatedHarvestMaps[activeHarvestMapIndex].id, {
                              elements: updatedHarvestMaps[activeHarvestMapIndex].elements
                            }).catch(error => {
                              console.error('Error updating marker position in harvest direction map:', error);
                              alert('Failed to save marker position. Please try again.');
                            });

                            setHarvestMaps(updatedHarvestMaps);
                          }
                        }}
                        onClick={() => {
                          // Open label input when marker is clicked
                          if (element.coordinates && typeof element.coordinates !== 'string') {
                            const position = element.coordinates as PolygonCoordinates;
                            setActiveLabelInput({
                              position,
                              text: element.label || ''
                            });
                          }
                        }}
                      >
                        {element.label && (
                          <InfoWindow
                            position={element.coordinates as PolygonCoordinates}
                            options={{
                              pixelOffset: new google.maps.Size(0, -30),
                            }}
                          >
                            <div className="p-1">
                              <p className="text-sm">{element.label}</p>
                            </div>
                          </InfoWindow>
                        )}
                      </Marker>
                    );
                  }
                  return null;
                })
              }

              {/* Label input for markers */}
              {activeLabelInput && (
                <InfoWindow
                  position={activeLabelInput.position}
                  onCloseClick={() => setActiveLabelInput(null)}
                >
                  <div className="p-2">
                    <input
                      type="text"
                      value={activeLabelInput.text}
                      onChange={(e) => setActiveLabelInput({
                        ...activeLabelInput,
                        text: e.target.value,
                      })}
                      placeholder="Enter label text"
                      className="w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                    />
                    <div className="flex justify-end mt-2">
                      <button
                        type="button"
                        onClick={handleLabelSubmit}
                        className="px-2 py-1 bg-primary-600 text-white rounded-md text-xs"
                      >
                        Save
                      </button>
                    </div>
                  </div>
                </InfoWindow>
              )}
            </GoogleMap>
          </div>

          <p className="mt-1 text-sm text-gray-500">
            Draw harvest directions on the map using the drawing tools. Add multiple direction maps for different steps of the harvesting process.
          </p>
        </div>
      )}

      {/* Map Dialog */}
      {mapDialogOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {mapDialogType === 'harvest' 
                ? (mapDialogMode === 'add' ? 'Add New Harvest Direction Map' : 'Edit Harvest Direction Map')
                : (mapDialogMode === 'add' ? 'Add New Field Boundary Map' : 'Edit Field Boundary Map')
              }
            </h3>

            <div className="space-y-4">
              <div>
                <label htmlFor="mapName" className="block text-sm font-medium text-gray-700 mb-1">
                  Map Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="mapName"
                  value={mapDialogData.name}
                  onChange={(e) => setMapDialogData({ ...mapDialogData, name: e.target.value })}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder={mapDialogType === 'harvest' ? "e.g., Initial Mowing Direction" : "e.g., Summer Field Layout"}
                  required
                />
              </div>

              <div>
                <label htmlFor="mapDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="mapDescription"
                  value={mapDialogData.description}
                  onChange={(e) => setMapDialogData({ ...mapDialogData, description: e.target.value })}
                  rows={3}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder={mapDialogType === 'harvest' 
                    ? "Describe the purpose of this map (e.g., Direction for initial mowing of hay)"
                    : "Describe the purpose of this map (e.g., Field boundaries for summer crop rotation)"
                  }
                />
              </div>
            </div>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setMapDialogOpen(false)}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleMapDialogSubmit}
                className="px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                {mapDialogMode === 'add' ? 'Add Map' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedMapDrawingComponent;
