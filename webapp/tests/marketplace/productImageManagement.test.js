// Product Image Management Test Script
// This script tests the product image management functionality in the marketplace

const { describe, it, expect, beforeEach, afterEach } = require('@jest/globals');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Mock axios for testing
jest.mock('axios');

describe('Product Image Management', () => {
  const baseUrl = 'http://localhost:3000/api';
  const productId = '12345-test-product-id';
  const testImagePath = path.join(__dirname, '../fixtures/test-image.jpg');
  
  let testImageId;

  beforeEach(() => {
    // Reset mocks before each test
    axios.mockReset();
  });

  afterEach(() => {
    // Clean up after tests if needed
  });

  it('should upload a product image', async () => {
    // Mock the API response
    axios.post.mockResolvedValue({
      data: {
        id: 'image-123',
        file_path: '/uploads/products/test-image.jpg',
        display_order: 0,
        is_primary: true
      }
    });

    // Create form data with image
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    // Make the request
    const response = await axios.post(
      `${baseUrl}/products/${productId}/images`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );

    // Store the image ID for later tests
    testImageId = response.data.id;

    // Assertions
    expect(axios.post).toHaveBeenCalledWith(
      `${baseUrl}/products/${productId}/images`,
      expect.any(FormData),
      expect.any(Object)
    );
    expect(response.data).toHaveProperty('id');
    expect(response.data).toHaveProperty('file_path');
    expect(response.data).toHaveProperty('display_order');
    expect(response.data).toHaveProperty('is_primary');
  });

  it('should get all images for a product', async () => {
    // Mock the API response
    axios.get.mockResolvedValue({
      data: {
        images: [
          {
            id: 'image-123',
            file_path: '/uploads/products/test-image.jpg',
            display_order: 0,
            is_primary: true
          },
          {
            id: 'image-456',
            file_path: '/uploads/products/test-image2.jpg',
            display_order: 1,
            is_primary: false
          }
        ]
      }
    });

    // Make the request
    const response = await axios.get(`${baseUrl}/products/${productId}/images`);

    // Assertions
    expect(axios.get).toHaveBeenCalledWith(`${baseUrl}/products/${productId}/images`);
    expect(response.data.images).toBeInstanceOf(Array);
    expect(response.data.images.length).toBeGreaterThan(0);
    expect(response.data.images[0]).toHaveProperty('id');
    expect(response.data.images[0]).toHaveProperty('file_path');
  });

  it('should reorder product images', async () => {
    // Mock the API response
    axios.put.mockResolvedValue({
      data: {
        images: [
          {
            id: 'image-456',
            file_path: '/uploads/products/test-image2.jpg',
            display_order: 0,
            is_primary: false
          },
          {
            id: 'image-123',
            file_path: '/uploads/products/test-image.jpg',
            display_order: 1,
            is_primary: true
          }
        ]
      }
    });

    // Make the request with new order
    const response = await axios.put(
      `${baseUrl}/products/${productId}/images/reorder`,
      {
        imageIds: ['image-456', 'image-123']
      }
    );

    // Assertions
    expect(axios.put).toHaveBeenCalledWith(
      `${baseUrl}/products/${productId}/images/reorder`,
      {
        imageIds: ['image-456', 'image-123']
      }
    );
    expect(response.data.images).toBeInstanceOf(Array);
    expect(response.data.images[0].id).toBe('image-456');
    expect(response.data.images[0].display_order).toBe(0);
    expect(response.data.images[1].id).toBe('image-123');
    expect(response.data.images[1].display_order).toBe(1);
  });

  it('should set an image as primary', async () => {
    // Mock the API response
    axios.put.mockResolvedValue({
      data: {
        id: 'image-456',
        file_path: '/uploads/products/test-image2.jpg',
        display_order: 1,
        is_primary: true
      }
    });

    // Make the request
    const response = await axios.put(
      `${baseUrl}/products/${productId}/images/image-456/primary`
    );

    // Assertions
    expect(axios.put).toHaveBeenCalledWith(
      `${baseUrl}/products/${productId}/images/image-456/primary`
    );
    expect(response.data).toHaveProperty('id', 'image-456');
    expect(response.data).toHaveProperty('is_primary', true);
  });

  it('should delete a product image', async () => {
    // Mock the API response
    axios.delete.mockResolvedValue({
      data: {
        message: 'Image deleted successfully'
      }
    });

    // Make the request
    const response = await axios.delete(
      `${baseUrl}/products/${productId}/images/image-456`
    );

    // Assertions
    expect(axios.delete).toHaveBeenCalledWith(
      `${baseUrl}/products/${productId}/images/image-456`
    );
    expect(response.data).toHaveProperty('message');
    expect(response.data.message).toContain('deleted successfully');
  });
});