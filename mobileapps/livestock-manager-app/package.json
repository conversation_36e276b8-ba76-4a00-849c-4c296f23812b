{"name": "livestock-manager-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "^53.0.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.2", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "expo-location": "~16.5.2", "expo-file-system": "~16.0.5", "expo-image-picker": "~14.7.1", "expo-camera": "~14.0.3", "expo-sqlite": "~13.2.1", "expo-updates": "~0.24.8", "expo-notifications": "~0.27.4", "expo-constants": "~15.4.5", "expo-device": "~5.9.3", "expo-network": "~5.8.0", "expo-secure-store": "~12.8.1", "expo-barcode-scanner": "~12.9.2", "expo-haptics": "~12.8.1", "react-native-maps": "1.10.0", "zustand": "^4.3.9", "axios": "^1.4.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "yup": "^1.2.0", "formik": "^2.4.3", "shared": "1.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}, "private": true}