import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Loading } from 'shared';

// Import screens (to be created)
import HomeScreen from './src/screens/HomeScreen';
import AnimalsScreen from './src/screens/AnimalsScreen';
import HealthScreen from './src/screens/HealthScreen';
import FeedingScreen from './src/screens/FeedingScreen';
import BreedingScreen from './src/screens/BreedingScreen';
import VetCareScreen from './src/screens/VetCareScreen';
import AnimalDetailScreen from './src/screens/AnimalDetailScreen';
import HealthRecordScreen from './src/screens/HealthRecordScreen';
import FeedingProgramScreen from './src/screens/FeedingProgramScreen';
import BreedingRecordScreen from './src/screens/BreedingRecordScreen';
import VetAppointmentScreen from './src/screens/VetAppointmentScreen';
import ScannerScreen from './src/screens/ScannerScreen';

// Create navigation stacks and tabs
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Main tab navigator
const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Animals') {
            iconName = focused ? 'paw' : 'paw-outline';
          } else if (route.name === 'Health') {
            iconName = focused ? 'medkit' : 'medkit-outline';
          } else if (route.name === 'Feeding') {
            iconName = focused ? 'restaurant' : 'restaurant-outline';
          } else if (route.name === 'Breeding') {
            iconName = focused ? 'heart' : 'heart-outline';
          } else if (route.name === 'VetCare') {
            iconName = focused ? 'medical' : 'medical-outline';
          }

          // You can return any component here
          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#007AFF',
        tabBarInactiveTintColor: 'gray',
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Animals" component={AnimalsScreen} />
      <Tab.Screen name="Health" component={HealthScreen} />
      <Tab.Screen name="Feeding" component={FeedingScreen} />
      <Tab.Screen name="Breeding" component={BreedingScreen} />
      <Tab.Screen name="VetCare" component={VetCareScreen} />
    </Tab.Navigator>
  );
};

// Main app component
export default function App() {
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading app resources
  useEffect(() => {
    const loadResources = async () => {
      try {
        // Load any resources, check authentication, etc.
        // For now, just simulate a delay
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error('Error loading resources:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadResources();
  }, []);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="Main" component={TabNavigator} />
          <Stack.Screen name="AnimalDetail" component={AnimalDetailScreen} />
          <Stack.Screen name="HealthRecord" component={HealthRecordScreen} />
          <Stack.Screen name="FeedingProgram" component={FeedingProgramScreen} />
          <Stack.Screen name="BreedingRecord" component={BreedingRecordScreen} />
          <Stack.Screen name="VetAppointment" component={VetAppointmentScreen} />
          <Stack.Screen name="Scanner" component={ScannerScreen} />
        </Stack.Navigator>
      </NavigationContainer>
      <StatusBar style="auto" />
    </SafeAreaProvider>
  );
}