import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Image, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

const AnimalDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { animalId, isNew } = route.params || {};
  const [isEditing, setIsEditing] = useState(isNew || false);
  const [activeTab, setActiveTab] = useState('Overview');
  
  // Placeholder animal data - would come from API/state in real app
  const [animal, setAnimal] = useState({
    id: animalId || 'new',
    name: '',
    type: '',
    breed: '',
    gender: '',
    birthDate: '',
    weight: '',
    status: 'Active',
    notes: '',
    image: null,
    tags: [],
    parentMale: '',
    parentFemale: '',
    purchaseDate: '',
    purchasePrice: '',
    location: 'Main Barn',
  });

  // Placeholder data for related records
  const healthRecords = [
    { 
      id: '1', 
      type: 'Vaccination', 
      date: '2023-06-24', 
      description: 'Annual vaccination against common diseases',
      performedBy: 'Dr. Smith',
      status: 'Completed'
    },
    { 
      id: '2', 
      type: 'Treatment', 
      date: '2023-05-15', 
      description: 'Treatment for minor injury',
      performedBy: 'Dr. Johnson',
      status: 'Completed'
    },
  ];

  const feedingPrograms = [
    { 
      id: '1', 
      feedType: 'Hay', 
      schedule: 'Daily', 
      amount: '20 kg',
      startDate: '2023-06-01',
      endDate: '2023-12-31',
      notes: 'High-quality alfalfa hay',
      status: 'Active'
    },
    { 
      id: '2', 
      feedType: 'Grain Mix', 
      schedule: 'Twice Daily', 
      amount: '5 kg',
      startDate: '2023-06-01',
      endDate: '2023-12-31',
      notes: 'Energy supplement',
      status: 'Active'
    },
  ];

  const breedingRecords = [
    { 
      id: '1', 
      type: 'Natural', 
      date: '2023-06-15', 
      partner: 'Bull #9012',
      expectedDueDate: '2023-03-25',
      notes: 'Observed breeding activity',
      status: 'Confirmed Pregnant'
    },
    { 
      id: '2', 
      type: 'Artificial Insemination', 
      date: '2022-05-10', 
      partner: 'AI Service',
      expectedDueDate: '2022-02-17',
      notes: 'Used premium semen from top genetics',
      status: 'Resulted in Birth'
    },
  ];

  const vetAppointments = [
    { 
      id: '1', 
      type: 'Vaccination', 
      date: '2023-07-15', 
      time: '09:00 AM',
      veterinarian: 'Dr. Smith',
      location: 'On-site',
      notes: 'Annual vaccination against common diseases',
      status: 'Scheduled'
    },
    { 
      id: '2', 
      type: 'Health Check', 
      date: '2023-06-20', 
      time: '11:00 AM',
      veterinarian: 'Dr. Smith',
      location: 'On-site',
      notes: 'Routine health examination',
      status: 'Completed'
    },
  ];

  // Load animal data when not creating a new animal
  useEffect(() => {
    if (!isNew && animalId) {
      // In a real app, this would fetch data from an API
      // For now, we'll use placeholder data
      const animalData = {
        id: animalId,
        name: 'Cow #1234',
        type: 'Cattle',
        breed: 'Holstein',
        gender: 'Female',
        birthDate: '2020-04-15',
        weight: '650 kg',
        status: 'Active',
        notes: 'Excellent milk producer. Calm temperament.',
        image: null, // Would be a URI in a real app
        tags: ['A123', 'RFID-456789'],
        parentMale: 'Bull #5678',
        parentFemale: 'Cow #9012',
        purchaseDate: '2020-06-01',
        purchasePrice: '$2,500',
        location: 'Main Barn',
      };
      
      setAnimal(animalData);
    }
  }, [isNew, animalId]);

  // Handle saving animal data
  const handleSave = () => {
    // Validate required fields
    if (!animal.name || !animal.type) {
      Alert.alert('Missing Information', 'Please provide at least a name and type for the animal.');
      return;
    }

    // In a real app, this would save to an API
    console.log('Saving animal:', animal);
    
    // Exit edit mode
    setIsEditing(false);
    
    // If this was a new animal, navigate back to the animals list
    if (isNew) {
      navigation.goBack();
    }
  };

  // Handle canceling edit
  const handleCancel = () => {
    if (isNew) {
      // If creating a new animal, go back to previous screen
      navigation.goBack();
    } else {
      // If editing existing animal, just exit edit mode
      setIsEditing(false);
    }
  };

  // Handle field changes
  const handleChange = (field, value) => {
    setAnimal(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Render edit form
  const renderEditForm = () => {
    return (
      <View style={styles.formContainer}>
        <View style={styles.formGroup}>
          <Text style={styles.label}>Name</Text>
          <TextInput
            style={styles.input}
            value={animal.name}
            onChangeText={(value) => handleChange('name', value)}
            placeholder="Enter animal name or ID"
          />
        </View>
        
        <View style={styles.formRow}>
          <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
            <Text style={styles.label}>Type</Text>
            <TextInput
              style={styles.input}
              value={animal.type}
              onChangeText={(value) => handleChange('type', value)}
              placeholder="Cattle, Sheep, etc."
            />
          </View>
          
          <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
            <Text style={styles.label}>Breed</Text>
            <TextInput
              style={styles.input}
              value={animal.breed}
              onChangeText={(value) => handleChange('breed', value)}
              placeholder="Breed"
            />
          </View>
        </View>
        
        <View style={styles.formRow}>
          <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
            <Text style={styles.label}>Gender</Text>
            <TextInput
              style={styles.input}
              value={animal.gender}
              onChangeText={(value) => handleChange('gender', value)}
              placeholder="Male/Female"
            />
          </View>
          
          <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
            <Text style={styles.label}>Birth Date</Text>
            <TextInput
              style={styles.input}
              value={animal.birthDate}
              onChangeText={(value) => handleChange('birthDate', value)}
              placeholder="YYYY-MM-DD"
            />
          </View>
        </View>
        
        <View style={styles.formRow}>
          <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
            <Text style={styles.label}>Weight</Text>
            <TextInput
              style={styles.input}
              value={animal.weight}
              onChangeText={(value) => handleChange('weight', value)}
              placeholder="Weight"
            />
          </View>
          
          <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
            <Text style={styles.label}>Status</Text>
            <TextInput
              style={styles.input}
              value={animal.status}
              onChangeText={(value) => handleChange('status', value)}
              placeholder="Active, Sold, etc."
            />
          </View>
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Location</Text>
          <TextInput
            style={styles.input}
            value={animal.location}
            onChangeText={(value) => handleChange('location', value)}
            placeholder="Barn, Pasture, etc."
          />
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Tags (comma separated)</Text>
          <TextInput
            style={styles.input}
            value={animal.tags.join(', ')}
            onChangeText={(value) => handleChange('tags', value.split(', '))}
            placeholder="Tag IDs"
          />
        </View>
        
        <View style={styles.formRow}>
          <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
            <Text style={styles.label}>Sire (Father)</Text>
            <TextInput
              style={styles.input}
              value={animal.parentMale}
              onChangeText={(value) => handleChange('parentMale', value)}
              placeholder="Sire ID"
            />
          </View>
          
          <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
            <Text style={styles.label}>Dam (Mother)</Text>
            <TextInput
              style={styles.input}
              value={animal.parentFemale}
              onChangeText={(value) => handleChange('parentFemale', value)}
              placeholder="Dam ID"
            />
          </View>
        </View>
        
        <View style={styles.formRow}>
          <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
            <Text style={styles.label}>Purchase Date</Text>
            <TextInput
              style={styles.input}
              value={animal.purchaseDate}
              onChangeText={(value) => handleChange('purchaseDate', value)}
              placeholder="YYYY-MM-DD"
            />
          </View>
          
          <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
            <Text style={styles.label}>Purchase Price</Text>
            <TextInput
              style={styles.input}
              value={animal.purchasePrice}
              onChangeText={(value) => handleChange('purchasePrice', value)}
              placeholder="Price"
            />
          </View>
        </View>
        
        <View style={styles.formGroup}>
          <Text style={styles.label}>Notes</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            value={animal.notes}
            onChangeText={(value) => handleChange('notes', value)}
            placeholder="Additional notes about this animal"
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
        
        <View style={styles.buttonRow}>
          <TouchableOpacity 
            style={[styles.button, styles.cancelButton]} 
            onPress={handleCancel}
          >
            <Text style={styles.buttonText}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, styles.saveButton]} 
            onPress={handleSave}
          >
            <Text style={[styles.buttonText, styles.saveButtonText]}>Save</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Render overview tab
  const renderOverviewTab = () => {
    return (
      <View style={styles.tabContent}>
        <View style={styles.infoSection}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Type:</Text>
            <Text style={styles.infoValue}>{animal.type}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Breed:</Text>
            <Text style={styles.infoValue}>{animal.breed}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Gender:</Text>
            <Text style={styles.infoValue}>{animal.gender}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Birth Date:</Text>
            <Text style={styles.infoValue}>{animal.birthDate}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Weight:</Text>
            <Text style={styles.infoValue}>{animal.weight}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Status:</Text>
            <Text style={styles.infoValue}>{animal.status}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Location:</Text>
            <Text style={styles.infoValue}>{animal.location}</Text>
          </View>
        </View>
        
        <View style={styles.sectionDivider} />
        
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Identification</Text>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Tags:</Text>
            <View style={styles.tagContainer}>
              {animal.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>
        
        <View style={styles.sectionDivider} />
        
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Lineage</Text>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Sire:</Text>
            <Text style={styles.infoValue}>{animal.parentMale || 'Unknown'}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Dam:</Text>
            <Text style={styles.infoValue}>{animal.parentFemale || 'Unknown'}</Text>
          </View>
        </View>
        
        <View style={styles.sectionDivider} />
        
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Purchase Information</Text>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Date:</Text>
            <Text style={styles.infoValue}>{animal.purchaseDate || 'N/A'}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Price:</Text>
            <Text style={styles.infoValue}>{animal.purchasePrice || 'N/A'}</Text>
          </View>
        </View>
        
        <View style={styles.sectionDivider} />
        
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Notes</Text>
          <Text style={styles.notesText}>{animal.notes || 'No notes available.'}</Text>
        </View>
      </View>
    );
  };

  // Render health tab
  const renderHealthTab = () => {
    return (
      <View style={styles.tabContent}>
        <View style={styles.tabHeader}>
          <Text style={styles.tabTitle}>Health Records</Text>
          <TouchableOpacity 
            style={styles.addRecordButton}
            onPress={() => navigation.navigate('HealthRecord', { animalId: animal.id, isNew: true })}
          >
            <Ionicons name="add" size={20} color="#fff" />
            <Text style={styles.addRecordButtonText}>Add Record</Text>
          </TouchableOpacity>
        </View>
        
        {healthRecords.length > 0 ? (
          healthRecords.map(record => (
            <TouchableOpacity 
              key={record.id}
              style={styles.recordCard}
              onPress={() => navigation.navigate('HealthRecord', { recordId: record.id })}
            >
              <View style={styles.recordHeader}>
                <Ionicons 
                  name={record.type === 'Vaccination' ? 'fitness' : 'medkit'} 
                  size={20} 
                  color={record.type === 'Vaccination' ? '#FF6B6B' : '#4ECDC4'} 
                />
                <Text style={styles.recordType}>{record.type}</Text>
                <Text style={styles.recordDate}>{record.date}</Text>
              </View>
              <Text style={styles.recordDescription}>{record.description}</Text>
              <Text style={styles.recordPerformedBy}>By: {record.performedBy}</Text>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyRecords}>
            <Text style={styles.emptyRecordsText}>No health records available.</Text>
          </View>
        )}
      </View>
    );
  };

  // Render feeding tab
  const renderFeedingTab = () => {
    return (
      <View style={styles.tabContent}>
        <View style={styles.tabHeader}>
          <Text style={styles.tabTitle}>Feeding Programs</Text>
          <TouchableOpacity 
            style={styles.addRecordButton}
            onPress={() => navigation.navigate('FeedingProgram', { animalId: animal.id, isNew: true })}
          >
            <Ionicons name="add" size={20} color="#fff" />
            <Text style={styles.addRecordButtonText}>Add Program</Text>
          </TouchableOpacity>
        </View>
        
        {feedingPrograms.length > 0 ? (
          feedingPrograms.map(program => (
            <TouchableOpacity 
              key={program.id}
              style={styles.recordCard}
              onPress={() => navigation.navigate('FeedingProgram', { programId: program.id })}
            >
              <View style={styles.recordHeader}>
                <Ionicons name="restaurant" size={20} color="#4ECDC4" />
                <Text style={styles.recordType}>{program.feedType}</Text>
                <View style={styles.statusBadge}>
                  <Text style={styles.statusText}>{program.status}</Text>
                </View>
              </View>
              <View style={styles.recordDetails}>
                <Text style={styles.recordSchedule}>{program.schedule} - {program.amount}</Text>
                <Text style={styles.recordDateRange}>{program.startDate} to {program.endDate}</Text>
              </View>
              {program.notes && <Text style={styles.recordNotes}>{program.notes}</Text>}
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyRecords}>
            <Text style={styles.emptyRecordsText}>No feeding programs available.</Text>
          </View>
        )}
      </View>
    );
  };

  // Render breeding tab
  const renderBreedingTab = () => {
    return (
      <View style={styles.tabContent}>
        <View style={styles.tabHeader}>
          <Text style={styles.tabTitle}>Breeding Records</Text>
          <TouchableOpacity 
            style={styles.addRecordButton}
            onPress={() => navigation.navigate('BreedingRecord', { animalId: animal.id, isNew: true })}
          >
            <Ionicons name="add" size={20} color="#fff" />
            <Text style={styles.addRecordButtonText}>Add Record</Text>
          </TouchableOpacity>
        </View>
        
        {breedingRecords.length > 0 ? (
          breedingRecords.map(record => (
            <TouchableOpacity 
              key={record.id}
              style={styles.recordCard}
              onPress={() => navigation.navigate('BreedingRecord', { recordId: record.id })}
            >
              <View style={styles.recordHeader}>
                <Ionicons 
                  name={record.type === 'Natural' ? 'male-female' : 'flask'} 
                  size={20} 
                  color={record.type === 'Natural' ? '#6B5B95' : '#FF6B6B'} 
                />
                <Text style={styles.recordType}>{record.type}</Text>
                <Text style={styles.recordDate}>{record.date}</Text>
              </View>
              <View style={styles.recordDetails}>
                <Text style={styles.recordPartner}>Partner: {record.partner}</Text>
                {record.expectedDueDate && (
                  <Text style={styles.recordDueDate}>Due: {record.expectedDueDate}</Text>
                )}
              </View>
              <View style={styles.recordStatusRow}>
                <Text style={styles.recordNotes}>{record.notes}</Text>
                <View style={[styles.statusBadge, { backgroundColor: record.status.includes('Pregnant') ? '#4ECDC4' : '#FFD166' }]}>
                  <Text style={styles.statusText}>{record.status}</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyRecords}>
            <Text style={styles.emptyRecordsText}>No breeding records available.</Text>
          </View>
        )}
      </View>
    );
  };

  // Render vet care tab
  const renderVetCareTab = () => {
    return (
      <View style={styles.tabContent}>
        <View style={styles.tabHeader}>
          <Text style={styles.tabTitle}>Veterinary Appointments</Text>
          <TouchableOpacity 
            style={styles.addRecordButton}
            onPress={() => navigation.navigate('VetAppointment', { animalId: animal.id, isNew: true })}
          >
            <Ionicons name="add" size={20} color="#fff" />
            <Text style={styles.addRecordButtonText}>Add Appointment</Text>
          </TouchableOpacity>
        </View>
        
        {vetAppointments.length > 0 ? (
          vetAppointments.map(appointment => (
            <TouchableOpacity 
              key={appointment.id}
              style={styles.recordCard}
              onPress={() => navigation.navigate('VetAppointment', { appointmentId: appointment.id })}
            >
              <View style={styles.recordHeader}>
                <Ionicons 
                  name={appointment.type === 'Vaccination' ? 'fitness' : 'medkit'} 
                  size={20} 
                  color={appointment.type === 'Vaccination' ? '#FF6B6B' : '#4ECDC4'} 
                />
                <Text style={styles.recordType}>{appointment.type}</Text>
                <View style={[styles.statusBadge, { backgroundColor: appointment.status === 'Scheduled' ? '#FFD166' : '#4ECDC4' }]}>
                  <Text style={styles.statusText}>{appointment.status}</Text>
                </View>
              </View>
              <View style={styles.recordDetails}>
                <Text style={styles.recordDateTime}>{appointment.date} at {appointment.time}</Text>
                <Text style={styles.recordVet}>Vet: {appointment.veterinarian}</Text>
                <Text style={styles.recordLocation}>Location: {appointment.location}</Text>
              </View>
              {appointment.notes && <Text style={styles.recordNotes}>{appointment.notes}</Text>}
            </TouchableOpacity>
          ))
        ) : (
          <View style={styles.emptyRecords}>
            <Text style={styles.emptyRecordsText}>No veterinary appointments available.</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.title}>{isNew ? 'New Animal' : animal.name}</Text>
        {!isNew && !isEditing && (
          <TouchableOpacity onPress={() => setIsEditing(true)} style={styles.editButton}>
            <Ionicons name="create-outline" size={24} color="#007AFF" />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content}>
        {isEditing ? (
          renderEditForm()
        ) : (
          <>
            <View style={styles.animalHeader}>
              <View style={styles.animalImage}>
                {animal.image ? (
                  <Image source={{ uri: animal.image }} style={styles.image} />
                ) : (
                  <View style={styles.imagePlaceholder}>
                    <Ionicons name={animal.type === 'Cattle' ? 'paw' : 'leaf'} size={48} color="#ccc" />
                  </View>
                )}
              </View>
              <View style={styles.animalInfo}>
                <Text style={styles.animalName}>{animal.name}</Text>
                <Text style={styles.animalType}>{animal.type} - {animal.breed}</Text>
                <View style={styles.statusContainer}>
                  <View style={styles.statusDot} />
                  <Text style={styles.statusLabel}>{animal.status}</Text>
                </View>
              </View>
            </View>

            <View style={styles.tabBar}>
              <TouchableOpacity 
                style={[styles.tabButton, activeTab === 'Overview' && styles.activeTabButton]} 
                onPress={() => setActiveTab('Overview')}
              >
                <Text style={[styles.tabButtonText, activeTab === 'Overview' && styles.activeTabButtonText]}>Overview</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.tabButton, activeTab === 'Health' && styles.activeTabButton]} 
                onPress={() => setActiveTab('Health')}
              >
                <Text style={[styles.tabButtonText, activeTab === 'Health' && styles.activeTabButtonText]}>Health</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.tabButton, activeTab === 'Feeding' && styles.activeTabButton]} 
                onPress={() => setActiveTab('Feeding')}
              >
                <Text style={[styles.tabButtonText, activeTab === 'Feeding' && styles.activeTabButtonText]}>Feeding</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.tabButton, activeTab === 'Breeding' && styles.activeTabButton]} 
                onPress={() => setActiveTab('Breeding')}
              >
                <Text style={[styles.tabButtonText, activeTab === 'Breeding' && styles.activeTabButtonText]}>Breeding</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.tabButton, activeTab === 'VetCare' && styles.activeTabButton]} 
                onPress={() => setActiveTab('VetCare')}
              >
                <Text style={[styles.tabButtonText, activeTab === 'VetCare' && styles.activeTabButtonText]}>Vet Care</Text>
              </TouchableOpacity>
            </View>

            {activeTab === 'Overview' && renderOverviewTab()}
            {activeTab === 'Health' && renderHealthTab()}
            {activeTab === 'Feeding' && renderFeedingTab()}
            {activeTab === 'Breeding' && renderBreedingTab()}
            {activeTab === 'VetCare' && renderVetCareTab()}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  editButton: {
    padding: 4,
  },
  content: {
    flex: 1,
  },
  animalHeader: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  animalImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    overflow: 'hidden',
    marginRight: 16,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 40,
  },
  animalInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  animalName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  animalType: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4ECDC4',
    marginRight: 6,
  },
  statusLabel: {
    fontSize: 14,
    color: '#4ECDC4',
    fontWeight: '500',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  activeTabButtonText: {
    color: '#007AFF',
  },
  tabContent: {
    padding: 16,
  },
  infoSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    width: 100,
    fontSize: 14,
    color: '#666',
  },
  infoValue: {
    flex: 1,
    fontSize: 14,
    color: '#333',
  },
  sectionDivider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 16,
  },
  tagContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#e0e0e0',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 12,
    color: '#333',
  },
  notesText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  formContainer: {
    padding: 16,
    backgroundColor: '#fff',
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    color: '#333',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 4,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f5f5f5',
    marginRight: 8,
  },
  saveButton: {
    backgroundColor: '#007AFF',
    marginLeft: 8,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
  },
  saveButtonText: {
    color: '#fff',
  },
  tabHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  tabTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  addRecordButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  addRecordButtonText: {
    color: '#fff',
    fontWeight: '500',
    marginLeft: 4,
  },
  recordCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  recordHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  recordType: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  recordDate: {
    fontSize: 14,
    color: '#666',
  },
  recordDescription: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  recordPerformedBy: {
    fontSize: 12,
    color: '#666',
  },
  recordDetails: {
    marginBottom: 8,
  },
  recordSchedule: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  recordDateRange: {
    fontSize: 12,
    color: '#666',
  },
  recordNotes: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    flex: 1,
  },
  recordPartner: {
    fontSize: 14,
    color: '#333',
  },
  recordDueDate: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  recordStatusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  recordDateTime: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  recordVet: {
    fontSize: 14,
    color: '#333',
  },
  recordLocation: {
    fontSize: 14,
    color: '#333',
  },
  statusBadge: {
    backgroundColor: '#4ECDC4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  emptyRecords: {
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
  },
  emptyRecordsText: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
  },
});

export default AnimalDetailScreen;