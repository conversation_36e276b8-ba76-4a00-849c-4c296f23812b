import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert, Text as RNText, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Camera } from 'expo-camera';
import { BarCodeScanner } from 'expo-barcode-scanner';
import * as Haptics from 'expo-haptics';
import { Text, Button } from 'shared';

/**
 * ScannerScreen component
 * Allows scanning of barcodes, QR codes, and RFID/NFC tags for animal identification
 */
const ScannerScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { scanMode = 'animal' } = route.params || {};
  
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [scanning, setScanning] = useState(true);
  const [flashMode, setFlashMode] = useState(Camera.Constants.FlashMode.off);
  const [scanResult, setScanResult] = useState(null);
  const [processingResult, setProcessingResult] = useState(false);
  
  const cameraRef = useRef(null);

  // Request camera permission
  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
      
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'We need camera permission to scan barcodes and QR codes.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      }
    })();
  }, [navigation]);

  // Handle barcode scan
  const handleBarCodeScanned = async ({ type, data }) => {
    if (scanned || !scanning) return;
    
    setScanned(true);
    setScanning(false);
    
    // Provide haptic feedback
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    
    // Process the scanned data
    try {
      setProcessingResult(true);
      
      // In a real app, this would be an API call to fetch data based on the scanned code
      // For now, use mock data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      let result;
      
      if (scanMode === 'animal') {
        result = {
          type: 'animal',
          id: data,
          name: 'Bessie',
          species: 'Cattle',
          breed: 'Holstein',
          gender: 'Female',
          birthDate: '2020-05-15',
          status: 'Active',
        };
      } else if (scanMode === 'medication') {
        result = {
          type: 'medication',
          id: data,
          name: 'Penicillin',
          category: 'Antibiotic',
          dosage: '10ml',
          expiryDate: '2024-12-31',
          batchNumber: 'B12345',
        };
      } else if (scanMode === 'equipment') {
        result = {
          type: 'equipment',
          id: data,
          name: 'Automatic Feeder',
          model: 'AF-2000',
          serialNumber: 'SN123456',
          purchaseDate: '2022-03-10',
          lastMaintenance: '2023-06-15',
        };
      } else {
        result = {
          type: 'unknown',
          id: data,
          rawData: data,
          format: type,
        };
      }
      
      setScanResult(result);
    } catch (error) {
      console.error('Error processing scan result:', error);
      Alert.alert('Error', 'Failed to process scan result');
      setScanResult({
        type: 'error',
        message: 'Failed to process scan result',
        rawData: data,
        format: type,
      });
    } finally {
      setProcessingResult(false);
    }
  };

  // Reset scanner
  const resetScanner = () => {
    setScanned(false);
    setScanning(true);
    setScanResult(null);
  };

  // Toggle flash
  const toggleFlash = () => {
    setFlashMode(
      flashMode === Camera.Constants.FlashMode.off
        ? Camera.Constants.FlashMode.torch
        : Camera.Constants.FlashMode.off
    );
  };

  // Navigate to appropriate screen based on scan result
  const navigateToResult = () => {
    if (!scanResult) return;
    
    if (scanResult.type === 'animal') {
      navigation.navigate('AnimalDetail', { animalId: scanResult.id });
    } else if (scanResult.type === 'medication') {
      // In a real app, navigate to medication detail screen
      Alert.alert('Medication Scanned', `Medication: ${scanResult.name}`);
      navigation.goBack();
    } else if (scanResult.type === 'equipment') {
      // In a real app, navigate to equipment detail screen
      Alert.alert('Equipment Scanned', `Equipment: ${scanResult.name}`);
      navigation.goBack();
    } else {
      Alert.alert('Unknown Code', `Scanned data: ${scanResult.rawData}`);
      navigation.goBack();
    }
  };

  // Render loading state while requesting permissions
  if (hasPermission === null) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <RNText style={styles.loadingText}>Requesting camera permission...</RNText>
        </View>
      </SafeAreaView>
    );
  }

  // Render permission denied state
  if (hasPermission === false) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.permissionContainer}>
          <Ionicons name="camera-off" size={64} color="#FF3B30" />
          <RNText style={styles.permissionText}>Camera permission is required to use the scanner</RNText>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            style={styles.permissionButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#FFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {scanMode === 'animal' ? 'Scan Animal Tag' : 
           scanMode === 'medication' ? 'Scan Medication' : 
           scanMode === 'equipment' ? 'Scan Equipment' : 'Scanner'}
        </Text>
        <TouchableOpacity onPress={toggleFlash} style={styles.flashButton}>
          <Ionicons 
            name={flashMode === Camera.Constants.FlashMode.torch ? "flash" : "flash-off"} 
            size={24} 
            color="#FFF" 
          />
        </TouchableOpacity>
      </View>
      
      {!scanned ? (
        <View style={styles.cameraContainer}>
          <Camera
            ref={cameraRef}
            style={styles.camera}
            type={Camera.Constants.Type.back}
            flashMode={flashMode}
            onBarCodeScanned={handleBarCodeScanned}
            barCodeScannerSettings={{
              barCodeTypes: [
                BarCodeScanner.Constants.BarCodeType.qr,
                BarCodeScanner.Constants.BarCodeType.code128,
                BarCodeScanner.Constants.BarCodeType.code39,
                BarCodeScanner.Constants.BarCodeType.ean13,
                BarCodeScanner.Constants.BarCodeType.ean8,
              ],
            }}
          >
            <View style={styles.scanOverlay}>
              <View style={styles.scanFrame}>
                <View style={[styles.scanCorner, styles.scanCornerTopLeft]} />
                <View style={[styles.scanCorner, styles.scanCornerTopRight]} />
                <View style={[styles.scanCorner, styles.scanCornerBottomLeft]} />
                <View style={[styles.scanCorner, styles.scanCornerBottomRight]} />
              </View>
              <RNText style={styles.scanInstructions}>
                Position the {scanMode === 'animal' ? 'animal tag' : 
                             scanMode === 'medication' ? 'medication barcode' : 
                             scanMode === 'equipment' ? 'equipment tag' : 'barcode'} 
                within the frame
              </RNText>
            </View>
          </Camera>
        </View>
      ) : (
        <View style={styles.resultContainer}>
          {processingResult ? (
            <View style={styles.processingContainer}>
              <ActivityIndicator size="large" color="#007AFF" />
              <RNText style={styles.processingText}>Processing scan result...</RNText>
            </View>
          ) : (
            <>
              <View style={styles.resultHeader}>
                <Ionicons 
                  name={
                    scanResult?.type === 'animal' ? 'paw' : 
                    scanResult?.type === 'medication' ? 'medkit' : 
                    scanResult?.type === 'equipment' ? 'construct' : 
                    scanResult?.type === 'error' ? 'alert-circle' : 'barcode'
                  } 
                  size={48} 
                  color="#007AFF" 
                />
                <RNText style={styles.resultTitle}>
                  {scanResult?.type === 'animal' ? 'Animal Identified' : 
                   scanResult?.type === 'medication' ? 'Medication Identified' : 
                   scanResult?.type === 'equipment' ? 'Equipment Identified' : 
                   scanResult?.type === 'error' ? 'Error' : 'Code Scanned'}
                </RNText>
              </View>
              
              <View style={styles.resultDetails}>
                {scanResult?.type === 'animal' && (
                  <>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>ID:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.id}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Name:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.name}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Species:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.species}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Breed:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.breed}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Gender:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.gender}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Birth Date:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.birthDate}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Status:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.status}</RNText>
                    </View>
                  </>
                )}
                
                {scanResult?.type === 'medication' && (
                  <>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>ID:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.id}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Name:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.name}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Category:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.category}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Dosage:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.dosage}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Expiry Date:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.expiryDate}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Batch Number:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.batchNumber}</RNText>
                    </View>
                  </>
                )}
                
                {scanResult?.type === 'equipment' && (
                  <>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>ID:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.id}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Name:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.name}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Model:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.model}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Serial Number:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.serialNumber}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Purchase Date:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.purchaseDate}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Last Maintenance:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.lastMaintenance}</RNText>
                    </View>
                  </>
                )}
                
                {(scanResult?.type === 'unknown' || scanResult?.type === 'error') && (
                  <>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Raw Data:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.rawData}</RNText>
                    </View>
                    <View style={styles.resultRow}>
                      <RNText style={styles.resultLabel}>Format:</RNText>
                      <RNText style={styles.resultValue}>{scanResult.format}</RNText>
                    </View>
                    {scanResult?.type === 'error' && (
                      <View style={styles.resultRow}>
                        <RNText style={styles.resultLabel}>Error:</RNText>
                        <RNText style={styles.resultValue}>{scanResult.message}</RNText>
                      </View>
                    )}
                  </>
                )}
              </View>
            </>
          )}
        </View>
      )}
      
      <View style={styles.footer}>
        {scanned ? (
          <View style={styles.buttonContainer}>
            <Button
              title="Scan Again"
              onPress={resetScanner}
              type="outline"
              style={styles.footerButton}
            />
            <Button
              title={scanResult?.type === 'animal' ? 'View Animal Details' : 'Continue'}
              onPress={navigateToResult}
              style={styles.footerButton}
              disabled={processingResult}
            />
          </View>
        ) : (
          <RNText style={styles.footerText}>
            {scanMode === 'animal' ? 'Scanning for animal tags...' : 
             scanMode === 'medication' ? 'Scanning for medication barcodes...' : 
             scanMode === 'equipment' ? 'Scanning for equipment tags...' : 'Scanning...'}
          </RNText>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFF',
  },
  flashButton: {
    padding: 4,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  scanOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    backgroundColor: 'transparent',
    position: 'relative',
  },
  scanCorner: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderColor: '#FFF',
  },
  scanCornerTopLeft: {
    top: -2,
    left: -2,
    borderTopWidth: 4,
    borderLeftWidth: 4,
  },
  scanCornerTopRight: {
    top: -2,
    right: -2,
    borderTopWidth: 4,
    borderRightWidth: 4,
  },
  scanCornerBottomLeft: {
    bottom: -2,
    left: -2,
    borderBottomWidth: 4,
    borderLeftWidth: 4,
  },
  scanCornerBottomRight: {
    bottom: -2,
    right: -2,
    borderBottomWidth: 4,
    borderRightWidth: 4,
  },
  scanInstructions: {
    color: '#FFF',
    fontSize: 16,
    marginTop: 24,
    textAlign: 'center',
    paddingHorizontal: 32,
  },
  resultContainer: {
    flex: 1,
    backgroundColor: '#FFF',
    padding: 16,
  },
  processingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  resultHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  resultTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 12,
  },
  resultDetails: {
    flex: 1,
  },
  resultRow: {
    flexDirection: 'row',
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  resultLabel: {
    fontSize: 16,
    fontWeight: '600',
    width: 120,
  },
  resultValue: {
    fontSize: 16,
    flex: 1,
  },
  footer: {
    padding: 16,
    backgroundColor: scanned => scanned ? '#FFF' : '#000',
  },
  footerText: {
    color: '#FFF',
    fontSize: 16,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  footerButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF',
    padding: 24,
  },
  permissionText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  permissionButton: {
    width: 200,
  },
});

export default ScannerScreen;