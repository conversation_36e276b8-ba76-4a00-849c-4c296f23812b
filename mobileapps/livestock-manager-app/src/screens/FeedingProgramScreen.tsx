import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';

const FeedingProgramScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { programId, isNew } = route.params || {};

  // State for form fields
  const [animalName, setAnimalName] = useState('');
  const [feedType, setFeedType] = useState('');
  const [schedule, setSchedule] = useState('');
  const [amount, setAmount] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [notes, setNotes] = useState('');
  const [status, setStatus] = useState('Active');
  const [isActive, setIsActive] = useState(true);
  const [isEditing, setIsEditing] = useState(isNew);

  // Placeholder data - would come from API/state in real app
  const feedingPrograms = [
    { 
      id: '1', 
      animalId: '1', 
      animalName: 'Cow #1234', 
      feedType: 'Hay', 
      schedule: 'Daily', 
      amount: '20 kg',
      startDate: '2023-06-01',
      endDate: '2023-12-31',
      notes: 'High-quality alfalfa hay',
      status: 'Active'
    },
    { 
      id: '2', 
      animalId: '2', 
      animalName: 'Cow #5678', 
      feedType: 'Grain Mix', 
      schedule: 'Twice Daily', 
      amount: '5 kg',
      startDate: '2023-06-01',
      endDate: '2023-12-31',
      notes: 'Pregnancy support mix',
      status: 'Active'
    },
    // More programs...
  ];

  // Feed type options
  const feedTypeOptions = ['Hay', 'Grain', 'Pasture', 'Silage', 'Mineral', 'Supplement', 'Mixed'];

  // Schedule options
  const scheduleOptions = ['Daily', 'Twice Daily', 'Weekly', 'Free Choice', 'Continuous'];

  // Load program data if editing an existing program
  useEffect(() => {
    if (programId && !isNew) {
      // In a real app, this would fetch from API
      const program = feedingPrograms.find(p => p.id === programId);
      if (program) {
        setAnimalName(program.animalName || '');
        setFeedType(program.feedType || '');
        setSchedule(program.schedule || '');
        setAmount(program.amount || '');
        setStartDate(program.startDate || '');
        setEndDate(program.endDate || '');
        setNotes(program.notes || '');
        setStatus(program.status || 'Active');
        setIsActive(program.status === 'Active');
      }
    }
  }, [programId, isNew]);

  // Handle save
  const handleSave = () => {
    // Validate form
    if (!animalName || !feedType || !schedule || !amount || !startDate) {
      Alert.alert('Missing Information', 'Please fill in all required fields.');
      return;
    }

    // In a real app, this would save to API
    Alert.alert(
      'Success',
      isNew ? 'Feeding program created successfully!' : 'Feeding program updated successfully!',
      [{ text: 'OK', onPress: () => navigation.goBack() }]
    );
  };

  // Handle delete
  const handleDelete = () => {
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this feeding program?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            // In a real app, this would delete from API
            Alert.alert('Success', 'Feeding program deleted successfully!', [
              { text: 'OK', onPress: () => navigation.goBack() }
            ]);
          }
        }
      ]
    );
  };

  // Toggle edit mode
  const toggleEditMode = () => {
    setIsEditing(!isEditing);
  };

  // Render feed type selector
  const renderFeedTypeSelector = () => {
    return (
      <View style={styles.optionsContainer}>
        <Text style={styles.optionsTitle}>Select Feed Type:</Text>
        <View style={styles.optionsGrid}>
          {feedTypeOptions.map(option => (
            <TouchableOpacity
              key={option}
              style={[
                styles.optionButton,
                feedType === option && styles.optionButtonSelected
              ]}
              onPress={() => setFeedType(option)}
              disabled={!isEditing}
            >
              <Text
                style={[
                  styles.optionText,
                  feedType === option && styles.optionTextSelected
                ]}
              >
                {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  // Render schedule selector
  const renderScheduleSelector = () => {
    return (
      <View style={styles.optionsContainer}>
        <Text style={styles.optionsTitle}>Select Schedule:</Text>
        <View style={styles.optionsGrid}>
          {scheduleOptions.map(option => (
            <TouchableOpacity
              key={option}
              style={[
                styles.optionButton,
                schedule === option && styles.optionButtonSelected
              ]}
              onPress={() => setSchedule(option)}
              disabled={!isEditing}
            >
              <Text
                style={[
                  styles.optionText,
                  schedule === option && styles.optionTextSelected
                ]}
              >
                {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={styles.title}>
            {isNew ? 'New Feeding Program' : isEditing ? 'Edit Feeding Program' : 'Feeding Program'}
          </Text>
          {!isNew && !isEditing && (
            <TouchableOpacity onPress={toggleEditMode} style={styles.editButton}>
              <Ionicons name="create-outline" size={24} color="#007AFF" />
            </TouchableOpacity>
          )}
          {isEditing && (
            <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          )}
        </View>

        <ScrollView style={styles.scrollView}>
          <View style={styles.formContainer}>
            {/* Animal Name */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Animal Name/Group *</Text>
              <TextInput
                style={[styles.input, !isEditing && styles.disabledInput]}
                value={animalName}
                onChangeText={setAnimalName}
                placeholder="Enter animal name or group"
                editable={isEditing}
              />
            </View>

            {/* Feed Type */}
            {renderFeedTypeSelector()}

            {/* Schedule */}
            {renderScheduleSelector()}

            {/* Amount */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Amount *</Text>
              <TextInput
                style={[styles.input, !isEditing && styles.disabledInput]}
                value={amount}
                onChangeText={setAmount}
                placeholder="Enter amount (e.g., 20 kg)"
                editable={isEditing}
              />
            </View>

            {/* Date Range */}
            <View style={styles.formRow}>
              <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                <Text style={styles.label}>Start Date *</Text>
                <TextInput
                  style={[styles.input, !isEditing && styles.disabledInput]}
                  value={startDate}
                  onChangeText={setStartDate}
                  placeholder="YYYY-MM-DD"
                  editable={isEditing}
                />
              </View>
              <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
                <Text style={styles.label}>End Date</Text>
                <TextInput
                  style={[styles.input, !isEditing && styles.disabledInput]}
                  value={endDate}
                  onChangeText={setEndDate}
                  placeholder="YYYY-MM-DD"
                  editable={isEditing}
                />
              </View>
            </View>

            {/* Notes */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Notes</Text>
              <TextInput
                style={[styles.textArea, !isEditing && styles.disabledInput]}
                value={notes}
                onChangeText={setNotes}
                placeholder="Enter any additional notes"
                multiline
                numberOfLines={4}
                editable={isEditing}
              />
            </View>

            {/* Status */}
            <View style={styles.formGroup}>
              <View style={styles.switchContainer}>
                <Text style={styles.label}>Active</Text>
                <Switch
                  value={isActive}
                  onValueChange={(value) => {
                    setIsActive(value);
                    setStatus(value ? 'Active' : 'Inactive');
                  }}
                  disabled={!isEditing}
                  trackColor={{ false: '#ccc', true: '#007AFF' }}
                  thumbColor={isActive ? '#fff' : '#f4f3f4'}
                />
              </View>
            </View>

            {/* Delete Button (only for editing existing programs) */}
            {!isNew && isEditing && (
              <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
                <Ionicons name="trash-outline" size={20} color="#fff" />
                <Text style={styles.deleteButtonText}>Delete Feeding Program</Text>
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  editButton: {
    padding: 4,
  },
  saveButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  disabledInput: {
    backgroundColor: '#f9f9f9',
    color: '#666',
  },
  textArea: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  deleteButton: {
    backgroundColor: '#FF3B30',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  deleteButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 8,
  },
  optionsContainer: {
    marginBottom: 16,
  },
  optionsTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  optionButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 10,
    margin: 4,
  },
  optionButtonSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  optionText: {
    color: '#333',
  },
  optionTextSelected: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default FeedingProgramScreen;