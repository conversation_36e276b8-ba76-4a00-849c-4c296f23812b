import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

const VetCareScreen = () => {
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [selectedTab, setSelectedTab] = useState('Upcoming');

  // Placeholder data - would come from API/state in real app
  const vetAppointments = [
    { 
      id: '1', 
      animalId: '1', 
      animalName: 'Cow #1234', 
      type: 'Vaccination', 
      date: '2023-07-15', 
      time: '09:00 AM',
      veterinarian: '<PERSON><PERSON> <PERSON>',
      location: 'On-site',
      notes: 'Annual vaccination against common diseases',
      status: 'Scheduled'
    },
    { 
      id: '2', 
      animalId: '4', 
      animalName: 'Sheep #3456', 
      type: 'Treatment', 
      date: '2023-07-10', 
      time: '10:30 AM',
      veterinarian: 'Dr. <PERSON>',
      location: 'On-site',
      notes: 'Follow-up treatment for respiratory infection',
      status: 'Scheduled'
    },
    { 
      id: '3', 
      animalId: '2', 
      animalName: 'Cow #5678', 
      type: 'Pregnancy Check', 
      date: '2023-07-05', 
      time: '02:00 PM',
      veterinarian: 'Dr. Williams',
      location: 'On-site',
      notes: 'Confirm pregnancy and check health',
      status: 'Scheduled'
    },
    { 
      id: '4', 
      animalId: '3', 
      animalName: 'Bull #9012', 
      type: 'Health Check', 
      date: '2023-06-20', 
      time: '11:00 AM',
      veterinarian: 'Dr. Smith',
      location: 'On-site',
      notes: 'Routine health examination',
      status: 'Completed'
    },
    { 
      id: '5', 
      animalId: '5', 
      animalName: 'Goat #7890', 
      type: 'Vaccination', 
      date: '2023-06-15', 
      time: '09:30 AM',
      veterinarian: 'Dr. Johnson',
      location: 'Vet Clinic',
      notes: 'Vaccination against tetanus',
      status: 'Completed'
    },
    { 
      id: '6', 
      animalGroup: 'All Cattle', 
      type: 'Herd Health Check', 
      date: '2023-06-10', 
      time: '08:00 AM',
      veterinarian: 'Dr. Williams',
      location: 'On-site',
      notes: 'Annual herd health assessment',
      status: 'Completed'
    },
    { 
      id: '7', 
      animalId: '6', 
      animalName: 'Cow #4321', 
      type: 'Surgery', 
      date: '2023-06-05', 
      time: '08:30 AM',
      veterinarian: 'Dr. Smith',
      location: 'Vet Clinic',
      notes: 'Minor surgical procedure',
      status: 'Completed'
    },
  ];

  // Filter options
  const filterOptions = ['All', 'Vaccination', 'Treatment', 'Health Check', 'Surgery'];

  // Filter appointments based on search query, selected filter, and tab
  const filteredAppointments = vetAppointments.filter(appointment => {
    const animalName = appointment.animalName || appointment.animalGroup || '';
    const matchesSearch = animalName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         appointment.notes.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         appointment.veterinarian.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = selectedFilter === 'All' || appointment.type.includes(selectedFilter);
    
    const isUpcoming = new Date(appointment.date) >= new Date() && appointment.status === 'Scheduled';
    const isPast = appointment.status === 'Completed' || new Date(appointment.date) < new Date();
    
    const matchesTab = (selectedTab === 'Upcoming' && isUpcoming) || 
                      (selectedTab === 'Past' && isPast) ||
                      selectedTab === 'All';
    
    return matchesSearch && matchesFilter && matchesTab;
  });

  // Sort appointments by date
  filteredAppointments.sort((a, b) => {
    if (selectedTab === 'Upcoming') {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    } else {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    }
  });

  // Navigate to vet appointment detail screen
  const navigateToVetAppointment = (appointmentId) => {
    navigation.navigate('VetAppointment', { appointmentId });
  };

  // Render vet appointment item
  const renderVetAppointmentItem = ({ item }) => {
    // Determine type icon
    let typeIcon;
    if (item.type.includes('Vaccination')) {
      typeIcon = <Ionicons name="fitness" size={24} color="#FF6B6B" />;
    } else if (item.type.includes('Treatment')) {
      typeIcon = <Ionicons name="bandage" size={24} color="#FFD166" />;
    } else if (item.type.includes('Health Check') || item.type.includes('Pregnancy')) {
      typeIcon = <Ionicons name="eye" size={24} color="#6B5B95" />;
    } else if (item.type.includes('Surgery')) {
      typeIcon = <Ionicons name="cut" size={24} color="#FF8C94" />;
    } else {
      typeIcon = <Ionicons name="medkit" size={24} color="#4ECDC4" />;
    }

    // Format date for display
    const appointmentDate = new Date(item.date);
    const formattedDate = appointmentDate.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });

    // Determine if appointment is today
    const today = new Date();
    const isToday = appointmentDate.getDate() === today.getDate() &&
                   appointmentDate.getMonth() === today.getMonth() &&
                   appointmentDate.getFullYear() === today.getFullYear();

    return (
      <TouchableOpacity 
        style={styles.appointmentCard}
        onPress={() => navigateToVetAppointment(item.id)}
      >
        <View style={styles.appointmentHeader}>
          {typeIcon}
          <View style={styles.appointmentHeaderText}>
            <Text style={styles.appointmentType}>{item.type}</Text>
            <Text style={styles.appointmentAnimal}>{item.animalName || item.animalGroup}</Text>
          </View>
          {isToday && (
            <View style={styles.todayBadge}>
              <Text style={styles.todayText}>TODAY</Text>
            </View>
          )}
        </View>
        
        <View style={styles.appointmentDetails}>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Ionicons name="calendar-outline" size={14} color="#666" />
              <Text style={styles.detailText}>{formattedDate}</Text>
            </View>
            <View style={styles.detailItem}>
              <Ionicons name="time-outline" size={14} color="#666" />
              <Text style={styles.detailText}>{item.time}</Text>
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Ionicons name="person-outline" size={14} color="#666" />
              <Text style={styles.detailText}>{item.veterinarian}</Text>
            </View>
            <View style={styles.detailItem}>
              <Ionicons name="location-outline" size={14} color="#666" />
              <Text style={styles.detailText}>{item.location}</Text>
            </View>
          </View>
          
          {item.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesText}>{item.notes}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Veterinary Care</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={() => navigation.navigate('VetAppointment', { isNew: true })}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Tab Selector */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'Upcoming' && styles.activeTab]}
          onPress={() => setSelectedTab('Upcoming')}
        >
          <Text style={[styles.tabText, selectedTab === 'Upcoming' && styles.activeTabText]}>Upcoming</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'Past' && styles.activeTab]}
          onPress={() => setSelectedTab('Past')}
        >
          <Text style={[styles.tabText, selectedTab === 'Past' && styles.activeTabText]}>Past</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'All' && styles.activeTab]}
          onPress={() => setSelectedTab('All')}
        >
          <Text style={[styles.tabText, selectedTab === 'All' && styles.activeTabText]}>All</Text>
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search appointments..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Filter Options */}
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {filterOptions.map(filter => (
            <TouchableOpacity
              key={filter}
              style={[
                styles.filterOption,
                selectedFilter === filter && styles.filterOptionSelected
              ]}
              onPress={() => setSelectedFilter(filter)}
            >
              <Text
                style={[
                  styles.filterText,
                  selectedFilter === filter && styles.filterTextSelected
                ]}
              >
                {filter}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Appointments List */}
      <FlatList
        data={filteredAppointments}
        renderItem={renderVetAppointmentItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="calendar" size={48} color="#ccc" />
            <Text style={styles.emptyText}>No appointments found</Text>
            <Text style={styles.emptySubtext}>Try adjusting your search or filters</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#007AFF',
    padding: 8,
    borderRadius: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    margin: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
  },
  filterContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#fff',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterOptionSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  filterText: {
    color: '#666',
    fontWeight: '500',
  },
  filterTextSelected: {
    color: '#fff',
  },
  listContainer: {
    padding: 16,
  },
  appointmentCard: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  appointmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  appointmentHeaderText: {
    flex: 1,
    marginLeft: 12,
  },
  appointmentType: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  appointmentAnimal: {
    fontSize: 14,
    color: '#666',
  },
  todayBadge: {
    backgroundColor: '#FF6B6B',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  todayText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  appointmentDetails: {
    marginLeft: 36, // Align with the header text
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  detailText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 4,
  },
  notesContainer: {
    backgroundColor: '#f9f9f9',
    padding: 8,
    borderRadius: 4,
    marginTop: 4,
  },
  notesText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
});

export default VetCareScreen;