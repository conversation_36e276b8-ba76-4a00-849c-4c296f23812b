import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, TextInput, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Text, Button, Loading, DatePicker } from 'shared';

/**
 * BreedingRecordScreen component
 * Allows viewing and editing breeding records for livestock
 */
const BreedingRecordScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { recordId, animalId } = route.params || {};
  const isNewRecord = !recordId;
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [record, setRecord] = useState({
    id: recordId || 'new',
    animalId: animalId || '',
    animalName: '',
    breedingDate: new Date(),
    mateId: '',
    mateName: '',
    mateBreed: '',
    expectedDueDate: new Date(new Date().setDate(new Date().getDate() + 283)), // Default to 283 days (cattle gestation)
    notes: '',
    status: 'pending', // pending, confirmed, unsuccessful, birthed
    birthDate: null,
    offspringCount: 0,
    offspringDetails: '',
    technician: '',
    method: 'natural', // natural, artificial
  });

  // Load record data
  useEffect(() => {
    const loadRecord = async () => {
      try {
        setIsLoading(true);
        
        if (isNewRecord) {
          // If it's a new record, load animal details
          if (animalId) {
            // In a real app, fetch animal details from API
            // For now, use mock data
            const animalDetails = {
              id: animalId,
              name: 'Bessie',
              breed: 'Holstein',
              type: 'Cattle',
              gender: 'Female',
            };
            
            setRecord(prev => ({
              ...prev,
              animalName: animalDetails.name,
            }));
          }
          
          setIsLoading(false);
          return;
        }
        
        // For existing records, fetch the record data
        // In a real app, this would be an API call
        // For now, use mock data
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
        
        const mockRecord = {
          id: recordId,
          animalId: animalId || 'A123',
          animalName: 'Bessie',
          breedingDate: new Date('2023-06-15'),
          mateId: 'B456',
          mateName: 'Ferdinand',
          mateBreed: 'Angus',
          expectedDueDate: new Date('2024-03-25'),
          notes: 'Breeding went well, animal in good health',
          status: 'confirmed',
          birthDate: null,
          offspringCount: 0,
          offspringDetails: '',
          technician: 'John Smith',
          method: 'artificial',
        };
        
        setRecord(mockRecord);
      } catch (error) {
        console.error('Error loading breeding record:', error);
        Alert.alert('Error', 'Failed to load breeding record');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadRecord();
  }, [recordId, animalId, isNewRecord]);

  // Save the record
  const saveRecord = async () => {
    try {
      setIsSaving(true);
      
      // Validate required fields
      if (!record.animalId || !record.breedingDate) {
        Alert.alert('Validation Error', 'Animal and breeding date are required');
        setIsSaving(false);
        return;
      }
      
      // In a real app, this would be an API call to save the record
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      Alert.alert(
        'Success',
        `Breeding record ${isNewRecord ? 'created' : 'updated'} successfully`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error saving breeding record:', error);
      Alert.alert('Error', 'Failed to save breeding record');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle status change
  const handleStatusChange = (newStatus) => {
    setRecord(prev => ({
      ...prev,
      status: newStatus,
      // If status is birthed, set birthDate to today if not already set
      birthDate: newStatus === 'birthed' && !prev.birthDate ? new Date() : prev.birthDate,
    }));
  };

  // Render loading state
  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#000" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {isNewRecord ? 'New Breeding Record' : 'Breeding Record'}
          </Text>
          <View style={styles.headerRight} />
        </View>
        
        <ScrollView style={styles.scrollView}>
          <Card style={styles.card}>
            <Text style={styles.sectionTitle}>Animal Information</Text>
            <View style={styles.fieldRow}>
              <Text style={styles.fieldLabel}>Animal ID:</Text>
              <Text style={styles.fieldValue}>{record.animalId}</Text>
            </View>
            <View style={styles.fieldRow}>
              <Text style={styles.fieldLabel}>Name:</Text>
              <Text style={styles.fieldValue}>{record.animalName}</Text>
            </View>
          </Card>
          
          <Card style={styles.card}>
            <Text style={styles.sectionTitle}>Breeding Details</Text>
            <View style={styles.fieldRow}>
              <Text style={styles.fieldLabel}>Breeding Date:</Text>
              <DatePicker
                date={record.breedingDate}
                onDateChange={(date) => {
                  // Calculate new expected due date based on breeding date
                  const dueDate = new Date(date);
                  dueDate.setDate(date.getDate() + 283); // Default to 283 days (cattle gestation)
                  
                  setRecord(prev => ({
                    ...prev,
                    breedingDate: date,
                    expectedDueDate: dueDate,
                  }));
                }}
                style={styles.datePicker}
              />
            </View>
            
            <View style={styles.fieldRow}>
              <Text style={styles.fieldLabel}>Method:</Text>
              <View style={styles.methodContainer}>
                <TouchableOpacity
                  style={[
                    styles.methodOption,
                    record.method === 'natural' && styles.methodOptionSelected,
                  ]}
                  onPress={() => setRecord(prev => ({ ...prev, method: 'natural' }))}
                >
                  <Text
                    style={[
                      styles.methodOptionText,
                      record.method === 'natural' && styles.methodOptionTextSelected,
                    ]}
                  >
                    Natural
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.methodOption,
                    record.method === 'artificial' && styles.methodOptionSelected,
                  ]}
                  onPress={() => setRecord(prev => ({ ...prev, method: 'artificial' }))}
                >
                  <Text
                    style={[
                      styles.methodOptionText,
                      record.method === 'artificial' && styles.methodOptionTextSelected,
                    ]}
                  >
                    Artificial
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.fieldRow}>
              <Text style={styles.fieldLabel}>Mate ID:</Text>
              <TextInput
                style={styles.textInput}
                value={record.mateId}
                onChangeText={(text) => setRecord(prev => ({ ...prev, mateId: text }))}
                placeholder="Enter mate ID"
              />
            </View>
            
            <View style={styles.fieldRow}>
              <Text style={styles.fieldLabel}>Mate Name:</Text>
              <TextInput
                style={styles.textInput}
                value={record.mateName}
                onChangeText={(text) => setRecord(prev => ({ ...prev, mateName: text }))}
                placeholder="Enter mate name"
              />
            </View>
            
            <View style={styles.fieldRow}>
              <Text style={styles.fieldLabel}>Mate Breed:</Text>
              <TextInput
                style={styles.textInput}
                value={record.mateBreed}
                onChangeText={(text) => setRecord(prev => ({ ...prev, mateBreed: text }))}
                placeholder="Enter mate breed"
              />
            </View>
            
            <View style={styles.fieldRow}>
              <Text style={styles.fieldLabel}>Technician:</Text>
              <TextInput
                style={styles.textInput}
                value={record.technician}
                onChangeText={(text) => setRecord(prev => ({ ...prev, technician: text }))}
                placeholder="Enter technician name"
              />
            </View>
          </Card>
          
          <Card style={styles.card}>
            <Text style={styles.sectionTitle}>Status & Results</Text>
            <View style={styles.statusContainer}>
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  record.status === 'pending' && styles.statusOptionSelected,
                  { backgroundColor: record.status === 'pending' ? '#FFC107' : '#F5F5F5' },
                ]}
                onPress={() => handleStatusChange('pending')}
              >
                <Text
                  style={[
                    styles.statusOptionText,
                    record.status === 'pending' && styles.statusOptionTextSelected,
                  ]}
                >
                  Pending
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  record.status === 'confirmed' && styles.statusOptionSelected,
                  { backgroundColor: record.status === 'confirmed' ? '#4CAF50' : '#F5F5F5' },
                ]}
                onPress={() => handleStatusChange('confirmed')}
              >
                <Text
                  style={[
                    styles.statusOptionText,
                    record.status === 'confirmed' && styles.statusOptionTextSelected,
                  ]}
                >
                  Confirmed
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  record.status === 'unsuccessful' && styles.statusOptionSelected,
                  { backgroundColor: record.status === 'unsuccessful' ? '#F44336' : '#F5F5F5' },
                ]}
                onPress={() => handleStatusChange('unsuccessful')}
              >
                <Text
                  style={[
                    styles.statusOptionText,
                    record.status === 'unsuccessful' && styles.statusOptionTextSelected,
                  ]}
                >
                  Unsuccessful
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.statusOption,
                  record.status === 'birthed' && styles.statusOptionSelected,
                  { backgroundColor: record.status === 'birthed' ? '#2196F3' : '#F5F5F5' },
                ]}
                onPress={() => handleStatusChange('birthed')}
              >
                <Text
                  style={[
                    styles.statusOptionText,
                    record.status === 'birthed' && styles.statusOptionTextSelected,
                  ]}
                >
                  Birthed
                </Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.fieldRow}>
              <Text style={styles.fieldLabel}>Expected Due Date:</Text>
              <DatePicker
                date={record.expectedDueDate}
                onDateChange={(date) => setRecord(prev => ({ ...prev, expectedDueDate: date }))}
                style={styles.datePicker}
              />
            </View>
            
            {record.status === 'birthed' && (
              <>
                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Birth Date:</Text>
                  <DatePicker
                    date={record.birthDate || new Date()}
                    onDateChange={(date) => setRecord(prev => ({ ...prev, birthDate: date }))}
                    style={styles.datePicker}
                  />
                </View>
                
                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Offspring Count:</Text>
                  <TextInput
                    style={styles.textInput}
                    value={record.offspringCount.toString()}
                    onChangeText={(text) => {
                      const count = parseInt(text) || 0;
                      setRecord(prev => ({ ...prev, offspringCount: count }));
                    }}
                    keyboardType="number-pad"
                    placeholder="Enter number of offspring"
                  />
                </View>
                
                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Offspring Details:</Text>
                  <TextInput
                    style={[styles.textInput, styles.textArea]}
                    value={record.offspringDetails}
                    onChangeText={(text) => setRecord(prev => ({ ...prev, offspringDetails: text }))}
                    placeholder="Enter details about the offspring"
                    multiline
                    numberOfLines={4}
                  />
                </View>
              </>
            )}
          </Card>
          
          <Card style={styles.card}>
            <Text style={styles.sectionTitle}>Notes</Text>
            <TextInput
              style={[styles.textInput, styles.textArea]}
              value={record.notes}
              onChangeText={(text) => setRecord(prev => ({ ...prev, notes: text }))}
              placeholder="Enter any notes about this breeding record"
              multiline
              numberOfLines={4}
            />
          </Card>
          
          <View style={styles.buttonContainer}>
            <Button
              title={isNewRecord ? 'Create Record' : 'Update Record'}
              onPress={saveRecord}
              loading={isSaving}
              disabled={isSaving}
              style={styles.saveButton}
            />
            {!isNewRecord && (
              <Button
                title="Delete Record"
                onPress={() => {
                  Alert.alert(
                    'Confirm Delete',
                    'Are you sure you want to delete this breeding record?',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Delete',
                        style: 'destructive',
                        onPress: () => {
                          // In a real app, this would be an API call to delete the record
                          Alert.alert(
                            'Success',
                            'Breeding record deleted successfully',
                            [{ text: 'OK', onPress: () => navigation.goBack() }]
                          );
                        },
                      },
                    ]
                  );
                }}
                type="outline"
                style={styles.deleteButton}
              />
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerRight: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  card: {
    margin: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  fieldRow: {
    marginBottom: 16,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  fieldValue: {
    fontSize: 16,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: 'white',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  datePicker: {
    width: '100%',
  },
  methodContainer: {
    flexDirection: 'row',
    marginTop: 8,
  },
  methodOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    marginRight: 8,
    alignItems: 'center',
  },
  methodOptionSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  methodOptionText: {
    fontSize: 16,
  },
  methodOptionTextSelected: {
    color: 'white',
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  statusOption: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
  },
  statusOptionSelected: {
    borderWidth: 1,
    borderColor: 'transparent',
  },
  statusOptionText: {
    fontSize: 14,
  },
  statusOptionTextSelected: {
    color: 'white',
    fontWeight: 'bold',
  },
  buttonContainer: {
    margin: 16,
  },
  saveButton: {
    marginBottom: 12,
  },
  deleteButton: {
    borderColor: '#F44336',
  },
});

export default BreedingRecordScreen;