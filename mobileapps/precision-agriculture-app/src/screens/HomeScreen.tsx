import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

type FieldSummary = {
  id: string;
  name: string;
  acres: number;
  crop: string;
  status: 'active' | 'planned' | 'harvested';
  lastActivity: string;
};

type TaskSummary = {
  id: string;
  title: string;
  dueDate: string;
  priority: 'high' | 'medium' | 'low';
  type: 'application' | 'sampling' | 'planting' | 'harvesting' | 'other';
};

type WeatherData = {
  temperature: number;
  condition: string;
  icon: string;
  precipitation: number;
  windSpeed: number;
  humidity: number;
};

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [fields, setFields] = useState<FieldSummary[]>([]);
  const [tasks, setTasks] = useState<TaskSummary[]>([]);
  const [weather, setWeather] = useState<WeatherData | null>(null);

  useEffect(() => {
    // Simulate fetching data
    setTimeout(() => {
      setFields([
        {
          id: '1',
          name: 'North Field',
          acres: 120.5,
          crop: 'Corn',
          status: 'active',
          lastActivity: '2024-07-20',
        },
        {
          id: '2',
          name: 'South Pasture',
          acres: 85.2,
          crop: 'Soybeans',
          status: 'active',
          lastActivity: '2024-07-22',
        },
        {
          id: '3',
          name: 'West Field',
          acres: 65.8,
          crop: 'Wheat',
          status: 'harvested',
          lastActivity: '2024-07-15',
        },
      ]);

      setTasks([
        {
          id: '1',
          title: 'Apply nitrogen to North Field',
          dueDate: '2024-07-30',
          priority: 'high',
          type: 'application',
        },
        {
          id: '2',
          title: 'Soil sampling for South Pasture',
          dueDate: '2024-08-05',
          priority: 'medium',
          type: 'sampling',
        },
        {
          id: '3',
          title: 'Create prescription map for West Field',
          dueDate: '2024-08-10',
          priority: 'low',
          type: 'other',
        },
      ]);

      setWeather({
        temperature: 78,
        condition: 'Partly Cloudy',
        icon: 'partly-sunny',
        precipitation: 20,
        windSpeed: 8,
        humidity: 65,
      });

      setLoading(false);
    }, 1500);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#4CAF50';
      case 'planned':
        return '#2196F3';
      case 'harvested':
        return '#FF9800';
      default:
        return '#757575';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#F44336';
      case 'medium':
        return '#FF9800';
      case 'low':
        return '#4CAF50';
      default:
        return '#757575';
    }
  };

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'application':
        return 'flask';
      case 'sampling':
        return 'leaf';
      case 'planting':
        return 'seed';
      case 'harvesting':
        return 'cut';
      default:
        return 'clipboard';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Precision Agriculture</Text>
        <TouchableOpacity onPress={() => navigation.navigate('Profile' as never)}>
          <Ionicons name="person-circle-outline" size={30} color="#2196F3" />
        </TouchableOpacity>
      </View>

      {weather && (
        <View style={styles.weatherCard}>
          <View style={styles.weatherHeader}>
            <Ionicons name={weather.icon as any} size={36} color="#2196F3" />
            <View>
              <Text style={styles.temperature}>{weather.temperature}°F</Text>
              <Text style={styles.condition}>{weather.condition}</Text>
            </View>
          </View>
          <View style={styles.weatherDetails}>
            <View style={styles.weatherItem}>
              <Ionicons name="water-outline" size={16} color="#2196F3" />
              <Text style={styles.weatherText}>{weather.precipitation}% Precip</Text>
            </View>
            <View style={styles.weatherItem}>
              <Ionicons name="speedometer-outline" size={16} color="#2196F3" />
              <Text style={styles.weatherText}>{weather.windSpeed} mph Wind</Text>
            </View>
            <View style={styles.weatherItem}>
              <Ionicons name="water" size={16} color="#2196F3" />
              <Text style={styles.weatherText}>{weather.humidity}% Humidity</Text>
            </View>
          </View>
        </View>
      )}

      <Text style={styles.sectionTitle}>Recent Fields</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.fieldsScroll}>
        {fields.map((field) => (
          <TouchableOpacity key={field.id} style={styles.fieldCard} onPress={() => navigation.navigate('Fields' as never)}>
            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(field.status) }]} />
            <Text style={styles.fieldName}>{field.name}</Text>
            <Text style={styles.fieldCrop}>{field.crop}</Text>
            <Text style={styles.fieldAcres}>{field.acres} acres</Text>
            <Text style={styles.fieldActivity}>Last activity: {field.lastActivity}</Text>
          </TouchableOpacity>
        ))}
        <TouchableOpacity style={styles.addFieldCard} onPress={() => navigation.navigate('Fields' as never)}>
          <Ionicons name="add-circle" size={36} color="#2196F3" />
          <Text style={styles.addFieldText}>Add Field</Text>
        </TouchableOpacity>
      </ScrollView>

      <Text style={styles.sectionTitle}>Upcoming Tasks</Text>
      <View style={styles.tasksContainer}>
        {tasks.map((task) => (
          <TouchableOpacity key={task.id} style={styles.taskCard}>
            <View style={styles.taskIconContainer}>
              <Ionicons name={getTaskIcon(task.type) as any} size={24} color="#2196F3" />
            </View>
            <View style={styles.taskContent}>
              <Text style={styles.taskTitle}>{task.title}</Text>
              <Text style={styles.taskDate}>Due: {task.dueDate}</Text>
            </View>
            <View
              style={[
                styles.priorityIndicator,
                { backgroundColor: getPriorityColor(task.priority) },
              ]}
            />
          </TouchableOpacity>
        ))}
        <TouchableOpacity style={styles.viewAllButton}>
          <Text style={styles.viewAllText}>View All Tasks</Text>
          <Ionicons name="chevron-forward" size={16} color="#2196F3" />
        </TouchableOpacity>
      </View>

      <Text style={styles.sectionTitle}>Quick Actions</Text>
      <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={() => navigation.navigate('Fields' as never)}>
          <Ionicons name="map" size={24} color="white" />
          <Text style={styles.actionText}>View Fields</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => navigation.navigate('Prescriptions' as never)}>
          <Ionicons name="grid" size={24} color="white" />
          <Text style={styles.actionText}>Create Prescription</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => navigation.navigate('Soil' as never)}>
          <Ionicons name="flask" size={24} color="white" />
          <Text style={styles.actionText}>Soil Sampling</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#2196F3',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  weatherCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  weatherHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  temperature: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 12,
  },
  condition: {
    fontSize: 16,
    color: '#757575',
    marginLeft: 12,
  },
  weatherDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  weatherItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  weatherText: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    color: '#333',
  },
  fieldsScroll: {
    marginBottom: 20,
  },
  fieldCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginRight: 12,
    width: 180,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
    overflow: 'hidden',
  },
  statusIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 4,
    height: '100%',
  },
  fieldName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  fieldCrop: {
    fontSize: 14,
    color: '#2196F3',
    marginBottom: 8,
  },
  fieldAcres: {
    fontSize: 14,
    color: '#757575',
    marginBottom: 8,
  },
  fieldActivity: {
    fontSize: 12,
    color: '#9E9E9E',
  },
  addFieldCard: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
    borderRadius: 8,
    padding: 16,
    marginRight: 12,
    width: 180,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(33, 150, 243, 0.3)',
    borderStyle: 'dashed',
  },
  addFieldText: {
    fontSize: 16,
    color: '#2196F3',
    marginTop: 8,
  },
  tasksContainer: {
    marginBottom: 20,
  },
  taskCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  taskIconContainer: {
    padding: 16,
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  taskContent: {
    flex: 1,
    padding: 16,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  taskDate: {
    fontSize: 14,
    color: '#757575',
    marginTop: 4,
  },
  priorityIndicator: {
    width: 4,
    height: '100%',
  },
  viewAllButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
  },
  viewAllText: {
    fontSize: 16,
    color: '#2196F3',
    marginRight: 4,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  actionButton: {
    backgroundColor: '#2196F3',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    width: '30%',
  },
  actionText: {
    color: 'white',
    marginTop: 8,
    fontSize: 12,
    textAlign: 'center',
  },
});

export default HomeScreen;