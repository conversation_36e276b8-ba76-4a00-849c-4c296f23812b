#!/bin/bash

# List of apps to update
APPS=(
  "crop-monitor-app"
  "drive-tracker-app"
  "driver-app"
  "employee-app"
  "farm-manager-app"
  "farm-safety-app"
  "field-operations-app"
  "financial-manager-app"
  "inventory-equipment-app"
  "livestock-manager-app"
  "marketplace-app"
  "precision-agriculture-app"
  "water-management-app"
  "shared"
)

# Function to update package.json
update_package_json() {
  local app_dir="$1"
  local package_json="$app_dir/package.json"

  if [ -f "$package_json" ]; then
    echo "Updating $package_json"

    # Update Expo and related dependencies
    sed -i '' 's/"expo": "\^49.0.0"/"expo": "\^53.0.0"/g' "$package_json"
    sed -i '' 's/"expo-status-bar": "~1.6.0"/"expo-status-bar": "~1.11.1"/g' "$package_json"
    sed -i '' 's/"react-native": "0.72.6"/"react-native": "0.73.2"/g' "$package_json"

    # Ensure @expo/vector-icons is included
    if ! grep -q '"@expo/vector-icons"' "$package_json"; then
      sed -i '' 's/"@react-navigation\/bottom-tabs": "\^6.5.11",/"@react-navigation\/bottom-tabs": "\^6.5.11",\n    "@expo\/vector-icons": "\^14.0.0",/g' "$package_json"
    else
      sed -i '' 's/"@expo\/vector-icons": "[^"]*"/"@expo\/vector-icons": "\^14.0.0"/g' "$package_json"
    fi

    # Update React Native related dependencies
    sed -i '' 's/"react-native-gesture-handler": "~2.12.0"/"react-native-gesture-handler": "~2.14.0"/g' "$package_json"
    sed -i '' 's/"react-native-reanimated": "~3.3.0"/"react-native-reanimated": "~3.6.0"/g' "$package_json"
    sed -i '' 's/"react-native-safe-area-context": "4.6.3"/"react-native-safe-area-context": "4.8.2"/g' "$package_json"
    sed -i '' 's/"react-native-screens": "~3.22.0"/"react-native-screens": "~3.29.0"/g' "$package_json"

    # Update React Navigation
    sed -i '' 's/"@react-navigation\/native": "\^6.1.7"/"@react-navigation\/native": "\^6.1.9"/g' "$package_json"
    sed -i '' 's/"@react-navigation\/stack": "\^6.3.17"/"@react-navigation\/stack": "\^6.3.20"/g' "$package_json"
    sed -i '' 's/"@react-navigation\/bottom-tabs": "\^6.5.8"/"@react-navigation\/bottom-tabs": "\^6.5.11"/g' "$package_json"

    # Update Expo packages
    sed -i '' 's/"expo-location": "~16.1.0"/"expo-location": "~16.5.2"/g' "$package_json"
    sed -i '' 's/"expo-file-system": "~15.4.5"/"expo-file-system": "~16.0.5"/g' "$package_json"
    sed -i '' 's/"expo-image-picker": "~14.3.2"/"expo-image-picker": "~14.7.1"/g' "$package_json"
    sed -i '' 's/"expo-camera": "~13.4.4"/"expo-camera": "~14.0.3"/g' "$package_json"
    sed -i '' 's/"expo-sqlite": "~11.3.3"/"expo-sqlite": "~13.2.1"/g' "$package_json"
    sed -i '' 's/"expo-updates": "~0.18.17"/"expo-updates": "~0.24.8"/g' "$package_json"
    sed -i '' 's/"expo-notifications": "~0.20.1"/"expo-notifications": "~0.27.4"/g' "$package_json"
    sed -i '' 's/"expo-constants": "~14.4.2"/"expo-constants": "~15.4.5"/g' "$package_json"
    sed -i '' 's/"expo-device": "~5.4.0"/"expo-device": "~5.9.3"/g' "$package_json"
    sed -i '' 's/"expo-network": "~5.4.0"/"expo-network": "~5.8.0"/g' "$package_json"
    sed -i '' 's/"expo-secure-store": "~12.3.1"/"expo-secure-store": "~12.8.1"/g' "$package_json"

    # Update other packages that might be present
    sed -i '' 's/"expo-sensors": "~12.3.0"/"expo-sensors": "~12.7.0"/g' "$package_json"
    sed -i '' 's/"expo-barcode-scanner": "~12.5.3"/"expo-barcode-scanner": "~12.9.2"/g' "$package_json"
    sed -i '' 's/"expo-document-picker": "~11.5.4"/"expo-document-picker": "~11.10.1"/g' "$package_json"
    sed -i '' 's/"expo-task-manager": "~11.3.0"/"expo-task-manager": "~11.3.0"/g' "$package_json"
    sed -i '' 's/"expo-background-fetch": "~11.3.0"/"expo-background-fetch": "~11.8.1"/g' "$package_json"
    sed -i '' 's/"react-native-maps": "1.7.1"/"react-native-maps": "1.10.0"/g' "$package_json"

    echo "Updated $package_json"
  else
    echo "Package.json not found in $app_dir"
  fi
}

# Update each app
for app in "${APPS[@]}"; do
  update_package_json "$app"
done

echo "All package.json files have been updated to Expo 53"
