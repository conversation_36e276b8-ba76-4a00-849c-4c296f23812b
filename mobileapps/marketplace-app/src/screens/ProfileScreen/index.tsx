import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Alert, Switch } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Contain<PERSON>, But<PERSON>, Loading } from 'shared/components';

// Mock user data
const MOCK_USER = {
  id: 'user123',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  avatar: 'https://via.placeholder.com/150',
  address: {
    street: '123 Farm Lane',
    city: 'Farmville',
    state: 'CA',
    zipCode: '95123',
    country: 'USA',
  },
  isSeller: true,
  joinDate: new Date(2022, 3, 15),
  preferences: {
    notifications: {
      orderUpdates: true,
      promotions: false,
      newProducts: true,
    },
    privacy: {
      shareActivity: false,
      showProfile: true,
    },
  },
};

const ProfileScreen = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  
  useEffect(() => {
    // Simulate API call to fetch user data
    const fetchUserData = async () => {
      try {
        // In a real app, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setUser(MOCK_USER);
      } catch (error) {
        console.error('Error fetching user data:', error);
        Alert.alert('Error', 'Failed to load your profile. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUserData();
  }, []);
  
  const handleEditProfile = () => {
    // In a real app, this would navigate to an edit profile screen
    Alert.alert('Edit Profile', 'Navigate to edit profile form');
  };
  
  const handleViewOrders = () => {
    navigation.navigate('Orders');
  };
  
  const handleViewSeller = () => {
    navigation.navigate('Seller');
  };
  
  const handleSavedItems = () => {
    // In a real app, this would navigate to a saved items screen
    Alert.alert('Saved Items', 'Navigate to saved items');
  };
  
  const handlePaymentMethods = () => {
    // In a real app, this would navigate to a payment methods screen
    Alert.alert('Payment Methods', 'Navigate to payment methods');
  };
  
  const handleAddresses = () => {
    // In a real app, this would navigate to an addresses screen
    Alert.alert('Addresses', 'Navigate to addresses');
  };
  
  const handleNotificationSettings = () => {
    // In a real app, this would navigate to a notification settings screen
    Alert.alert('Notification Settings', 'Navigate to notification settings');
  };
  
  const handlePrivacySettings = () => {
    // In a real app, this would navigate to a privacy settings screen
    Alert.alert('Privacy Settings', 'Navigate to privacy settings');
  };
  
  const handleHelpSupport = () => {
    // In a real app, this would navigate to a help and support screen
    Alert.alert('Help & Support', 'Navigate to help and support');
  };
  
  const handleAbout = () => {
    // In a real app, this would navigate to an about screen
    Alert.alert('About', 'Navigate to about');
  };
  
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to log out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            // In a real app, this would clear the auth token and navigate to login
            Alert.alert('Logged Out', 'You have been logged out successfully.');
          },
        },
      ]
    );
  };
  
  const toggleNotificationSetting = (key, value) => {
    // In a real app, this would update the user's notification preferences via API
    setUser(prevUser => ({
      ...prevUser,
      preferences: {
        ...prevUser.preferences,
        notifications: {
          ...prevUser.preferences.notifications,
          [key]: value,
        },
      },
    }));
  };
  
  const togglePrivacySetting = (key, value) => {
    // In a real app, this would update the user's privacy preferences via API
    setUser(prevUser => ({
      ...prevUser,
      preferences: {
        ...prevUser.preferences,
        privacy: {
          ...prevUser.preferences.privacy,
          [key]: value,
        },
      },
    }));
  };
  
  if (isLoading) {
    return <Loading visible={true} text="Loading profile..." color="#007AFF" size="large" overlay={true} />;
  }
  
  if (!user) {
    return (
      <Container>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Failed to load profile</Text>
          <Button 
            title="Try Again" 
            onPress={() => setIsLoading(true)} 
            style={styles.errorButton}
          />
        </View>
      </Container>
    );
  }
  
  return (
    <Container>
      <ScrollView>
        <View style={styles.header}>
          <View style={styles.profileHeader}>
            <Image source={{ uri: user.avatar }} style={styles.avatar} />
            
            <View style={styles.profileInfo}>
              <Text style={styles.name}>{user.name}</Text>
              <Text style={styles.email}>{user.email}</Text>
              <Text style={styles.joinDate}>Member since {user.joinDate.toLocaleDateString()}</Text>
            </View>
          </View>
          
          <Button 
            title="Edit Profile" 
            onPress={handleEditProfile} 
            style={styles.editButton}
          />
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          
          <TouchableOpacity style={styles.menuItem} onPress={handleViewOrders}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="receipt-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>My Orders</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
          
          {user.isSeller && (
            <TouchableOpacity style={styles.menuItem} onPress={handleViewSeller}>
              <View style={styles.menuItemLeft}>
                <Ionicons name="storefront-outline" size={24} color="#007AFF" style={styles.menuIcon} />
                <Text style={styles.menuText}>Seller Dashboard</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#999" />
            </TouchableOpacity>
          )}
          
          <TouchableOpacity style={styles.menuItem} onPress={handleSavedItems}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="heart-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>Saved Items</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.menuItem} onPress={handlePaymentMethods}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="card-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>Payment Methods</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.menuItem} onPress={handleAddresses}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="location-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>Addresses</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          
          <View style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="notifications-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>Order Updates</Text>
            </View>
            <Switch
              value={user.preferences.notifications.orderUpdates}
              onValueChange={(value) => toggleNotificationSetting('orderUpdates', value)}
              trackColor={{ false: '#e0e0e0', true: '#007AFF' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="pricetag-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>Promotions</Text>
            </View>
            <Switch
              value={user.preferences.notifications.promotions}
              onValueChange={(value) => toggleNotificationSetting('promotions', value)}
              trackColor={{ false: '#e0e0e0', true: '#007AFF' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="cube-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>New Products</Text>
            </View>
            <Switch
              value={user.preferences.notifications.newProducts}
              onValueChange={(value) => toggleNotificationSetting('newProducts', value)}
              trackColor={{ false: '#e0e0e0', true: '#007AFF' }}
              thumbColor="#fff"
            />
          </View>
          
          <TouchableOpacity style={styles.menuItem} onPress={handleNotificationSettings}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="settings-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>More Notification Settings</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy</Text>
          
          <View style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="share-social-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>Share Activity</Text>
            </View>
            <Switch
              value={user.preferences.privacy.shareActivity}
              onValueChange={(value) => togglePrivacySetting('shareActivity', value)}
              trackColor={{ false: '#e0e0e0', true: '#007AFF' }}
              thumbColor="#fff"
            />
          </View>
          
          <View style={styles.menuItem}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="eye-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>Show Profile</Text>
            </View>
            <Switch
              value={user.preferences.privacy.showProfile}
              onValueChange={(value) => togglePrivacySetting('showProfile', value)}
              trackColor={{ false: '#e0e0e0', true: '#007AFF' }}
              thumbColor="#fff"
            />
          </View>
          
          <TouchableOpacity style={styles.menuItem} onPress={handlePrivacySettings}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="shield-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>Privacy Settings</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          
          <TouchableOpacity style={styles.menuItem} onPress={handleHelpSupport}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="help-circle-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>Help & Support</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.menuItem} onPress={handleAbout}>
            <View style={styles.menuItemLeft}>
              <Ionicons name="information-circle-outline" size={24} color="#007AFF" style={styles.menuIcon} />
              <Text style={styles.menuText}>About</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#999" />
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
        
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>Version 1.0.0</Text>
        </View>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    padding: 20,
    backgroundColor: '#f8f8f8',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  name: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  email: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  joinDate: {
    fontSize: 12,
    color: '#999',
  },
  editButton: {
    marginTop: 8,
  },
  section: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    marginRight: 12,
  },
  menuText: {
    fontSize: 16,
    color: '#333',
  },
  logoutButton: {
    marginTop: 30,
    marginHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  logoutText: {
    fontSize: 16,
    color: '#FF3B30',
    fontWeight: '500',
  },
  versionContainer: {
    marginTop: 20,
    marginBottom: 30,
    alignItems: 'center',
  },
  versionText: {
    fontSize: 12,
    color: '#999',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 16,
  },
  errorButton: {
    width: 200,
  },
});

export default ProfileScreen;