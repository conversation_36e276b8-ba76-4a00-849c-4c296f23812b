import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet, Image, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Container, Card, Button, Loading, EmptyState } from 'shared/components';

// Mock data for featured products
const FEATURED_PRODUCTS = [
  {
    id: '1',
    name: 'Organic Fertilizer',
    price: 29.99,
    image: 'https://via.placeholder.com/150',
    seller: 'Green Farms Inc.',
    rating: 4.5,
  },
  {
    id: '2',
    name: 'Tractor Parts Kit',
    price: 149.99,
    image: 'https://via.placeholder.com/150',
    seller: 'Farm Equipment Supply',
    rating: 4.2,
  },
  {
    id: '3',
    name: 'Irrigation System',
    price: 299.99,
    image: 'https://via.placeholder.com/150',
    seller: 'Water Solutions Co.',
    rating: 4.8,
  },
];

// Mock data for categories
const CATEGORIES = [
  { id: '1', name: 'Equipment', icon: '🚜' },
  { id: '2', name: 'Seeds', icon: '🌱' },
  { id: '3', name: 'Fertilizers', icon: '💧' },
  { id: '4', name: 'Tools', icon: '🔧' },
  { id: '5', name: 'Livestock', icon: '🐄' },
  { id: '6', name: 'Produce', icon: '🌽' },
];

const HomeScreen = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    // Simulate API call to fetch data
    const fetchData = async () => {
      try {
        // In a real app, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setFeaturedProducts(FEATURED_PRODUCTS);
        setCategories(CATEGORIES);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleProductPress = (product) => {
    navigation.navigate('ProductDetail', { product });
  };

  const handleCategoryPress = (category) => {
    navigation.navigate('Products', { category });
  };

  const handleViewAllProducts = () => {
    navigation.navigate('Products');
  };

  const renderProductItem = ({ item }) => (
    <TouchableOpacity onPress={() => handleProductPress(item)}>
      <Card style={styles.productCard}>
        <Image source={{ uri: item.image }} style={styles.productImage} />
        <View style={styles.productInfo}>
          <Text style={styles.productName}>{item.name}</Text>
          <Text style={styles.productPrice}>${item.price.toFixed(2)}</Text>
          <Text style={styles.productSeller}>{item.seller}</Text>
          <View style={styles.ratingContainer}>
            <Text style={styles.rating}>★ {item.rating}</Text>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderCategoryItem = ({ item }) => (
    <TouchableOpacity 
      style={styles.categoryItem} 
      onPress={() => handleCategoryPress(item)}
    >
      <Text style={styles.categoryIcon}>{item.icon}</Text>
      <Text style={styles.categoryName}>{item.name}</Text>
    </TouchableOpacity>
  );

  if (isLoading) {
    return <Loading visible={true} text="Loading marketplace..." color="#007AFF" size="large" overlay={true} />;
  }

  return (
    <Container>
      <ScrollView>
        <View style={styles.header}>
          <Text style={styles.title}>NxtAcre Marketplace</Text>
          <Text style={styles.subtitle}>Buy and sell farm products</Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Categories</Text>
          </View>
          <FlatList
            data={categories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Products</Text>
            <TouchableOpacity onPress={handleViewAllProducts}>
              <Text style={styles.viewAll}>View All</Text>
            </TouchableOpacity>
          </View>
          {featuredProducts.length > 0 ? (
            <FlatList
              data={featuredProducts}
              renderItem={renderProductItem}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.productsList}
            />
          ) : (
            <EmptyState
              title="No Products Found"
              message="There are no featured products available at the moment."
              icon="cube-outline"
            />
          )}
        </View>

        <View style={styles.section}>
          <View style={styles.sellerCta}>
            <Text style={styles.sellerCtaTitle}>Become a Seller</Text>
            <Text style={styles.sellerCtaText}>
              Start selling your farm products on NxtAcre Marketplace
            </Text>
            <Button 
              title="Start Selling" 
              onPress={() => navigation.navigate('Seller')} 
              style={styles.sellerCtaButton}
            />
          </View>
        </View>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    padding: 20,
    backgroundColor: '#f8f8f8',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  section: {
    marginVertical: 15,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  viewAll: {
    fontSize: 14,
    color: '#007AFF',
  },
  categoriesList: {
    paddingHorizontal: 15,
  },
  categoryItem: {
    alignItems: 'center',
    marginHorizontal: 10,
    width: 80,
  },
  categoryIcon: {
    fontSize: 30,
    marginBottom: 5,
  },
  categoryName: {
    fontSize: 12,
    textAlign: 'center',
    color: '#333',
  },
  productsList: {
    paddingHorizontal: 15,
  },
  productCard: {
    width: 180,
    marginHorizontal: 8,
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  productInfo: {
    padding: 10,
  },
  productName: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#333',
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 5,
  },
  productSeller: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: 12,
    color: '#FF9500',
  },
  sellerCta: {
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
    padding: 20,
    marginHorizontal: 20,
    alignItems: 'center',
  },
  sellerCtaTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  sellerCtaText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 15,
  },
  sellerCtaButton: {
    width: '100%',
  },
});

export default HomeScreen;