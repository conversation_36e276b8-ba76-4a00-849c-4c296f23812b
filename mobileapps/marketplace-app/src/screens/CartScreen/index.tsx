import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Image, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Container, Button, Loading, EmptyState } from 'shared/components';

// Mock data for cart items
const MOCK_CART_ITEMS = [
  {
    id: '1',
    productId: '1',
    name: 'Organic Fertilizer',
    price: 29.99,
    image: 'https://via.placeholder.com/150',
    seller: 'Green Farms Inc.',
    quantity: 2,
  },
  {
    id: '2',
    productId: '4',
    name: 'Heirloom Tomato Seeds',
    price: 4.99,
    image: 'https://via.placeholder.com/150',
    seller: 'Heritage Seeds',
    quantity: 1,
  },
  {
    id: '3',
    productId: '5',
    name: 'Pruning Shears',
    price: 24.99,
    image: 'https://via.placeholder.com/150',
    seller: 'Garden Tools Inc.',
    quantity: 1,
  },
];

const CartScreen = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [cartItems, setCartItems] = useState([]);
  const [isUpdatingCart, setIsUpdatingCart] = useState(false);
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  
  useEffect(() => {
    // Simulate API call to fetch cart data
    const fetchCart = async () => {
      try {
        // In a real app, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setCartItems(MOCK_CART_ITEMS);
      } catch (error) {
        console.error('Error fetching cart:', error);
        Alert.alert('Error', 'Failed to load your cart. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCart();
  }, []);
  
  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };
  
  const calculateShipping = () => {
    const subtotal = calculateSubtotal();
    // Free shipping for orders over $50
    return subtotal >= 50 ? 0 : 5.99;
  };
  
  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping();
  };
  
  const handleUpdateQuantity = async (itemId, newQuantity) => {
    if (newQuantity < 1) {
      handleRemoveItem(itemId);
      return;
    }
    
    setIsUpdatingCart(true);
    
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setCartItems(prevItems => 
        prevItems.map(item => 
          item.id === itemId ? { ...item, quantity: newQuantity } : item
        )
      );
    } catch (error) {
      console.error('Error updating quantity:', error);
      Alert.alert('Error', 'Failed to update quantity. Please try again.');
    } finally {
      setIsUpdatingCart(false);
    }
  };
  
  const handleRemoveItem = async (itemId) => {
    setIsUpdatingCart(true);
    
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setCartItems(prevItems => prevItems.filter(item => item.id !== itemId));
    } catch (error) {
      console.error('Error removing item:', error);
      Alert.alert('Error', 'Failed to remove item. Please try again.');
    } finally {
      setIsUpdatingCart(false);
    }
  };
  
  const handleClearCart = () => {
    Alert.alert(
      'Clear Cart',
      'Are you sure you want to remove all items from your cart?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            setIsUpdatingCart(true);
            try {
              // In a real app, this would be an API call
              await new Promise(resolve => setTimeout(resolve, 500));
              setCartItems([]);
            } catch (error) {
              console.error('Error clearing cart:', error);
              Alert.alert('Error', 'Failed to clear cart. Please try again.');
            } finally {
              setIsUpdatingCart(false);
            }
          },
        },
      ]
    );
  };
  
  const handleCheckout = async () => {
    if (cartItems.length === 0) {
      Alert.alert('Empty Cart', 'Your cart is empty. Add some products before checking out.');
      return;
    }
    
    setIsCheckingOut(true);
    
    try {
      // In a real app, this would navigate to a checkout flow or payment screen
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For demo purposes, just show a success message
      Alert.alert(
        'Order Placed',
        'Your order has been successfully placed!',
        [
          {
            text: 'View Orders',
            onPress: () => navigation.navigate('Orders'),
          },
          {
            text: 'Continue Shopping',
            onPress: () => {
              setCartItems([]);
              navigation.navigate('Home');
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error during checkout:', error);
      Alert.alert('Error', 'Failed to complete checkout. Please try again.');
    } finally {
      setIsCheckingOut(false);
    }
  };
  
  const renderCartItem = ({ item }) => (
    <View style={styles.cartItem}>
      <Image source={{ uri: item.image }} style={styles.itemImage} />
      
      <View style={styles.itemDetails}>
        <Text style={styles.itemName}>{item.name}</Text>
        <Text style={styles.itemSeller}>{item.seller}</Text>
        <Text style={styles.itemPrice}>${item.price.toFixed(2)}</Text>
        
        <View style={styles.quantityContainer}>
          <TouchableOpacity 
            style={styles.quantityButton}
            onPress={() => handleUpdateQuantity(item.id, item.quantity - 1)}
            disabled={isUpdatingCart}
          >
            <Ionicons name="remove" size={16} color="#333" />
          </TouchableOpacity>
          
          <Text style={styles.quantityText}>{item.quantity}</Text>
          
          <TouchableOpacity 
            style={styles.quantityButton}
            onPress={() => handleUpdateQuantity(item.id, item.quantity + 1)}
            disabled={isUpdatingCart}
          >
            <Ionicons name="add" size={16} color="#333" />
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.itemActions}>
        <Text style={styles.itemTotal}>${(item.price * item.quantity).toFixed(2)}</Text>
        
        <TouchableOpacity 
          style={styles.removeButton}
          onPress={() => handleRemoveItem(item.id)}
          disabled={isUpdatingCart}
        >
          <Ionicons name="trash-outline" size={20} color="#FF3B30" />
        </TouchableOpacity>
      </View>
    </View>
  );
  
  const renderSeparator = () => <View style={styles.separator} />;
  
  if (isLoading) {
    return <Loading visible={true} text="Loading cart..." color="#007AFF" size="large" overlay={true} />;
  }
  
  if (cartItems.length === 0) {
    return (
      <Container>
        <EmptyState
          title="Your Cart is Empty"
          message="Add some products to your cart to see them here."
          icon="cart-outline"
        />
        <Button 
          title="Browse Products" 
          onPress={() => navigation.navigate('Products')} 
          style={styles.browseButton}
        />
      </Container>
    );
  }
  
  return (
    <Container>
      <View style={styles.header}>
        <Text style={styles.title}>Shopping Cart</Text>
        <TouchableOpacity onPress={handleClearCart} disabled={isUpdatingCart}>
          <Text style={styles.clearButton}>Clear Cart</Text>
        </TouchableOpacity>
      </View>
      
      <FlatList
        data={cartItems}
        renderItem={renderCartItem}
        keyExtractor={(item) => item.id}
        ItemSeparatorComponent={renderSeparator}
        contentContainerStyle={styles.cartList}
      />
      
      <View style={styles.summaryContainer}>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Subtotal</Text>
          <Text style={styles.summaryValue}>${calculateSubtotal().toFixed(2)}</Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Shipping</Text>
          <Text style={styles.summaryValue}>
            {calculateShipping() === 0 ? 'Free' : `$${calculateShipping().toFixed(2)}`}
          </Text>
        </View>
        
        <View style={styles.divider} />
        
        <View style={styles.summaryRow}>
          <Text style={styles.totalLabel}>Total</Text>
          <Text style={styles.totalValue}>${calculateTotal().toFixed(2)}</Text>
        </View>
        
        <Button 
          title={isCheckingOut ? 'Processing...' : 'Proceed to Checkout'} 
          onPress={handleCheckout} 
          disabled={isCheckingOut || cartItems.length === 0}
          style={styles.checkoutButton}
        />
      </View>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  clearButton: {
    fontSize: 14,
    color: '#FF3B30',
  },
  cartList: {
    paddingBottom: 16,
  },
  cartItem: {
    flexDirection: 'row',
    padding: 16,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  itemDetails: {
    flex: 1,
    marginLeft: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  itemSeller: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
    marginBottom: 8,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  quantityText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginHorizontal: 8,
    minWidth: 20,
    textAlign: 'center',
  },
  itemActions: {
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  itemTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  removeButton: {
    padding: 4,
  },
  separator: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginLeft: 16,
  },
  summaryContainer: {
    padding: 16,
    backgroundColor: '#f8f8f8',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 12,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  checkoutButton: {
    marginTop: 16,
  },
  browseButton: {
    margin: 16,
  },
});

export default CartScreen;