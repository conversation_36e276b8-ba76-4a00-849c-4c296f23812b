import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Text, View } from 'react-native';
import { Button } from 'shared/components';

// This will be replaced with actual navigation
const AppContent = () => {
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <Text style={{ fontSize: 24, marginBottom: 20 }}>Employee App</Text>
      <Text style={{ fontSize: 16, marginBottom: 20, textAlign: 'center', paddingHorizontal: 20 }}>
        This app will provide tools for time tracking, task management, work order management, and access to training materials and safety information.
      </Text>
      <Button 
        title="Sample Button" 
        onPress={() => alert('Button pressed!')} 
        style={{ marginTop: 10 }}
      />
    </View>
  );
};

export default function App() {
  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <AppContent />
      </NavigationContainer>
      <StatusBar style="auto" />
    </SafeAreaProvider>
  );
}