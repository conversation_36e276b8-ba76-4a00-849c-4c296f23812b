import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { <PERSON><PERSON>, <PERSON> } from 'shared';

/**
 * Home screen for the Field Operations App
 * Displays a summary of fields, tasks, and weather information
 */
const HomeScreen = ({ navigation }) => {
  // Placeholder data - would be fetched from an API in a real app
  const fieldSummary = {
    total: 12,
    active: 8,
    needsAttention: 2,
  };

  const taskSummary = {
    today: 5,
    pending: 3,
    completed: 2,
  };

  const weatherSummary = {
    temperature: '72°F',
    condition: 'Partly Cloudy',
    precipitation: '10%',
    wind: '5 mph',
  };

  const quickActions = [
    { title: 'View Fields', action: () => navigation.navigate('Fields') },
    { title: 'Start Field Operation', action: () => navigation.navigate('FieldOperation') },
    { title: 'View Tasks', action: () => navigation.navigate('Tasks') },
    { title: 'Field Scouting', action: () => navigation.navigate('FieldScouting') },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        <Text style={styles.greeting}>Hello, Operator!</Text>
        <Text style={styles.date}>{new Date().toDateString()}</Text>

        <Card style={styles.weatherCard}>
          <Text style={styles.cardTitle}>Current Weather</Text>
          <View style={styles.weatherContainer}>
            <Text style={styles.temperature}>{weatherSummary.temperature}</Text>
            <View style={styles.weatherDetails}>
              <Text style={styles.weatherCondition}>{weatherSummary.condition}</Text>
              <Text style={styles.weatherInfo}>Precipitation: {weatherSummary.precipitation}</Text>
              <Text style={styles.weatherInfo}>Wind: {weatherSummary.wind}</Text>
            </View>
          </View>
        </Card>

        <View style={styles.summaryContainer}>
          <Card style={styles.summaryCard}>
            <Text style={styles.cardTitle}>Fields</Text>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{fieldSummary.total}</Text>
                <Text style={styles.statLabel}>Total</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{fieldSummary.active}</Text>
                <Text style={styles.statLabel}>Active</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{fieldSummary.needsAttention}</Text>
                <Text style={styles.statLabel}>Needs Attention</Text>
              </View>
            </View>
          </Card>

          <Card style={styles.summaryCard}>
            <Text style={styles.cardTitle}>Tasks</Text>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{taskSummary.today}</Text>
                <Text style={styles.statLabel}>Today</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{taskSummary.pending}</Text>
                <Text style={styles.statLabel}>Pending</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{taskSummary.completed}</Text>
                <Text style={styles.statLabel}>Completed</Text>
              </View>
            </View>
          </Card>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionsContainer}>
          {quickActions.map((action, index) => (
            <Button
              key={index}
              title={action.title}
              onPress={action.action}
              variant="secondary"
              style={styles.actionButton}
            />
          ))}
        </View>

        <Text style={styles.sectionTitle}>Recent Activity</Text>
        <Card style={styles.activityCard}>
          <Text style={styles.activityText}>No recent activity</Text>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 16,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  date: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
  },
  weatherCard: {
    marginBottom: 24,
    padding: 16,
  },
  weatherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  temperature: {
    fontSize: 36,
    fontWeight: 'bold',
    marginRight: 16,
  },
  weatherDetails: {
    flex: 1,
  },
  weatherCondition: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  weatherInfo: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  summaryCard: {
    width: '48%',
    padding: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statsContainer: {
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  actionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  actionButton: {
    width: '48%',
    marginBottom: 12,
  },
  activityCard: {
    padding: 16,
    marginBottom: 24,
  },
  activityText: {
    textAlign: 'center',
    color: '#666',
  },
});

export default HomeScreen;