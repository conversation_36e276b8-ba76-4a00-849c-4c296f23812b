import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Mock data for tasks (same as in TasksScreen)
const MOCK_TASKS = [
  {
    id: '1',
    title: 'Spray herbicide in North Field',
    description: 'Apply post-emergent herbicide to control weeds. Use glyphosate at recommended rate. Avoid spraying when wind speed exceeds 10 mph. Wear appropriate PPE including gloves, goggles, and respirator.',
    dueDate: '2024-07-05',
    status: 'pending',
    priority: 'high',
    assignedTo: '<PERSON>',
    field: 'North Field',
    equipment: 'Sprayer Tractor',
    notes: 'Check weather forecast before starting. Document application rate and coverage area.',
    createdAt: '2024-06-20',
    updatedAt: '2024-06-25',
  },
  {
    id: '2',
    title: 'Plant corn in East Field',
    description: 'Plant corn seeds at 30,000 seeds per acre. Ensure proper depth and spacing. Check planter calibration before starting.',
    dueDate: '2024-07-10',
    status: 'pending',
    priority: 'medium',
    assignedTo: 'Jane Smith',
    field: 'East Field',
    equipment: 'Planter Tractor',
    notes: 'Use variety XYZ-123. Ensure seed treatment is applied.',
    createdAt: '2024-06-22',
    updatedAt: '2024-06-22',
  },
  {
    id: '3',
    title: 'Harvest wheat in South Field',
    description: 'Harvest wheat when moisture content is below 14%. Adjust combine settings for optimal grain quality and minimal losses.',
    dueDate: '2024-07-15',
    status: 'pending',
    priority: 'high',
    assignedTo: 'John Doe',
    field: 'South Field',
    equipment: 'Combine Harvester',
    notes: 'Check grain moisture content daily. Coordinate with grain haulers.',
    createdAt: '2024-06-18',
    updatedAt: '2024-06-24',
  },
  {
    id: '4',
    title: 'Repair irrigation system in West Field',
    description: 'Fix leaking irrigation pipes. Replace damaged sections and check for proper water pressure.',
    dueDate: '2024-07-03',
    status: 'completed',
    priority: 'medium',
    assignedTo: 'Bob Johnson',
    field: 'West Field',
    equipment: 'Pipe Wrench, PVC Pipe',
    notes: 'Parts ordered and delivered. System will need to be shut down during repairs.',
    createdAt: '2024-06-15',
    updatedAt: '2024-06-30',
  },
  {
    id: '5',
    title: 'Scout for pests in North Field',
    description: 'Check for corn rootworm and other pests. Document findings and recommend treatment if necessary.',
    dueDate: '2024-07-08',
    status: 'in-progress',
    priority: 'low',
    assignedTo: 'Jane Smith',
    field: 'North Field',
    equipment: 'Scouting Kit',
    notes: 'Focus on field edges and areas with previous pest pressure.',
    createdAt: '2024-06-25',
    updatedAt: '2024-06-28',
  },
];

const TaskDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { taskId } = route.params || {};
  
  // Find the task by ID
  const task = MOCK_TASKS.find(t => t.id === taskId);
  
  // State for task status (to allow updating)
  const [status, setStatus] = useState(task?.status || 'pending');
  
  // Handle back button press
  const handleBackPress = () => {
    navigation.goBack();
  };
  
  // Handle status change
  const handleStatusChange = (newStatus) => {
    setStatus(newStatus);
    // In a real app, this would update the task in the database
    Alert.alert('Status Updated', `Task status changed to ${newStatus}`);
  };
  
  // Handle edit button press
  const handleEditPress = () => {
    // In a real app, this would navigate to an edit screen
    Alert.alert('Edit Task', 'Edit functionality will be implemented soon');
  };
  
  // Handle delete button press
  const handleDeletePress = () => {
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            // In a real app, this would delete the task from the database
            Alert.alert('Task Deleted', 'Task has been deleted');
            navigation.goBack();
          },
        },
      ]
    );
  };
  
  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return '#FF4D4F';
      case 'medium':
        return '#FAAD14';
      case 'low':
        return '#52C41A';
      default:
        return '#1890FF';
    }
  };
  
  // Get status color and label
  const getStatusInfo = (statusValue) => {
    switch (statusValue) {
      case 'completed':
        return { color: '#52C41A', label: 'Completed', icon: 'checkmark-circle' };
      case 'in-progress':
        return { color: '#1890FF', label: 'In Progress', icon: 'time' };
      case 'pending':
        return { color: '#FAAD14', label: 'Pending', icon: 'hourglass' };
      default:
        return { color: '#8C8C8C', label: 'Unknown', icon: 'help-circle' };
    }
  };
  
  // If task not found
  if (!task) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Task Detail</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.notFoundContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#E0E0E0" />
          <Text style={styles.notFoundText}>Task not found</Text>
        </View>
      </SafeAreaView>
    );
  }
  
  const priorityColor = getPriorityColor(task.priority);
  const statusInfo = getStatusInfo(status);
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Task Detail</Text>
        <TouchableOpacity onPress={handleEditPress} style={styles.editButton}>
          <Ionicons name="create-outline" size={24} color="#333" />
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.scrollView}>
        <View style={styles.titleContainer}>
          <View style={[styles.priorityIndicator, { backgroundColor: priorityColor }]} />
          <Text style={styles.title}>{task.title}</Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>{task.description}</Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Status</Text>
          <View style={styles.statusContainer}>
            <TouchableOpacity
              style={[
                styles.statusButton,
                status === 'pending' && { backgroundColor: getStatusInfo('pending').color },
              ]}
              onPress={() => handleStatusChange('pending')}
            >
              <Ionicons
                name="hourglass"
                size={20}
                color={status === 'pending' ? 'white' : getStatusInfo('pending').color}
              />
              <Text
                style={[
                  styles.statusButtonText,
                  status === 'pending' && { color: 'white' },
                ]}
              >
                Pending
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.statusButton,
                status === 'in-progress' && { backgroundColor: getStatusInfo('in-progress').color },
              ]}
              onPress={() => handleStatusChange('in-progress')}
            >
              <Ionicons
                name="time"
                size={20}
                color={status === 'in-progress' ? 'white' : getStatusInfo('in-progress').color}
              />
              <Text
                style={[
                  styles.statusButtonText,
                  status === 'in-progress' && { color: 'white' },
                ]}
              >
                In Progress
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[
                styles.statusButton,
                status === 'completed' && { backgroundColor: getStatusInfo('completed').color },
              ]}
              onPress={() => handleStatusChange('completed')}
            >
              <Ionicons
                name="checkmark-circle"
                size={20}
                color={status === 'completed' ? 'white' : getStatusInfo('completed').color}
              />
              <Text
                style={[
                  styles.statusButtonText,
                  status === 'completed' && { color: 'white' },
                ]}
              >
                Completed
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Details</Text>
          <View style={styles.detailsContainer}>
            <View style={styles.detailRow}>
              <View style={styles.detailItem}>
                <Ionicons name="calendar-outline" size={20} color="#8C8C8C" />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Due Date</Text>
                  <Text style={styles.detailValue}>{task.dueDate}</Text>
                </View>
              </View>
              
              <View style={styles.detailItem}>
                <Ionicons name="flag-outline" size={20} color="#8C8C8C" />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Priority</Text>
                  <Text style={[styles.detailValue, { color: priorityColor }]}>
                    {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                  </Text>
                </View>
              </View>
            </View>
            
            <View style={styles.detailRow}>
              <View style={styles.detailItem}>
                <Ionicons name="person-outline" size={20} color="#8C8C8C" />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Assigned To</Text>
                  <Text style={styles.detailValue}>{task.assignedTo}</Text>
                </View>
              </View>
              
              <View style={styles.detailItem}>
                <Ionicons name="location-outline" size={20} color="#8C8C8C" />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Field</Text>
                  <Text style={styles.detailValue}>{task.field}</Text>
                </View>
              </View>
            </View>
            
            <View style={styles.detailRow}>
              <View style={styles.detailItem}>
                <Ionicons name="construct-outline" size={20} color="#8C8C8C" />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Equipment</Text>
                  <Text style={styles.detailValue}>{task.equipment}</Text>
                </View>
              </View>
              
              <View style={styles.detailItem}>
                <Ionicons name="time-outline" size={20} color="#8C8C8C" />
                <View style={styles.detailTextContainer}>
                  <Text style={styles.detailLabel}>Created</Text>
                  <Text style={styles.detailValue}>{task.createdAt}</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notes</Text>
          <Text style={styles.notes}>{task.notes}</Text>
        </View>
        
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Map', { fieldName: task.field })}
          >
            <Ionicons name="map-outline" size={20} color="#007AFF" />
            <Text style={styles.actionButtonText}>View Field</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => navigation.navigate('Scouting', { fieldName: task.field })}
          >
            <Ionicons name="search-outline" size={20} color="#007AFF" />
            <Text style={styles.actionButtonText}>Scout Field</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={handleDeletePress}
          >
            <Ionicons name="trash-outline" size={20} color="#FF4D4F" />
            <Text style={[styles.actionButtonText, styles.deleteButtonText]}>Delete Task</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  editButton: {
    padding: 4,
  },
  scrollView: {
    flex: 1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 12,
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  title: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: 'white',
    flex: 1,
    marginHorizontal: 4,
  },
  statusButtonText: {
    fontSize: 14,
    marginLeft: 4,
  },
  detailsContainer: {
    marginTop: 8,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  detailItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  detailTextContainer: {
    marginLeft: 8,
    flex: 1,
  },
  detailLabel: {
    fontSize: 12,
    color: '#8C8C8C',
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    color: '#333',
  },
  notes: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  actionsContainer: {
    backgroundColor: 'white',
    padding: 16,
    marginBottom: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  actionButtonText: {
    fontSize: 16,
    color: '#007AFF',
    marginLeft: 12,
  },
  deleteButton: {
    borderBottomWidth: 0,
  },
  deleteButtonText: {
    color: '#FF4D4F',
  },
  notFoundContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  notFoundText: {
    fontSize: 18,
    color: '#8C8C8C',
    marginTop: 16,
  },
});

export default TaskDetailScreen;