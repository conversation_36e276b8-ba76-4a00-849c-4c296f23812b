import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Mock data for tasks
const MOCK_TASKS = [
  {
    id: '1',
    title: 'Spray herbicide in North Field',
    description: 'Apply post-emergent herbicide to control weeds',
    dueDate: '2024-07-05',
    status: 'pending',
    priority: 'high',
    assignedTo: '<PERSON>',
    field: 'North Field',
  },
  {
    id: '2',
    title: 'Plant corn in East Field',
    description: 'Plant corn seeds at 30,000 seeds per acre',
    dueDate: '2024-07-10',
    status: 'pending',
    priority: 'medium',
    assignedTo: 'Jane Smith',
    field: 'East Field',
  },
  {
    id: '3',
    title: 'Harvest wheat in South Field',
    description: 'Harvest wheat when moisture content is below 14%',
    dueDate: '2024-07-15',
    status: 'pending',
    priority: 'high',
    assignedTo: '<PERSON>',
    field: 'South Field',
  },
  {
    id: '4',
    title: 'Repair irrigation system in West Field',
    description: 'Fix leaking irrigation pipes',
    dueDate: '2024-07-03',
    status: 'completed',
    priority: 'medium',
    assignedTo: 'Bob Johnson',
    field: 'West Field',
  },
  {
    id: '5',
    title: 'Scout for pests in North Field',
    description: 'Check for corn rootworm and other pests',
    dueDate: '2024-07-08',
    status: 'in-progress',
    priority: 'low',
    assignedTo: 'Jane Smith',
    field: 'North Field',
  },
];

const TasksScreen = () => {
  const navigation = useNavigation();
  const [tasks, setTasks] = useState(MOCK_TASKS);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Filter tasks based on search query and status filter
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         task.field.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || task.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  // Get priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high':
        return '#FF4D4F';
      case 'medium':
        return '#FAAD14';
      case 'low':
        return '#52C41A';
      default:
        return '#1890FF';
    }
  };

  // Get status color and icon
  const getStatusInfo = (status) => {
    switch (status) {
      case 'completed':
        return { color: '#52C41A', icon: 'checkmark-circle' };
      case 'in-progress':
        return { color: '#1890FF', icon: 'time' };
      case 'pending':
        return { color: '#FAAD14', icon: 'hourglass' };
      default:
        return { color: '#8C8C8C', icon: 'help-circle' };
    }
  };

  // Render task item
  const renderTaskItem = ({ item }) => {
    const priorityColor = getPriorityColor(item.priority);
    const statusInfo = getStatusInfo(item.status);
    
    return (
      <TouchableOpacity
        style={styles.taskItem}
        onPress={() => navigation.navigate('TaskDetail', { taskId: item.id })}
      >
        <View style={styles.taskHeader}>
          <View style={[styles.priorityIndicator, { backgroundColor: priorityColor }]} />
          <Text style={styles.taskTitle} numberOfLines={1}>{item.title}</Text>
          <Ionicons name={statusInfo.icon} size={20} color={statusInfo.color} />
        </View>
        
        <Text style={styles.taskDescription} numberOfLines={2}>{item.description}</Text>
        
        <View style={styles.taskFooter}>
          <View style={styles.taskInfo}>
            <Ionicons name="calendar-outline" size={16} color="#8C8C8C" />
            <Text style={styles.taskInfoText}>{item.dueDate}</Text>
          </View>
          
          <View style={styles.taskInfo}>
            <Ionicons name="person-outline" size={16} color="#8C8C8C" />
            <Text style={styles.taskInfoText}>{item.assignedTo}</Text>
          </View>
          
          <View style={styles.taskInfo}>
            <Ionicons name="location-outline" size={16} color="#8C8C8C" />
            <Text style={styles.taskInfoText}>{item.field}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Tasks</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={() => {
            // Navigate to task creation screen (to be implemented)
            alert('Create task functionality will be implemented soon');
          }}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#8C8C8C" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search tasks..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>
      
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'all' && styles.activeFilter]}
          onPress={() => setFilterStatus('all')}
        >
          <Text style={[styles.filterText, filterStatus === 'all' && styles.activeFilterText]}>All</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'pending' && styles.activeFilter]}
          onPress={() => setFilterStatus('pending')}
        >
          <Text style={[styles.filterText, filterStatus === 'pending' && styles.activeFilterText]}>Pending</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'in-progress' && styles.activeFilter]}
          onPress={() => setFilterStatus('in-progress')}
        >
          <Text style={[styles.filterText, filterStatus === 'in-progress' && styles.activeFilterText]}>In Progress</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterButton, filterStatus === 'completed' && styles.activeFilter]}
          onPress={() => setFilterStatus('completed')}
        >
          <Text style={[styles.filterText, filterStatus === 'completed' && styles.activeFilterText]}>Completed</Text>
        </TouchableOpacity>
      </View>
      
      <FlatList
        data={filteredTasks}
        renderItem={renderTaskItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.taskList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="list" size={64} color="#E0E0E0" />
            <Text style={styles.emptyText}>No tasks found</Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#007AFF',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 8,
    marginHorizontal: 16,
    marginBottom: 12,
    paddingHorizontal: 12,
    height: 44,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 44,
    fontSize: 16,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    backgroundColor: '#E0E0E0',
  },
  activeFilter: {
    backgroundColor: '#007AFF',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterText: {
    color: 'white',
  },
  taskList: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  taskItem: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  priorityIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  taskTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
  },
  taskDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  taskInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taskInfoText: {
    fontSize: 12,
    color: '#8C8C8C',
    marginLeft: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 16,
    color: '#8C8C8C',
    marginTop: 16,
  },
});

export default TasksScreen;