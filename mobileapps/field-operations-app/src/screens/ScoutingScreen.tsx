import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Image, Alert, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as ImagePicker from 'expo-image-picker';
import * as Location from 'expo-location';

// Mock data for fields
const MOCK_FIELDS = [
  {
    id: '1',
    name: 'North Field',
    crop: 'Corn',
    area: '45.2 acres',
    status: 'Active',
  },
  {
    id: '2',
    name: 'East Field',
    crop: 'Soybeans',
    area: '38.7 acres',
    status: 'Active',
  },
  {
    id: '3',
    name: 'South Field',
    crop: 'Wheat',
    area: '42.1 acres',
    status: 'Active',
  },
  {
    id: '4',
    name: 'West Field',
    crop: 'Alfalfa',
    area: '36.5 acres',
    status: 'Active',
  },
];

// Mock data for observation types
const OBSERVATION_TYPES = [
  { id: '1', name: 'Pest', icon: 'bug' },
  { id: '2', name: 'Disease', icon: 'medkit' },
  { id: '3', name: 'Weed', icon: 'leaf' },
  { id: '4', name: 'Nutrient Deficiency', icon: 'flask' },
  { id: '5', name: 'Water Issue', icon: 'water' },
  { id: '6', name: 'Growth Stage', icon: 'analytics' },
  { id: '7', name: 'Damage', icon: 'warning' },
  { id: '8', name: 'Other', icon: 'ellipsis-horizontal' },
];

// Mock data for severity levels
const SEVERITY_LEVELS = [
  { id: '1', name: 'Low', color: '#52C41A' },
  { id: '2', name: 'Medium', color: '#FAAD14' },
  { id: '3', name: 'High', color: '#FF4D4F' },
];

const ScoutingScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { fieldId } = route.params || {};
  
  const [isLoading, setIsLoading] = useState(true);
  const [field, setField] = useState(null);
  const [observationType, setObservationType] = useState(null);
  const [severityLevel, setSeverityLevel] = useState(null);
  const [notes, setNotes] = useState('');
  const [images, setImages] = useState([]);
  const [location, setLocation] = useState(null);
  const [errorMsg, setErrorMsg] = useState(null);
  
  // Load field data and request permissions
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        
        // Find field by ID
        if (fieldId) {
          const foundField = MOCK_FIELDS.find(f => f.id === fieldId);
          if (foundField) {
            setField(foundField);
          }
        }
        
        // Request location permissions
        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          setErrorMsg('Permission to access location was denied');
        } else {
          // Get current location
          let location = await Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.Highest,
          });
          setLocation(location);
        }
        
        // Request camera permissions
        const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
        if (cameraPermission.status !== 'granted') {
          setErrorMsg('Permission to access camera was denied');
        }
        
        // Request media library permissions
        const mediaLibraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (mediaLibraryPermission.status !== 'granted') {
          setErrorMsg('Permission to access media library was denied');
        }
      } catch (error) {
        console.error('Error loading data:', error);
        setErrorMsg('Error loading data');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, [fieldId]);
  
  // Handle back button press
  const handleBackPress = () => {
    navigation.goBack();
  };
  
  // Handle field selection
  const handleFieldSelect = (selectedField) => {
    setField(selectedField);
  };
  
  // Handle observation type selection
  const handleObservationTypeSelect = (type) => {
    setObservationType(type);
  };
  
  // Handle severity level selection
  const handleSeverityLevelSelect = (level) => {
    setSeverityLevel(level);
  };
  
  // Handle taking a photo
  const handleTakePhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImages([...images, result.assets[0].uri]);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };
  
  // Handle selecting a photo from the library
  const handleSelectPhoto = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImages([...images, result.assets[0].uri]);
      }
    } catch (error) {
      console.error('Error selecting photo:', error);
      Alert.alert('Error', 'Failed to select photo');
    }
  };
  
  // Handle removing a photo
  const handleRemovePhoto = (index) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };
  
  // Handle saving the observation
  const handleSaveObservation = () => {
    // Validate required fields
    if (!field) {
      Alert.alert('Error', 'Please select a field');
      return;
    }
    
    if (!observationType) {
      Alert.alert('Error', 'Please select an observation type');
      return;
    }
    
    if (!severityLevel) {
      Alert.alert('Error', 'Please select a severity level');
      return;
    }
    
    if (notes.trim() === '') {
      Alert.alert('Error', 'Please enter observation notes');
      return;
    }
    
    // In a real app, this would save the observation to the database
    Alert.alert(
      'Save Observation',
      'Observation saved successfully',
      [
        {
          text: 'OK',
          onPress: () => {
            // Reset form and navigate back
            setObservationType(null);
            setSeverityLevel(null);
            setNotes('');
            setImages([]);
            navigation.goBack();
          },
        },
      ]
    );
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading...</Text>
      </SafeAreaView>
    );
  }
  
  // Render error state
  if (errorMsg && !field) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={64} color="#FF4D4F" />
        <Text style={styles.errorText}>{errorMsg}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => navigation.goBack()}>
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Field Scouting</Text>
        <View style={{ width: 24 }} />
      </View>
      
      <ScrollView style={styles.scrollView}>
        {/* Field Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Field</Text>
          
          {field ? (
            <View style={styles.selectedFieldContainer}>
              <View style={styles.selectedField}>
                <Ionicons name="location" size={24} color="#007AFF" />
                <View style={styles.selectedFieldInfo}>
                  <Text style={styles.selectedFieldName}>{field.name}</Text>
                  <Text style={styles.selectedFieldDetails}>{field.crop} • {field.area}</Text>
                </View>
              </View>
              
              <TouchableOpacity
                style={styles.changeButton}
                onPress={() => setField(null)}
              >
                <Text style={styles.changeButtonText}>Change</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.fieldsContainer}
            >
              {MOCK_FIELDS.map((fieldItem) => (
                <TouchableOpacity
                  key={fieldItem.id}
                  style={styles.fieldItem}
                  onPress={() => handleFieldSelect(fieldItem)}
                >
                  <Ionicons name="location" size={24} color="#007AFF" />
                  <Text style={styles.fieldItemName}>{fieldItem.name}</Text>
                  <Text style={styles.fieldItemDetails}>{fieldItem.crop}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </View>
        
        {/* Observation Type */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Observation Type</Text>
          
          <View style={styles.observationTypesContainer}>
            {OBSERVATION_TYPES.map((type) => (
              <TouchableOpacity
                key={type.id}
                style={[
                  styles.observationTypeItem,
                  observationType?.id === type.id && styles.selectedObservationType,
                ]}
                onPress={() => handleObservationTypeSelect(type)}
              >
                <Ionicons
                  name={type.icon}
                  size={24}
                  color={observationType?.id === type.id ? 'white' : '#333'}
                />
                <Text
                  style={[
                    styles.observationTypeName,
                    observationType?.id === type.id && styles.selectedObservationTypeName,
                  ]}
                >
                  {type.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Severity Level */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Severity Level</Text>
          
          <View style={styles.severityLevelsContainer}>
            {SEVERITY_LEVELS.map((level) => (
              <TouchableOpacity
                key={level.id}
                style={[
                  styles.severityLevelItem,
                  severityLevel?.id === level.id && styles.selectedSeverityLevel,
                  severityLevel?.id === level.id && { borderColor: level.color },
                ]}
                onPress={() => handleSeverityLevelSelect(level)}
              >
                <View
                  style={[
                    styles.severityLevelIndicator,
                    { backgroundColor: level.color },
                  ]}
                />
                <Text
                  style={[
                    styles.severityLevelName,
                    severityLevel?.id === level.id && styles.selectedSeverityLevelName,
                  ]}
                >
                  {level.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {/* Notes */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notes</Text>
          
          <TextInput
            style={styles.notesInput}
            placeholder="Enter observation details..."
            value={notes}
            onChangeText={setNotes}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
        
        {/* Photos */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Photos</Text>
          
          <View style={styles.photosContainer}>
            {images.map((uri, index) => (
              <View key={index} style={[styles.photoItem, index % 2 === 1 ? styles.photoItemRight : null]}>
                <Image source={{ uri }} style={styles.photoImage} />
                <TouchableOpacity
                  style={styles.removePhotoButton}
                  onPress={() => handleRemovePhoto(index)}
                >
                  <Ionicons name="close-circle" size={24} color="#FF4D4F" />
                </TouchableOpacity>
              </View>
            ))}
            
            <View style={styles.photoActions}>
              <TouchableOpacity
                style={styles.photoActionButton}
                onPress={handleTakePhoto}
              >
                <Ionicons name="camera" size={24} color="#007AFF" />
                <Text style={styles.photoActionText}>Take Photo</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.photoActionButton}
                onPress={handleSelectPhoto}
              >
                <Ionicons name="images" size={24} color="#007AFF" />
                <Text style={styles.photoActionText}>Select Photo</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
        
        {/* Location */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location</Text>
          
          <View style={styles.locationContainer}>
            {location ? (
              <View style={styles.locationInfo}>
                <Ionicons name="location" size={24} color="#007AFF" />
                <View style={styles.locationDetails}>
                  <Text style={styles.locationCoordinates}>
                    Lat: {location.coords.latitude.toFixed(6)}, Lon: {location.coords.longitude.toFixed(6)}
                  </Text>
                  <Text style={styles.locationAccuracy}>
                    Accuracy: ±{location.coords.accuracy.toFixed(1)} meters
                  </Text>
                </View>
              </View>
            ) : (
              <View style={styles.locationError}>
                <Ionicons name="warning" size={24} color="#FAAD14" />
                <Text style={styles.locationErrorText}>
                  {errorMsg || 'Unable to get current location'}
                </Text>
              </View>
            )}
          </View>
        </View>
        
        {/* Save Button */}
        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSaveObservation}
        >
          <Text style={styles.saveButtonText}>Save Observation</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    padding: 16,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  fieldsContainer: {
    flexDirection: 'row',
  },
  fieldItem: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 100,
    height: 100,
    borderRadius: 12,
    backgroundColor: '#F0F0F0',
    marginRight: 12,
    padding: 8,
  },
  fieldItemName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 8,
    textAlign: 'center',
  },
  fieldItemDetails: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
    textAlign: 'center',
  },
  selectedFieldContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedField: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectedFieldInfo: {
    marginLeft: 12,
    flex: 1,
  },
  selectedFieldName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  selectedFieldDetails: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  changeButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
  },
  changeButtonText: {
    fontSize: 12,
    color: '#333',
  },
  observationTypesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  observationTypeItem: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '23%',
    aspectRatio: 1,
    borderRadius: 12,
    backgroundColor: '#F0F0F0',
    marginBottom: 8,
    padding: 8,
  },
  selectedObservationType: {
    backgroundColor: '#007AFF',
  },
  observationTypeName: {
    fontSize: 12,
    color: '#333',
    marginTop: 8,
    textAlign: 'center',
  },
  selectedObservationTypeName: {
    color: 'white',
  },
  severityLevelsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  severityLevelItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '30%',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: 'white',
  },
  selectedSeverityLevel: {
    backgroundColor: '#F0F0F0',
    borderWidth: 2,
  },
  severityLevelIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  severityLevelName: {
    fontSize: 14,
    color: '#333',
  },
  selectedSeverityLevelName: {
    fontWeight: 'bold',
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#333',
    backgroundColor: 'white',
    minHeight: 100,
  },
  photosContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  photoItem: {
    width: '48%',
    aspectRatio: 1,
    borderRadius: 8,
    marginBottom: 8,
    marginRight: '4%',
    position: 'relative',
  },
  photoItemRight: {
    marginRight: 0,
  },
  photoImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  removePhotoButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  photoActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    width: '100%',
  },
  photoActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '48%',
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#F0F0F0',
  },
  photoActionText: {
    fontSize: 14,
    color: '#007AFF',
    marginLeft: 8,
  },
  locationContainer: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    backgroundColor: 'white',
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationDetails: {
    marginLeft: 12,
    flex: 1,
  },
  locationCoordinates: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  locationAccuracy: {
    fontSize: 12,
    color: '#666',
  },
  locationError: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
  },
  locationErrorText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  saveButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 24,
    marginBottom: 32,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
  },
});

export default ScoutingScreen;