import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { <PERSON>, Button } from 'shared';

/**
 * Field Detail screen for the Field Operations App
 * Displays detailed information about a field, including boundaries, crop info, and operations history
 */
const FieldDetailScreen = ({ route, navigation }) => {
  const { fieldId } = route.params;
  const [activeTab, setActiveTab] = useState('overview');

  // Placeholder data - would be fetched from an API in a real app
  const field = {
    id: fieldId,
    name: 'North Field',
    crop: 'Corn',
    acres: 120,
    status: 'active',
    lastActivity: '2023-06-20',
    needsAttention: false,
    soilType: 'Loam',
    plantingDate: '2023-04-15',
    expectedHarvest: '2023-09-30',
    cropVariety: 'Pioneer P1234',
    seedRate: '32,000 seeds/acre',
    notes: 'Field is performing well. Some minor weed pressure in the northwest corner that should be addressed in the next week.',
    boundaries: {
      type: 'Polygon',
      coordinates: [
        [
          [-95.123456, 41.123456],
          [-95.123456, 41.234567],
          [-95.234567, 41.234567],
          [-95.234567, 41.123456],
          [-95.123456, 41.123456]
        ]
      ]
    },
    operations: [
      {
        id: '1',
        type: 'Planting',
        date: '2023-04-15',
        operator: 'John Smith',
        equipment: 'John Deere 8R Tractor with 24-row planter',
        notes: 'Completed planting in good conditions. No issues.'
      },
      {
        id: '2',
        type: 'Fertilizer Application',
        date: '2023-05-10',
        operator: 'Mike Johnson',
        equipment: 'Case IH Patriot 4440 Sprayer',
        notes: 'Applied nitrogen at 180 lbs/acre.'
      },
      {
        id: '3',
        type: 'Herbicide Application',
        date: '2023-06-01',
        operator: 'Mike Johnson',
        equipment: 'Case IH Patriot 4440 Sprayer',
        notes: 'Applied post-emergence herbicide for broadleaf weed control.'
      }
    ],
    observations: [
      {
        id: '1',
        date: '2023-05-20',
        type: 'Crop Growth',
        notes: 'Corn at V4 stage. Good stand establishment.',
        images: ['https://example.com/image1.jpg']
      },
      {
        id: '2',
        date: '2023-06-10',
        type: 'Pest Scouting',
        notes: 'Found some corn rootworm activity in the southeast corner. Monitoring situation.',
        images: ['https://example.com/image2.jpg']
      }
    ]
  };

  // Render the overview tab
  const renderOverview = () => (
    <View>
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>Field Information</Text>
        <View style={styles.fieldDetail}>
          <Text style={styles.fieldDetailLabel}>Crop:</Text>
          <Text style={styles.fieldDetailValue}>{field.crop}</Text>
        </View>
        <View style={styles.fieldDetail}>
          <Text style={styles.fieldDetailLabel}>Acres:</Text>
          <Text style={styles.fieldDetailValue}>{field.acres}</Text>
        </View>
        <View style={styles.fieldDetail}>
          <Text style={styles.fieldDetailLabel}>Status:</Text>
          <Text style={[
            styles.fieldDetailValue,
            field.status === 'active' ? styles.activeStatus : styles.inactiveStatus
          ]}>
            {field.status.charAt(0).toUpperCase() + field.status.slice(1)}
          </Text>
        </View>
        <View style={styles.fieldDetail}>
          <Text style={styles.fieldDetailLabel}>Soil Type:</Text>
          <Text style={styles.fieldDetailValue}>{field.soilType}</Text>
        </View>
        <View style={styles.fieldDetail}>
          <Text style={styles.fieldDetailLabel}>Last Activity:</Text>
          <Text style={styles.fieldDetailValue}>{new Date(field.lastActivity).toLocaleDateString()}</Text>
        </View>
      </Card>

      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>Crop Information</Text>
        <View style={styles.fieldDetail}>
          <Text style={styles.fieldDetailLabel}>Variety:</Text>
          <Text style={styles.fieldDetailValue}>{field.cropVariety}</Text>
        </View>
        <View style={styles.fieldDetail}>
          <Text style={styles.fieldDetailLabel}>Planting Date:</Text>
          <Text style={styles.fieldDetailValue}>{new Date(field.plantingDate).toLocaleDateString()}</Text>
        </View>
        <View style={styles.fieldDetail}>
          <Text style={styles.fieldDetailLabel}>Expected Harvest:</Text>
          <Text style={styles.fieldDetailValue}>{new Date(field.expectedHarvest).toLocaleDateString()}</Text>
        </View>
        <View style={styles.fieldDetail}>
          <Text style={styles.fieldDetailLabel}>Seed Rate:</Text>
          <Text style={styles.fieldDetailValue}>{field.seedRate}</Text>
        </View>
      </Card>

      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>Notes</Text>
        <Text style={styles.notesText}>{field.notes}</Text>
      </Card>
    </View>
  );

  // Render the map tab
  const renderMap = () => (
    <View>
      <Card style={styles.mapCard}>
        <Text style={styles.sectionTitle}>Field Boundaries</Text>
        <View style={styles.mapPlaceholder}>
          <Text style={styles.mapPlaceholderText}>Map View</Text>
          <Text style={styles.mapPlaceholderSubtext}>Field boundaries would be displayed here using React Native Maps</Text>
        </View>
        <View style={styles.mapActions}>
          <Button
            title="View Full Map"
            onPress={() => navigation.navigate('FieldMap', { fieldId })}
            variant="secondary"
            style={styles.mapActionButton}
          />
          <Button
            title="Edit Boundaries"
            onPress={() => navigation.navigate('EditBoundaries', { fieldId })}
            variant="secondary"
            style={styles.mapActionButton}
          />
        </View>
      </Card>
    </View>
  );

  // Render the operations tab
  const renderOperations = () => (
    <View>
      <Card style={styles.sectionCard}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Field Operations</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => navigation.navigate('AddOperation', { fieldId })}
          >
            <Ionicons name="add-circle" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>
        {field.operations.map(operation => (
          <View key={operation.id} style={styles.operationItem}>
            <View style={styles.operationHeader}>
              <Text style={styles.operationType}>{operation.type}</Text>
              <Text style={styles.operationDate}>{new Date(operation.date).toLocaleDateString()}</Text>
            </View>
            <View style={styles.operationDetail}>
              <Text style={styles.operationDetailLabel}>Operator:</Text>
              <Text style={styles.operationDetailValue}>{operation.operator}</Text>
            </View>
            <View style={styles.operationDetail}>
              <Text style={styles.operationDetailLabel}>Equipment:</Text>
              <Text style={styles.operationDetailValue}>{operation.equipment}</Text>
            </View>
            <View style={styles.operationDetail}>
              <Text style={styles.operationDetailLabel}>Notes:</Text>
              <Text style={styles.operationDetailValue}>{operation.notes}</Text>
            </View>
          </View>
        ))}
      </Card>
    </View>
  );

  // Render the observations tab
  const renderObservations = () => (
    <View>
      <Card style={styles.sectionCard}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Field Observations</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => navigation.navigate('AddObservation', { fieldId })}
          >
            <Ionicons name="add-circle" size={24} color="#007AFF" />
          </TouchableOpacity>
        </View>
        {field.observations.map(observation => (
          <View key={observation.id} style={styles.observationItem}>
            <View style={styles.observationHeader}>
              <Text style={styles.observationType}>{observation.type}</Text>
              <Text style={styles.observationDate}>{new Date(observation.date).toLocaleDateString()}</Text>
            </View>
            <Text style={styles.observationNotes}>{observation.notes}</Text>
            {observation.images && observation.images.length > 0 && (
              <View style={styles.observationImages}>
                {observation.images.map((image, index) => (
                  <View key={index} style={styles.observationImageContainer}>
                    <Image
                      source={{ uri: image }}
                      style={styles.observationImage}
                      // No defaultSource to avoid requiring a non-existent image
                      onError={() => console.log(`Failed to load image: ${image}`)}
                    />
                  </View>
                ))}
              </View>
            )}
          </View>
        ))}
      </Card>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.title}>{field.name}</Text>
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => navigation.navigate('EditField', { fieldId })}
        >
          <Ionicons name="create-outline" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
          onPress={() => setActiveTab('overview')}
        >
          <Text style={[styles.tabText, activeTab === 'overview' && styles.activeTabText]}>Overview</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'map' && styles.activeTab]}
          onPress={() => setActiveTab('map')}
        >
          <Text style={[styles.tabText, activeTab === 'map' && styles.activeTabText]}>Map</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'operations' && styles.activeTab]}
          onPress={() => setActiveTab('operations')}
        >
          <Text style={[styles.tabText, activeTab === 'operations' && styles.activeTabText]}>Operations</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'observations' && styles.activeTab]}
          onPress={() => setActiveTab('observations')}
        >
          <Text style={[styles.tabText, activeTab === 'observations' && styles.activeTabText]}>Observations</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'map' && renderMap()}
        {activeTab === 'operations' && renderOperations()}
        {activeTab === 'observations' && renderObservations()}
      </ScrollView>

      <View style={styles.actionBar}>
        <Button
          title="Start Operation"
          onPress={() => navigation.navigate('FieldOperation', { fieldId })}
          variant="primary"
          style={styles.actionButton}
        />
        <Button
          title="Field Scouting"
          onPress={() => navigation.navigate('FieldScouting', { fieldId })}
          variant="secondary"
          style={styles.actionButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    backgroundColor: '#fff',
  },
  backButton: {
    padding: 4,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  editButton: {
    padding: 4,
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionCard: {
    marginBottom: 16,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  fieldDetail: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  fieldDetailLabel: {
    width: 120,
    fontSize: 14,
    color: '#666',
  },
  fieldDetailValue: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  activeStatus: {
    color: '#34C759',
  },
  inactiveStatus: {
    color: '#8E8E93',
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
  mapCard: {
    marginBottom: 16,
    padding: 16,
  },
  mapPlaceholder: {
    height: 200,
    backgroundColor: '#E5E5EA',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  mapPlaceholderText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#8E8E93',
  },
  mapPlaceholderSubtext: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 32,
  },
  mapActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  mapActionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  addButton: {
    padding: 4,
  },
  operationItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    paddingBottom: 16,
    marginBottom: 16,
  },
  operationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  operationType: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  operationDate: {
    fontSize: 14,
    color: '#666',
  },
  operationDetail: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  operationDetailLabel: {
    width: 80,
    fontSize: 14,
    color: '#666',
  },
  operationDetailValue: {
    flex: 1,
    fontSize: 14,
  },
  observationItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    paddingBottom: 16,
    marginBottom: 16,
  },
  observationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  observationType: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  observationDate: {
    fontSize: 14,
    color: '#666',
  },
  observationNotes: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  observationImages: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  observationImageContainer: {
    width: '48%',
    aspectRatio: 1,
    marginRight: '2%',
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#E5E5EA',
  },
  observationImage: {
    width: '100%',
    height: '100%',
  },
  actionBar: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    backgroundColor: '#fff',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default FieldDetailScreen;
