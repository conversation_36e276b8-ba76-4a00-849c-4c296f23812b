{"expo": {"name": "NxtAcre Field Operations", "slug": "nxtacre-field-operations", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.nxtacre.fieldoperations", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app uses your location to track field operations and provide navigation assistance.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses your location to track field operations and provide navigation assistance even when the app is in the background.", "NSCameraUsageDescription": "This app uses the camera to capture field observations and scan barcodes.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to upload field images."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.nxtacre.fieldoperations", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow NxtAcre Field Operations to use your location to track field operations and provide navigation assistance."}], ["expo-camera", {"cameraPermission": "Allow NxtAcre Field Operations to access your camera to capture field observations and scan barcodes."}], ["expo-image-picker", {"photosPermission": "Allow NxtAcre Field Operations to access your photos to upload field images."}]], "extra": {"eas": {"projectId": "nxtacre-field-operations"}}}}