import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

type SafetyMetric = {
  id: string;
  title: string;
  value: string | number;
  change: number;
  icon: string;
  color: string;
};

type SafetyAlert = {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  date: string;
};

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<SafetyMetric[]>([]);
  const [alerts, setAlerts] = useState<SafetyAlert[]>([]);

  useEffect(() => {
    // Simulate fetching data
    setTimeout(() => {
      setMetrics([
        {
          id: '1',
          title: 'Days Without Incident',
          value: 45,
          change: 5,
          icon: 'calendar',
          color: '#4CAF50',
        },
        {
          id: '2',
          title: 'Open Safety Issues',
          value: 3,
          change: -2,
          icon: 'warning',
          color: '#FF9800',
        },
        {
          id: '3',
          title: 'Training Compliance',
          value: '92%',
          change: 4,
          icon: 'school',
          color: '#2196F3',
        },
        {
          id: '4',
          title: 'Inspections Due',
          value: 2,
          change: 0,
          icon: 'clipboard',
          color: '#9C27B0',
        },
      ]);

      setAlerts([
        {
          id: '1',
          title: 'Equipment Inspection Due',
          description: 'Tractor #5 requires safety inspection by July 30',
          priority: 'high',
          date: '2024-07-25',
        },
        {
          id: '2',
          title: 'Training Expiring',
          description: 'Chemical handling certification for 3 employees expires next week',
          priority: 'medium',
          date: '2024-07-26',
        },
        {
          id: '3',
          title: 'Weather Alert',
          description: 'Severe thunderstorm warning for tomorrow afternoon',
          priority: 'high',
          date: '2024-07-27',
        },
      ]);

      setLoading(false);
    }, 1500);
  }, []);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return '#F44336';
      case 'medium':
        return '#FF9800';
      case 'low':
        return '#4CAF50';
      default:
        return '#757575';
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={styles.loadingText}>Loading safety dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Safety Dashboard</Text>
        <TouchableOpacity onPress={() => navigation.navigate('Profile' as never)}>
          <Ionicons name="person-circle-outline" size={30} color="#4CAF50" />
        </TouchableOpacity>
      </View>

      <Text style={styles.sectionTitle}>Safety Metrics</Text>
      <View style={styles.metricsContainer}>
        {metrics.map((metric) => (
          <View key={metric.id} style={styles.metricCard}>
            <Ionicons name={metric.icon as any} size={24} color={metric.color} />
            <Text style={styles.metricTitle}>{metric.title}</Text>
            <Text style={styles.metricValue}>{metric.value}</Text>
            <View style={styles.metricChange}>
              <Ionicons
                name={metric.change > 0 ? 'arrow-up' : metric.change < 0 ? 'arrow-down' : 'remove'}
                size={16}
                color={metric.change > 0 ? '#4CAF50' : metric.change < 0 ? '#F44336' : '#757575'}
              />
              <Text
                style={[
                  styles.metricChangeText,
                  {
                    color:
                      metric.change > 0 ? '#4CAF50' : metric.change < 0 ? '#F44336' : '#757575',
                  },
                ]}
              >
                {Math.abs(metric.change)}
              </Text>
            </View>
          </View>
        ))}
      </View>

      <Text style={styles.sectionTitle}>Safety Alerts</Text>
      <View style={styles.alertsContainer}>
        {alerts.map((alert) => (
          <TouchableOpacity key={alert.id} style={styles.alertCard}>
            <View
              style={[
                styles.priorityIndicator,
                { backgroundColor: getPriorityColor(alert.priority) },
              ]}
            />
            <View style={styles.alertContent}>
              <Text style={styles.alertTitle}>{alert.title}</Text>
              <Text style={styles.alertDescription}>{alert.description}</Text>
              <Text style={styles.alertDate}>{alert.date}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#757575" />
          </TouchableOpacity>
        ))}
      </View>

      <Text style={styles.sectionTitle}>Quick Actions</Text>
      <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={() => navigation.navigate('Incidents' as never)}>
          <Ionicons name="warning" size={24} color="white" />
          <Text style={styles.actionText}>Report Incident</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => navigation.navigate('Inspections' as never)}>
          <Ionicons name="clipboard" size={24} color="white" />
          <Text style={styles.actionText}>Start Inspection</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={() => navigation.navigate('Emergency' as never)}>
          <Ionicons name="alert-circle" size={24} color="white" />
          <Text style={styles.actionText}>Emergency Procedures</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#4CAF50',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    color: '#333',
  },
  metricsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    width: '48%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  metricTitle: {
    fontSize: 14,
    color: '#757575',
    marginTop: 8,
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  metricChange: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  metricChangeText: {
    fontSize: 12,
    marginLeft: 2,
  },
  alertsContainer: {
    marginBottom: 20,
  },
  alertCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  priorityIndicator: {
    width: 4,
    height: '100%',
  },
  alertContent: {
    flex: 1,
    padding: 16,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  alertDescription: {
    fontSize: 14,
    color: '#757575',
    marginTop: 4,
  },
  alertDate: {
    fontSize: 12,
    color: '#9E9E9E',
    marginTop: 4,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  actionButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    width: '30%',
  },
  actionText: {
    color: 'white',
    marginTop: 8,
    fontSize: 12,
    textAlign: 'center',
  },
});

export default HomeScreen;