import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { View, Text, ActivityIndicator } from 'react-native';

// Import screens
import { HomeScreen } from './src/screens';
// import TrainingScreen from './src/screens/TrainingScreen';
// import IncidentsScreen from './src/screens/IncidentsScreen';
// import InspectionsScreen from './src/screens/InspectionsScreen';
// import EmergencyScreen from './src/screens/EmergencyScreen';
// import ProfileScreen from './src/screens/ProfileScreen';

// Placeholder components until actual screens are implemented

const TrainingScreen = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text>Training Screen</Text>
    <Text>Safety training modules and tracking</Text>
  </View>
);

const IncidentsScreen = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text>Incidents Screen</Text>
    <Text>Incident reporting and documentation</Text>
  </View>
);

const InspectionsScreen = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text>Inspections Screen</Text>
    <Text>Safety inspection checklists</Text>
  </View>
);

const EmergencyScreen = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text>Emergency Screen</Text>
    <Text>Emergency procedures and contacts</Text>
  </View>
);

const ProfileScreen = () => (
  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
    <Text>Profile Screen</Text>
    <Text>User profile and settings</Text>
  </View>
);

// Create the tab navigator
const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

// Tab navigator component
function TabNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Training') {
            iconName = focused ? 'school' : 'school-outline';
          } else if (route.name === 'Incidents') {
            iconName = focused ? 'warning' : 'warning-outline';
          } else if (route.name === 'Inspections') {
            iconName = focused ? 'clipboard' : 'clipboard-outline';
          } else if (route.name === 'Emergency') {
            iconName = focused ? 'alert-circle' : 'alert-circle-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#4CAF50',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Training" component={TrainingScreen} />
      <Tab.Screen name="Incidents" component={IncidentsScreen} />
      <Tab.Screen name="Inspections" component={InspectionsScreen} />
      <Tab.Screen name="Emergency" component={EmergencyScreen} />
    </Tab.Navigator>
  );
}

// Main app component
export default function App() {
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading resources
  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  }, []);

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#4CAF50" />
        <Text style={{ marginTop: 10 }}>Loading Farm Safety App...</Text>
      </View>
    );
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <Stack.Navigator>
          <Stack.Screen 
            name="Main" 
            component={TabNavigator} 
            options={{ headerShown: false }} 
          />
          <Stack.Screen name="Profile" component={ProfileScreen} />
          {/* Add more stack screens as needed */}
        </Stack.Navigator>
      </NavigationContainer>
      <StatusBar style="auto" />
    </SafeAreaProvider>
  );
}
