{"name": "farm-safety-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"expo": "^53.0.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.2", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "expo-font": "~11.10.2", "expo-asset": "~9.0.2", "expo-constants": "~15.4.5", "expo-linking": "~6.2.2", "expo-notifications": "~0.27.6", "expo-camera": "~14.0.5", "expo-document-picker": "~11.10.1", "expo-file-system": "~16.0.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "typescript": "^5.1.3"}, "private": true}