// metro.config.js
const { getDefaultConfig } = require('@expo/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

module.exports = {
  ...defaultConfig,
  // Add any custom configuration here if needed
  resolver: {
    ...defaultConfig.resolver,
    // Ensure Metro can resolve all required modules
    extraNodeModules: {
      ...defaultConfig.resolver.extraNodeModules,
    },
  },
  transformer: {
    ...defaultConfig.transformer,
    // Add any custom transformer options if needed
  },
};