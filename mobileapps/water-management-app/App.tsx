import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Loading } from 'shared';

// Import screens from src/screens
import { 
  HomeScreen, 
  IrrigationScreen, 
  MonitoringScreen, 
  ReportsScreen, 
  SettingsScreen,
  IrrigationDetailScreen,
  SensorDetailScreen,
  WaterUsageScreen,
  ConservationScreen
} from './src/screens';

// Create navigation stacks and tabs
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Main tab navigator
const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Irrigation') {
            iconName = focused ? 'water' : 'water-outline';
          } else if (route.name === 'Monitoring') {
            iconName = focused ? 'analytics' : 'analytics-outline';
          } else if (route.name === 'Reports') {
            iconName = focused ? 'document-text' : 'document-text-outline';
          } else if (route.name === 'Settings') {
            iconName = focused ? 'settings' : 'settings-outline';
          }

          // You can return any component here
          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#0096FF',
        tabBarInactiveTintColor: 'gray',
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Irrigation" component={IrrigationScreen} />
      <Tab.Screen name="Monitoring" component={MonitoringScreen} />
      <Tab.Screen name="Reports" component={ReportsScreen} />
      <Tab.Screen name="Settings" component={SettingsScreen} />
    </Tab.Navigator>
  );
};

// Main app component
export default function App() {
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading app resources
  useEffect(() => {
    const loadResources = async () => {
      try {
        // Load any resources, check authentication, etc.
        // For now, just simulate a delay
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error('Error loading resources:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadResources();
  }, []);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="Main" component={TabNavigator} />
          <Stack.Screen name="IrrigationDetail" component={IrrigationDetailScreen} />
          <Stack.Screen name="SensorDetail" component={SensorDetailScreen} />
          <Stack.Screen name="WaterUsage" component={WaterUsageScreen} />
          <Stack.Screen name="Conservation" component={ConservationScreen} />
        </Stack.Navigator>
      </NavigationContainer>
      <StatusBar style="auto" />
    </SafeAreaProvider>
  );
}