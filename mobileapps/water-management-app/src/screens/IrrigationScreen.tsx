import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Card, Loading } from 'shared';
import { useNavigation } from '@react-navigation/native';

/**
 * Irrigation screen for the Water Management App
 * Displays a list of irrigation systems and allows the user to manage them
 */
const IrrigationScreen = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [irrigationSystems, setIrrigationSystems] = useState([]);
  const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'active', 'inactive'

  // Load irrigation systems data
  useEffect(() => {
    loadIrrigationSystems();
  }, []);

  // Function to load irrigation systems data
  const loadIrrigationSystems = async () => {
    try {
      setIsLoading(true);
      // In a real app, this would fetch data from an API
      // For now, we'll use mock data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      setIrrigationSystems([
        {
          id: '1',
          name: 'North Field Pivot',
          type: 'pivot',
          status: 'active',
          lastActive: new Date(Date.now() - 3600000), // 1 hour ago
          waterUsage: 2500, // gallons
          batteryLevel: 85, // percentage
          coverage: 120, // acres
          nextScheduled: new Date(Date.now() + 86400000), // tomorrow
          alerts: 0
        },
        {
          id: '2',
          name: 'South Field Drip',
          type: 'drip',
          status: 'active',
          lastActive: new Date(Date.now() - 7200000), // 2 hours ago
          waterUsage: 1200, // gallons
          batteryLevel: 72, // percentage
          coverage: 45, // acres
          nextScheduled: new Date(Date.now() + 172800000), // 2 days from now
          alerts: 0
        },
        {
          id: '3',
          name: 'East Field Sprinklers',
          type: 'sprinkler',
          status: 'inactive',
          lastActive: new Date(Date.now() - 259200000), // 3 days ago
          waterUsage: 0, // gallons
          batteryLevel: 64, // percentage
          coverage: 30, // acres
          nextScheduled: null,
          alerts: 1
        },
        {
          id: '4',
          name: 'West Field Pivot',
          type: 'pivot',
          status: 'active',
          lastActive: new Date(Date.now() - 1800000), // 30 minutes ago
          waterUsage: 3200, // gallons
          batteryLevel: 92, // percentage
          coverage: 150, // acres
          nextScheduled: new Date(Date.now() + 43200000), // 12 hours from now
          alerts: 0
        },
        {
          id: '5',
          name: 'Greenhouse Drip',
          type: 'drip',
          status: 'inactive',
          lastActive: new Date(Date.now() - 432000000), // 5 days ago
          waterUsage: 0, // gallons
          batteryLevel: 45, // percentage
          coverage: 2, // acres
          nextScheduled: null,
          alerts: 2
        }
      ]);
    } catch (error) {
      console.error('Error loading irrigation systems:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await loadIrrigationSystems();
    setRefreshing(false);
  };

  // Function to format date
  const formatDate = (date) => {
    if (!date) return 'Not scheduled';
    
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (diffDays > 0) {
      return `In ${diffDays} day${diffDays > 1 ? 's' : ''}`;
    } else if (diffHours > 0) {
      return `In ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
    } else {
      return 'Soon';
    }
  };

  // Function to format time ago
  const formatTimeAgo = (date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    }
  };

  // Function to navigate to irrigation detail screen
  const navigateToIrrigationDetail = (system) => {
    navigation.navigate('IrrigationDetail', { system });
  };

  // Function to filter systems by status
  const getFilteredSystems = () => {
    if (filterStatus === 'all') {
      return irrigationSystems;
    }
    return irrigationSystems.filter(system => system.status === filterStatus);
  };

  // Function to render system type icon
  const renderSystemTypeIcon = (type) => {
    switch (type) {
      case 'pivot':
        return <Ionicons name="sync" size={24} color="#0096FF" />;
      case 'drip':
        return <Ionicons name="water" size={24} color="#0096FF" />;
      case 'sprinkler':
        return <Ionicons name="umbrella" size={24} color="#0096FF" />;
      default:
        return <Ionicons name="water" size={24} color="#0096FF" />;
    }
  };

  // Function to render system item
  const renderSystemItem = ({ item }) => (
    <TouchableOpacity onPress={() => navigateToIrrigationDetail(item)}>
      <Card style={styles.systemCard}>
        <View style={styles.systemHeader}>
          <View style={styles.systemTypeIcon}>
            {renderSystemTypeIcon(item.type)}
          </View>
          <View style={styles.systemInfo}>
            <Text style={styles.systemName}>{item.name}</Text>
            <View style={styles.systemMeta}>
              <Text style={styles.systemType}>
                {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
              </Text>
              <View style={styles.statusDot} />
              <Text 
                style={[
                  styles.systemStatus,
                  item.status === 'active' ? styles.statusActive : styles.statusInactive
                ]}
              >
                {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
              </Text>
            </View>
          </View>
          {item.alerts > 0 && (
            <View style={styles.alertBadge}>
              <Text style={styles.alertBadgeText}>{item.alerts}</Text>
            </View>
          )}
        </View>
        
        <View style={styles.systemDetails}>
          <View style={styles.detailItem}>
            <Ionicons name="time-outline" size={16} color="#666" />
            <Text style={styles.detailText}>
              Last active: {formatTimeAgo(item.lastActive)}
            </Text>
          </View>
          
          <View style={styles.detailItem}>
            <Ionicons name="calendar-outline" size={16} color="#666" />
            <Text style={styles.detailText}>
              Next: {formatDate(item.nextScheduled)}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Ionicons name="water-outline" size={16} color="#666" />
              <Text style={styles.detailText}>
                {item.waterUsage > 0 ? `${item.waterUsage} gal` : 'No usage'}
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Ionicons name="battery-half-outline" size={16} color="#666" />
              <Text style={styles.detailText}>
                {item.batteryLevel}%
              </Text>
            </View>
            
            <View style={styles.detailItem}>
              <Ionicons name="map-outline" size={16} color="#666" />
              <Text style={styles.detailText}>
                {item.coverage} acres
              </Text>
            </View>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Filter tabs */}
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterTab, filterStatus === 'all' && styles.filterTabActive]}
          onPress={() => setFilterStatus('all')}
        >
          <Text 
            style={[styles.filterText, filterStatus === 'all' && styles.filterTextActive]}
          >
            All ({irrigationSystems.length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterTab, filterStatus === 'active' && styles.filterTabActive]}
          onPress={() => setFilterStatus('active')}
        >
          <Text 
            style={[styles.filterText, filterStatus === 'active' && styles.filterTextActive]}
          >
            Active ({irrigationSystems.filter(s => s.status === 'active').length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.filterTab, filterStatus === 'inactive' && styles.filterTabActive]}
          onPress={() => setFilterStatus('inactive')}
        >
          <Text 
            style={[styles.filterText, filterStatus === 'inactive' && styles.filterTextActive]}
          >
            Inactive ({irrigationSystems.filter(s => s.status === 'inactive').length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Systems list */}
      <FlatList
        data={getFilteredSystems()}
        renderItem={renderSystemItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="water-outline" size={48} color="#CCC" />
            <Text style={styles.emptyText}>No irrigation systems found</Text>
          </View>
        }
      />

      {/* Add system button */}
      <TouchableOpacity style={styles.addButton}>
        <Ionicons name="add" size={24} color="#FFF" />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  filterContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFF',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  filterTab: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  filterTabActive: {
    borderBottomColor: '#0096FF',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
  },
  filterTextActive: {
    color: '#0096FF',
    fontWeight: '500',
  },
  listContainer: {
    padding: 16,
  },
  systemCard: {
    marginBottom: 16,
    padding: 0,
    overflow: 'hidden',
  },
  systemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  systemTypeIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 150, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  systemInfo: {
    flex: 1,
  },
  systemName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  systemMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  systemType: {
    fontSize: 14,
    color: '#666',
  },
  statusDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#666',
    marginHorizontal: 6,
  },
  systemStatus: {
    fontSize: 14,
  },
  statusActive: {
    color: '#34C759',
  },
  statusInactive: {
    color: '#FF3B30',
  },
  alertBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alertBadgeText: {
    color: '#FFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  systemDetails: {
    padding: 16,
    backgroundColor: '#FAFAFA',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
  },
  addButton: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#0096FF',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
});

export default IrrigationScreen;