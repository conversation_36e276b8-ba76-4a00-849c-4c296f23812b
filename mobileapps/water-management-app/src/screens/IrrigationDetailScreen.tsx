import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Card, Loading } from 'shared';
import { useNavigation, useRoute } from '@react-navigation/native';

/**
 * Irrigation Detail screen for the Water Management App
 * Displays detailed information about a specific irrigation system and allows the user to control it
 */
const IrrigationDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [isLoading, setIsLoading] = useState(true);
  const [system, setSystem] = useState(null);
  const [isActive, setIsActive] = useState(false);
  const [waterFlow, setWaterFlow] = useState(50); // percentage
  const [schedule, setSchedule] = useState([
    { day: 'Monday', active: true, startTime: '06:00', duration: 60 },
    { day: 'Wednesday', active: true, startTime: '06:00', duration: 60 },
    { day: 'Friday', active: true, startTime: '06:00', duration: 60 },
    { day: 'Sunday', active: false, startTime: '06:00', duration: 60 }
  ]);

  // Load system data
  useEffect(() => {
    loadSystemData();
  }, []);

  // Function to load system data
  const loadSystemData = async () => {
    try {
      setIsLoading(true);
      // In a real app, this would fetch data from an API
      // For now, we'll use the system passed from the previous screen or mock data
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
      
      const systemData = route.params?.system || {
        id: '1',
        name: 'North Field Pivot',
        type: 'pivot',
        status: 'active',
        lastActive: new Date(Date.now() - 3600000), // 1 hour ago
        waterUsage: 2500, // gallons
        batteryLevel: 85, // percentage
        coverage: 120, // acres
        nextScheduled: new Date(Date.now() + 86400000), // tomorrow
        alerts: 0,
        location: 'North Field',
        installDate: new Date('2022-03-15'),
        manufacturer: 'IrriTech',
        model: 'PT-5000',
        serialNumber: 'IT5000-12345',
        maintenanceStatus: 'good', // 'good', 'needs-attention', 'requires-service'
        lastMaintenance: new Date('2023-08-10'),
        nextMaintenance: new Date('2024-02-10'),
        sensors: [
          { id: '1', type: 'soil-moisture', status: 'active', value: 42, unit: '%' },
          { id: '2', type: 'temperature', status: 'active', value: 72, unit: '°F' },
          { id: '3', type: 'flow-rate', status: 'active', value: 120, unit: 'gal/min' },
          { id: '4', type: 'pressure', status: 'active', value: 45, unit: 'psi' }
        ],
        weatherAdjustment: true,
        soilMoistureThreshold: 35, // percentage
        rainDelay: true,
        rainDelayDuration: 24, // hours
        wateringZones: [
          { id: '1', name: 'Zone 1', active: true, coverage: 40, flowRate: 40 },
          { id: '2', name: 'Zone 2', active: true, coverage: 40, flowRate: 40 },
          { id: '3', name: 'Zone 3', active: true, coverage: 40, flowRate: 40 }
        ]
      };
      
      setSystem(systemData);
      setIsActive(systemData.status === 'active');
    } catch (error) {
      console.error('Error loading system data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to format date
  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Function to format time ago
  const formatTimeAgo = (date) => {
    if (!date) return 'N/A';
    
    const now = new Date();
    const diffMs = now.getTime() - new Date(date).getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    }
  };

  // Function to toggle system active state
  const toggleSystemActive = () => {
    // In a real app, this would call an API to activate/deactivate the system
    if (!isActive) {
      Alert.alert(
        'Activate System',
        `Are you sure you want to activate ${system.name}?`,
        [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: 'Activate',
            onPress: () => {
              setIsActive(true);
              setSystem(prev => ({ ...prev, status: 'active' }));
            }
          }
        ]
      );
    } else {
      Alert.alert(
        'Deactivate System',
        `Are you sure you want to deactivate ${system.name}?`,
        [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: 'Deactivate',
            style: 'destructive',
            onPress: () => {
              setIsActive(false);
              setSystem(prev => ({ ...prev, status: 'inactive' }));
            }
          }
        ]
      );
    }
  };

  // Function to adjust water flow
  const adjustWaterFlow = (newValue) => {
    // In a real app, this would call an API to adjust the water flow
    setWaterFlow(newValue);
  };

  // Function to toggle schedule day
  const toggleScheduleDay = (index) => {
    const newSchedule = [...schedule];
    newSchedule[index].active = !newSchedule[index].active;
    setSchedule(newSchedule);
  };

  // Function to render system type icon
  const renderSystemTypeIcon = (type) => {
    switch (type) {
      case 'pivot':
        return <Ionicons name="sync" size={24} color="#0096FF" />;
      case 'drip':
        return <Ionicons name="water" size={24} color="#0096FF" />;
      case 'sprinkler':
        return <Ionicons name="umbrella" size={24} color="#0096FF" />;
      default:
        return <Ionicons name="water" size={24} color="#0096FF" />;
    }
  };

  // Function to render maintenance status
  const renderMaintenanceStatus = (status) => {
    switch (status) {
      case 'good':
        return (
          <View style={[styles.statusBadge, styles.statusGood]}>
            <Ionicons name="checkmark-circle" size={16} color="#34C759" />
            <Text style={styles.statusGoodText}>Good</Text>
          </View>
        );
      case 'needs-attention':
        return (
          <View style={[styles.statusBadge, styles.statusWarning]}>
            <Ionicons name="alert-circle" size={16} color="#FF9500" />
            <Text style={styles.statusWarningText}>Needs Attention</Text>
          </View>
        );
      case 'requires-service':
        return (
          <View style={[styles.statusBadge, styles.statusCritical]}>
            <Ionicons name="warning" size={16} color="#FF3B30" />
            <Text style={styles.statusCriticalText}>Requires Service</Text>
          </View>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="chevron-back" size={24} color="#FFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{system.name}</Text>
          <TouchableOpacity style={styles.moreButton}>
            <Ionicons name="ellipsis-vertical" size={24} color="#FFF" />
          </TouchableOpacity>
        </View>

        {/* System Status Card */}
        <Card style={styles.statusCard}>
          <View style={styles.statusHeader}>
            <View style={styles.statusIconContainer}>
              {renderSystemTypeIcon(system.type)}
            </View>
            <View style={styles.statusInfo}>
              <Text style={styles.statusTitle}>{system.type.charAt(0).toUpperCase() + system.type.slice(1)} System</Text>
              <Text style={styles.statusSubtitle}>{system.location}</Text>
            </View>
            <Switch
              value={isActive}
              onValueChange={toggleSystemActive}
              trackColor={{ false: '#E0E0E0', true: '#B3E0FF' }}
              thumbColor={isActive ? '#0096FF' : '#F5F5F5'}
              ios_backgroundColor="#E0E0E0"
            />
          </View>
          
          <View style={styles.statusDetails}>
            <View style={styles.statusDetail}>
              <Text style={styles.statusDetailLabel}>Status</Text>
              <Text 
                style={[
                  styles.statusDetailValue,
                  isActive ? styles.statusActive : styles.statusInactive
                ]}
              >
                {isActive ? 'Active' : 'Inactive'}
              </Text>
            </View>
            
            <View style={styles.statusDetail}>
              <Text style={styles.statusDetailLabel}>Last Active</Text>
              <Text style={styles.statusDetailValue}>{formatTimeAgo(system.lastActive)}</Text>
            </View>
            
            <View style={styles.statusDetail}>
              <Text style={styles.statusDetailLabel}>Water Usage</Text>
              <Text style={styles.statusDetailValue}>{system.waterUsage} gal</Text>
            </View>
            
            <View style={styles.statusDetail}>
              <Text style={styles.statusDetailLabel}>Battery</Text>
              <Text style={styles.statusDetailValue}>{system.batteryLevel}%</Text>
            </View>
          </View>
        </Card>

        {/* Water Flow Control Card */}
        <Card style={styles.controlCard}>
          <Text style={styles.controlTitle}>Water Flow Control</Text>
          
          <View style={styles.flowControl}>
            <TouchableOpacity 
              style={styles.flowButton}
              onPress={() => adjustWaterFlow(Math.max(0, waterFlow - 10))}
            >
              <Ionicons name="remove" size={24} color="#0096FF" />
            </TouchableOpacity>
            
            <View style={styles.flowGauge}>
              <View style={styles.flowGaugeBackground}>
                <View 
                  style={[
                    styles.flowGaugeFill,
                    { width: `${waterFlow}%` }
                  ]}
                />
              </View>
              <Text style={styles.flowValue}>{waterFlow}%</Text>
            </View>
            
            <TouchableOpacity 
              style={styles.flowButton}
              onPress={() => adjustWaterFlow(Math.min(100, waterFlow + 10))}
            >
              <Ionicons name="add" size={24} color="#0096FF" />
            </TouchableOpacity>
          </View>
          
          <View style={styles.flowPresets}>
            <TouchableOpacity 
              style={styles.flowPresetButton}
              onPress={() => adjustWaterFlow(25)}
            >
              <Text style={styles.flowPresetText}>25%</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.flowPresetButton}
              onPress={() => adjustWaterFlow(50)}
            >
              <Text style={styles.flowPresetText}>50%</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.flowPresetButton}
              onPress={() => adjustWaterFlow(75)}
            >
              <Text style={styles.flowPresetText}>75%</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.flowPresetButton}
              onPress={() => adjustWaterFlow(100)}
            >
              <Text style={styles.flowPresetText}>100%</Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Schedule Card */}
        <Card style={styles.scheduleCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Watering Schedule</Text>
            <TouchableOpacity style={styles.cardAction}>
              <Text style={styles.cardActionText}>Edit</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.scheduleList}>
            {schedule.map((item, index) => (
              <View key={index} style={styles.scheduleItem}>
                <View style={styles.scheduleInfo}>
                  <Text style={styles.scheduleDay}>{item.day}</Text>
                  <Text style={styles.scheduleTime}>{item.startTime} • {item.duration} min</Text>
                </View>
                <Switch
                  value={item.active}
                  onValueChange={() => toggleScheduleDay(index)}
                  trackColor={{ false: '#E0E0E0', true: '#B3E0FF' }}
                  thumbColor={item.active ? '#0096FF' : '#F5F5F5'}
                  ios_backgroundColor="#E0E0E0"
                />
              </View>
            ))}
          </View>
        </Card>

        {/* Sensor Readings Card */}
        <Card style={styles.sensorsCard}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Sensor Readings</Text>
            <TouchableOpacity 
              style={styles.cardAction}
              onPress={() => navigation.navigate('SensorDetail', { system })}
            >
              <Text style={styles.cardActionText}>View All</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.sensorGrid}>
            {system.sensors.map(sensor => (
              <View key={sensor.id} style={styles.sensorItem}>
                <View style={styles.sensorIcon}>
                  <Ionicons 
                    name={
                      sensor.type === 'soil-moisture' ? 'water' :
                      sensor.type === 'temperature' ? 'thermometer' :
                      sensor.type === 'flow-rate' ? 'speedometer' : 'pulse'
                    } 
                    size={24} 
                    color="#0096FF" 
                  />
                </View>
                <Text style={styles.sensorValue}>{sensor.value} {sensor.unit}</Text>
                <Text style={styles.sensorType}>
                  {sensor.type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                </Text>
              </View>
            ))}
          </View>
        </Card>

        {/* System Details Card */}
        <Card style={styles.detailsCard}>
          <Text style={styles.cardTitle}>System Details</Text>
          
          <View style={styles.detailsList}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Manufacturer</Text>
              <Text style={styles.detailValue}>{system.manufacturer}</Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Model</Text>
              <Text style={styles.detailValue}>{system.model}</Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Serial Number</Text>
              <Text style={styles.detailValue}>{system.serialNumber}</Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Installation Date</Text>
              <Text style={styles.detailValue}>{formatDate(system.installDate)}</Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Coverage Area</Text>
              <Text style={styles.detailValue}>{system.coverage} acres</Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Maintenance Status</Text>
              {renderMaintenanceStatus(system.maintenanceStatus)}
            </View>
            
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Last Maintenance</Text>
              <Text style={styles.detailValue}>{formatDate(system.lastMaintenance)}</Text>
            </View>
            
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Next Maintenance</Text>
              <Text style={styles.detailValue}>{formatDate(system.nextMaintenance)}</Text>
            </View>
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={[styles.actionButton, styles.actionButtonSecondary]}>
            <Ionicons name="document-text-outline" size={20} color="#0096FF" />
            <Text style={styles.actionButtonSecondaryText}>View Reports</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.actionButton, styles.actionButtonPrimary]}>
            <Ionicons name="construct-outline" size={20} color="#FFF" />
            <Text style={styles.actionButtonPrimaryText}>Request Service</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  scrollView: {
    flex: 1,
  },
  // Header Styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0096FF',
    paddingTop: 16,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFF',
    flex: 1,
    textAlign: 'center',
  },
  moreButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Status Card Styles
  statusCard: {
    margin: 16,
    padding: 16,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0, 150, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statusSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  statusDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  statusDetail: {
    width: '50%',
    marginBottom: 16,
  },
  statusDetailLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  statusDetailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  statusActive: {
    color: '#34C759',
  },
  statusInactive: {
    color: '#FF3B30',
  },
  // Control Card Styles
  controlCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  controlTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  flowControl: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  flowButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  flowGauge: {
    flex: 1,
    marginHorizontal: 16,
  },
  flowGaugeBackground: {
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  flowGaugeFill: {
    height: '100%',
    backgroundColor: '#0096FF',
    borderRadius: 8,
  },
  flowValue: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 8,
  },
  flowPresets: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  flowPresetButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
  },
  flowPresetText: {
    fontSize: 14,
    color: '#0096FF',
    fontWeight: '500',
  },
  // Schedule Card Styles
  scheduleCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  cardAction: {
    
  },
  cardActionText: {
    fontSize: 14,
    color: '#0096FF',
    fontWeight: '500',
  },
  scheduleList: {
    
  },
  scheduleItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  scheduleInfo: {
    flex: 1,
  },
  scheduleDay: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  scheduleTime: {
    fontSize: 14,
    color: '#666',
  },
  // Sensors Card Styles
  sensorsCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  sensorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  sensorItem: {
    width: '50%',
    padding: 8,
  },
  sensorIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0, 150, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  sensorValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  sensorType: {
    fontSize: 14,
    color: '#666',
  },
  // Details Card Styles
  detailsCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  detailsList: {
    marginTop: 16,
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  detailLabel: {
    fontSize: 16,
    color: '#666',
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  // Status Badge Styles
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 16,
  },
  statusGood: {
    backgroundColor: 'rgba(52, 199, 89, 0.1)',
  },
  statusWarning: {
    backgroundColor: 'rgba(255, 149, 0, 0.1)',
  },
  statusCritical: {
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
  },
  statusGoodText: {
    fontSize: 14,
    color: '#34C759',
    fontWeight: '500',
    marginLeft: 4,
  },
  statusWarningText: {
    fontSize: 14,
    color: '#FF9500',
    fontWeight: '500',
    marginLeft: 4,
  },
  statusCriticalText: {
    fontSize: 14,
    color: '#FF3B30',
    fontWeight: '500',
    marginLeft: 4,
  },
  // Action Buttons Styles
  actionButtons: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 8,
  },
  actionButtonPrimary: {
    backgroundColor: '#0096FF',
  },
  actionButtonSecondary: {
    backgroundColor: '#FFF',
    borderWidth: 1,
    borderColor: '#0096FF',
  },
  actionButtonPrimaryText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFF',
    marginLeft: 8,
  },
  actionButtonSecondaryText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0096FF',
    marginLeft: 8,
  },
});

export default IrrigationDetailScreen;