import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, FlatList } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Card, Loading } from 'shared';
import { useNavigation, useRoute } from '@react-navigation/native';

/**
 * Sensor Detail screen for the Water Management App
 * Displays detailed information about the sensors for a specific irrigation system
 */
const SensorDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [isLoading, setIsLoading] = useState(true);
  const [system, setSystem] = useState(null);
  const [sensors, setSensors] = useState([]);
  const [selectedSensor, setSelectedSensor] = useState(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState('day'); // 'day', 'week', 'month'
  const [historicalData, setHistoricalData] = useState([]);

  // Load sensor data
  useEffect(() => {
    loadSensorData();
  }, []);

  // Load historical data when sensor or time range changes
  useEffect(() => {
    if (selectedSensor) {
      loadHistoricalData();
    }
  }, [selectedSensor, selectedTimeRange]);

  // Function to load sensor data
  const loadSensorData = async () => {
    try {
      setIsLoading(true);
      // In a real app, this would fetch data from an API
      // For now, we'll use the system passed from the previous screen or mock data
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
      
      const systemData = route.params?.system || {
        id: '1',
        name: 'North Field Pivot',
        type: 'pivot',
        location: 'North Field',
        sensors: [
          { 
            id: '1', 
            type: 'soil-moisture', 
            status: 'active', 
            value: 42, 
            unit: '%',
            location: 'Zone 1',
            lastCalibration: new Date('2023-06-15'),
            batteryLevel: 85,
            signalStrength: 'good',
            manufacturer: 'SoilSense',
            model: 'SM-200',
            serialNumber: 'SS-12345',
            installDate: new Date('2022-03-15'),
            threshold: {
              min: 30,
              max: 60,
              optimal: 45
            }
          },
          { 
            id: '2', 
            type: 'temperature', 
            status: 'active', 
            value: 72, 
            unit: '°F',
            location: 'Zone 1',
            lastCalibration: new Date('2023-06-15'),
            batteryLevel: 90,
            signalStrength: 'excellent',
            manufacturer: 'TempTrack',
            model: 'TT-100',
            serialNumber: 'TT-67890',
            installDate: new Date('2022-03-15'),
            threshold: {
              min: 50,
              max: 85,
              optimal: 70
            }
          },
          { 
            id: '3', 
            type: 'flow-rate', 
            status: 'active', 
            value: 120, 
            unit: 'gal/min',
            location: 'Main Line',
            lastCalibration: new Date('2023-04-10'),
            batteryLevel: 75,
            signalStrength: 'good',
            manufacturer: 'FlowMaster',
            model: 'FM-500',
            serialNumber: 'FM-24680',
            installDate: new Date('2022-03-15'),
            threshold: {
              min: 80,
              max: 150,
              optimal: 120
            }
          },
          { 
            id: '4', 
            type: 'pressure', 
            status: 'active', 
            value: 45, 
            unit: 'psi',
            location: 'Main Line',
            lastCalibration: new Date('2023-04-10'),
            batteryLevel: 80,
            signalStrength: 'good',
            manufacturer: 'PressurePro',
            model: 'PP-200',
            serialNumber: 'PP-13579',
            installDate: new Date('2022-03-15'),
            threshold: {
              min: 35,
              max: 55,
              optimal: 45
            }
          },
          { 
            id: '5', 
            type: 'humidity', 
            status: 'warning', 
            value: 65, 
            unit: '%',
            location: 'Zone 2',
            lastCalibration: new Date('2023-06-15'),
            batteryLevel: 30,
            signalStrength: 'fair',
            manufacturer: 'HumidSense',
            model: 'HS-100',
            serialNumber: 'HS-97531',
            installDate: new Date('2022-03-15'),
            threshold: {
              min: 40,
              max: 80,
              optimal: 60
            }
          },
          { 
            id: '6', 
            type: 'ph-level', 
            status: 'inactive', 
            value: 0, 
            unit: 'pH',
            location: 'Zone 3',
            lastCalibration: new Date('2023-01-20'),
            batteryLevel: 0,
            signalStrength: 'none',
            manufacturer: 'pHMeter',
            model: 'PM-300',
            serialNumber: 'PM-86420',
            installDate: new Date('2022-03-15'),
            threshold: {
              min: 6.0,
              max: 7.5,
              optimal: 6.8
            }
          }
        ]
      };
      
      setSystem(systemData);
      setSensors(systemData.sensors);
      setSelectedSensor(systemData.sensors[0]);
    } catch (error) {
      console.error('Error loading sensor data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to load historical data
  const loadHistoricalData = async () => {
    try {
      // In a real app, this would fetch data from an API
      // For now, we'll generate mock data
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate API delay
      
      // Generate mock historical data based on sensor type and time range
      const now = new Date();
      const data = [];
      
      if (selectedTimeRange === 'day') {
        // Generate hourly data for the past 24 hours
        for (let i = 0; i < 24; i++) {
          const time = new Date(now);
          time.setHours(now.getHours() - 23 + i);
          time.setMinutes(0);
          time.setSeconds(0);
          time.setMilliseconds(0);
          
          // Generate a value based on sensor type with some randomness
          let value;
          switch (selectedSensor.type) {
            case 'soil-moisture':
              value = selectedSensor.threshold.optimal + (Math.random() * 10 - 5);
              break;
            case 'temperature':
              value = selectedSensor.threshold.optimal + (Math.random() * 8 - 4);
              break;
            case 'flow-rate':
              value = selectedSensor.threshold.optimal + (Math.random() * 20 - 10);
              break;
            case 'pressure':
              value = selectedSensor.threshold.optimal + (Math.random() * 6 - 3);
              break;
            case 'humidity':
              value = selectedSensor.threshold.optimal + (Math.random() * 10 - 5);
              break;
            case 'ph-level':
              value = selectedSensor.threshold.optimal + (Math.random() * 0.6 - 0.3);
              break;
            default:
              value = selectedSensor.value + (Math.random() * 10 - 5);
          }
          
          // Ensure value is within reasonable bounds
          value = Math.max(0, value);
          value = Number(value.toFixed(1));
          
          data.push({
            time: time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
            value,
            timestamp: time
          });
        }
      } else if (selectedTimeRange === 'week') {
        // Generate daily data for the past 7 days
        for (let i = 0; i < 7; i++) {
          const time = new Date(now);
          time.setDate(now.getDate() - 6 + i);
          time.setHours(12, 0, 0, 0);
          
          // Generate a value based on sensor type with some randomness
          let value;
          switch (selectedSensor.type) {
            case 'soil-moisture':
              value = selectedSensor.threshold.optimal + (Math.random() * 12 - 6);
              break;
            case 'temperature':
              value = selectedSensor.threshold.optimal + (Math.random() * 10 - 5);
              break;
            case 'flow-rate':
              value = selectedSensor.threshold.optimal + (Math.random() * 25 - 12.5);
              break;
            case 'pressure':
              value = selectedSensor.threshold.optimal + (Math.random() * 8 - 4);
              break;
            case 'humidity':
              value = selectedSensor.threshold.optimal + (Math.random() * 12 - 6);
              break;
            case 'ph-level':
              value = selectedSensor.threshold.optimal + (Math.random() * 0.8 - 0.4);
              break;
            default:
              value = selectedSensor.value + (Math.random() * 12 - 6);
          }
          
          // Ensure value is within reasonable bounds
          value = Math.max(0, value);
          value = Number(value.toFixed(1));
          
          data.push({
            time: time.toLocaleDateString('en-US', { weekday: 'short' }),
            value,
            timestamp: time
          });
        }
      } else if (selectedTimeRange === 'month') {
        // Generate weekly data for the past 4 weeks
        for (let i = 0; i < 4; i++) {
          const time = new Date(now);
          time.setDate(now.getDate() - 21 + (i * 7));
          time.setHours(12, 0, 0, 0);
          
          // Generate a value based on sensor type with some randomness
          let value;
          switch (selectedSensor.type) {
            case 'soil-moisture':
              value = selectedSensor.threshold.optimal + (Math.random() * 15 - 7.5);
              break;
            case 'temperature':
              value = selectedSensor.threshold.optimal + (Math.random() * 12 - 6);
              break;
            case 'flow-rate':
              value = selectedSensor.threshold.optimal + (Math.random() * 30 - 15);
              break;
            case 'pressure':
              value = selectedSensor.threshold.optimal + (Math.random() * 10 - 5);
              break;
            case 'humidity':
              value = selectedSensor.threshold.optimal + (Math.random() * 15 - 7.5);
              break;
            case 'ph-level':
              value = selectedSensor.threshold.optimal + (Math.random() * 1 - 0.5);
              break;
            default:
              value = selectedSensor.value + (Math.random() * 15 - 7.5);
          }
          
          // Ensure value is within reasonable bounds
          value = Math.max(0, value);
          value = Number(value.toFixed(1));
          
          data.push({
            time: `Week ${i + 1}`,
            value,
            timestamp: time
          });
        }
      }
      
      setHistoricalData(data);
    } catch (error) {
      console.error('Error loading historical data:', error);
    }
  };

  // Function to format date
  const formatDate = (date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Function to get sensor status color
  const getSensorStatusColor = (status) => {
    switch (status) {
      case 'active':
        return '#34C759';
      case 'warning':
        return '#FF9500';
      case 'inactive':
        return '#FF3B30';
      default:
        return '#999';
    }
  };

  // Function to get signal strength icon
  const getSignalStrengthIcon = (strength) => {
    switch (strength) {
      case 'excellent':
        return <Ionicons name="wifi" size={16} color="#34C759" />;
      case 'good':
        return <Ionicons name="wifi" size={16} color="#30D158" />;
      case 'fair':
        return <Ionicons name="wifi" size={16} color="#FF9500" />;
      case 'poor':
        return <Ionicons name="wifi" size={16} color="#FF3B30" />;
      case 'none':
        return <Ionicons name="wifi-off" size={16} color="#999" />;
      default:
        return <Ionicons name="wifi" size={16} color="#999" />;
    }
  };

  // Function to get battery level icon
  const getBatteryLevelIcon = (level) => {
    if (level >= 90) {
      return <Ionicons name="battery-full" size={16} color="#34C759" />;
    } else if (level >= 70) {
      return <Ionicons name="battery-half" size={16} color="#30D158" />;
    } else if (level >= 30) {
      return <Ionicons name="battery-half" size={16} color="#FF9500" />;
    } else if (level > 0) {
      return <Ionicons name="battery-dead" size={16} color="#FF3B30" />;
    } else {
      return <Ionicons name="battery-dead" size={16} color="#999" />;
    }
  };

  // Function to get sensor type icon
  const getSensorTypeIcon = (type) => {
    switch (type) {
      case 'soil-moisture':
        return <Ionicons name="water" size={24} color="#0096FF" />;
      case 'temperature':
        return <Ionicons name="thermometer" size={24} color="#FF9500" />;
      case 'flow-rate':
        return <Ionicons name="speedometer" size={24} color="#34C759" />;
      case 'pressure':
        return <Ionicons name="pulse" size={24} color="#FF3B30" />;
      case 'humidity':
        return <Ionicons name="cloud" size={24} color="#5856D6" />;
      case 'ph-level':
        return <Ionicons name="flask" size={24} color="#FF2D55" />;
      default:
        return <Ionicons name="hardware-chip" size={24} color="#8E8E93" />;
    }
  };

  // Function to get sensor type name
  const getSensorTypeName = (type) => {
    return type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  // Function to render sensor item
  const renderSensorItem = ({ item }) => (
    <TouchableOpacity 
      style={[
        styles.sensorItem,
        selectedSensor?.id === item.id && styles.sensorItemSelected
      ]}
      onPress={() => setSelectedSensor(item)}
    >
      <View style={styles.sensorItemIcon}>
        {getSensorTypeIcon(item.type)}
      </View>
      <View style={styles.sensorItemInfo}>
        <Text style={styles.sensorItemType}>{getSensorTypeName(item.type)}</Text>
        <Text style={styles.sensorItemLocation}>{item.location}</Text>
      </View>
      <View style={styles.sensorItemValue}>
        <Text style={styles.sensorItemValueText}>{item.value} {item.unit}</Text>
        <View 
          style={[
            styles.sensorItemStatus,
            { backgroundColor: getSensorStatusColor(item.status) }
          ]} 
        />
      </View>
    </TouchableOpacity>
  );

  // Function to render chart
  const renderChart = () => {
    if (!selectedSensor || !historicalData.length) return null;
    
    const maxValue = Math.max(...historicalData.map(item => item.value));
    const minValue = Math.min(...historicalData.map(item => item.value));
    const range = maxValue - minValue;
    const chartHeight = 150;
    
    return (
      <View style={styles.chartContainer}>
        <View style={styles.chartYAxis}>
          <Text style={styles.chartYAxisLabel}>{maxValue.toFixed(1)}</Text>
          <Text style={styles.chartYAxisLabel}>{((maxValue + minValue) / 2).toFixed(1)}</Text>
          <Text style={styles.chartYAxisLabel}>{minValue.toFixed(1)}</Text>
        </View>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.chartContent}
        >
          {historicalData.map((item, index) => {
            const height = range === 0 ? chartHeight / 2 : ((item.value - minValue) / range) * chartHeight;
            const isInOptimalRange = 
              item.value >= selectedSensor.threshold.min && 
              item.value <= selectedSensor.threshold.max;
            
            return (
              <View key={index} style={styles.chartBarContainer}>
                <View style={styles.chartBar}>
                  <View 
                    style={[
                      styles.chartBarFill, 
                      { 
                        height, 
                        backgroundColor: isInOptimalRange ? '#34C759' : '#FF3B30' 
                      }
                    ]} 
                  />
                </View>
                <Text style={styles.chartBarLabel}>{item.time}</Text>
              </View>
            );
          })}
        </ScrollView>
      </View>
    );
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="chevron-back" size={24} color="#FFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Sensor Details</Text>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.content}>
        {/* Sensor List */}
        <View style={styles.sensorListContainer}>
          <FlatList
            data={sensors}
            renderItem={renderSensorItem}
            keyExtractor={item => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.sensorList}
          />
        </View>

        <ScrollView style={styles.scrollView}>
          {selectedSensor && (
            <>
              {/* Sensor Overview Card */}
              <Card style={styles.overviewCard}>
                <View style={styles.overviewHeader}>
                  <View style={styles.overviewIconContainer}>
                    {getSensorTypeIcon(selectedSensor.type)}
                  </View>
                  <View style={styles.overviewInfo}>
                    <Text style={styles.overviewTitle}>{getSensorTypeName(selectedSensor.type)}</Text>
                    <Text style={styles.overviewSubtitle}>{selectedSensor.location}</Text>
                  </View>
                  <View 
                    style={[
                      styles.overviewStatus,
                      { backgroundColor: getSensorStatusColor(selectedSensor.status) }
                    ]}
                  >
                    <Text style={styles.overviewStatusText}>
                      {selectedSensor.status.charAt(0).toUpperCase() + selectedSensor.status.slice(1)}
                    </Text>
                  </View>
                </View>
                
                <View style={styles.overviewValue}>
                  <Text style={styles.overviewValueText}>{selectedSensor.value}</Text>
                  <Text style={styles.overviewValueUnit}>{selectedSensor.unit}</Text>
                </View>
                
                <View style={styles.thresholdContainer}>
                  <View style={styles.thresholdBar}>
                    <View 
                      style={[
                        styles.thresholdOptimal,
                        {
                          left: `${(selectedSensor.threshold.min / selectedSensor.threshold.max) * 100}%`,
                          width: `${((selectedSensor.threshold.max - selectedSensor.threshold.min) / selectedSensor.threshold.max) * 100}%`
                        }
                      ]}
                    />
                    <View 
                      style={[
                        styles.thresholdIndicator,
                        { left: `${(selectedSensor.value / selectedSensor.threshold.max) * 100}%` }
                      ]}
                    />
                  </View>
                  <View style={styles.thresholdLabels}>
                    <Text style={styles.thresholdLabel}>Min: {selectedSensor.threshold.min} {selectedSensor.unit}</Text>
                    <Text style={styles.thresholdLabel}>Optimal: {selectedSensor.threshold.optimal} {selectedSensor.unit}</Text>
                    <Text style={styles.thresholdLabel}>Max: {selectedSensor.threshold.max} {selectedSensor.unit}</Text>
                  </View>
                </View>
                
                <View style={styles.sensorStats}>
                  <View style={styles.sensorStat}>
                    <View style={styles.sensorStatIcon}>
                      {getBatteryLevelIcon(selectedSensor.batteryLevel)}
                    </View>
                    <Text style={styles.sensorStatValue}>{selectedSensor.batteryLevel}%</Text>
                    <Text style={styles.sensorStatLabel}>Battery</Text>
                  </View>
                  
                  <View style={styles.sensorStat}>
                    <View style={styles.sensorStatIcon}>
                      {getSignalStrengthIcon(selectedSensor.signalStrength)}
                    </View>
                    <Text style={styles.sensorStatValue}>
                      {selectedSensor.signalStrength.charAt(0).toUpperCase() + selectedSensor.signalStrength.slice(1)}
                    </Text>
                    <Text style={styles.sensorStatLabel}>Signal</Text>
                  </View>
                  
                  <View style={styles.sensorStat}>
                    <View style={styles.sensorStatIcon}>
                      <Ionicons name="calendar" size={16} color="#666" />
                    </View>
                    <Text style={styles.sensorStatValue}>{formatDate(selectedSensor.lastCalibration)}</Text>
                    <Text style={styles.sensorStatLabel}>Calibrated</Text>
                  </View>
                </View>
              </Card>

              {/* Historical Data Card */}
              <Card style={styles.historyCard}>
                <View style={styles.historyHeader}>
                  <Text style={styles.historyTitle}>Historical Data</Text>
                  <View style={styles.timeRangeSelector}>
                    <TouchableOpacity
                      style={[
                        styles.timeRangeButton,
                        selectedTimeRange === 'day' && styles.timeRangeButtonActive
                      ]}
                      onPress={() => setSelectedTimeRange('day')}
                    >
                      <Text 
                        style={[
                          styles.timeRangeButtonText,
                          selectedTimeRange === 'day' && styles.timeRangeButtonTextActive
                        ]}
                      >
                        Day
                      </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[
                        styles.timeRangeButton,
                        selectedTimeRange === 'week' && styles.timeRangeButtonActive
                      ]}
                      onPress={() => setSelectedTimeRange('week')}
                    >
                      <Text 
                        style={[
                          styles.timeRangeButtonText,
                          selectedTimeRange === 'week' && styles.timeRangeButtonTextActive
                        ]}
                      >
                        Week
                      </Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity
                      style={[
                        styles.timeRangeButton,
                        selectedTimeRange === 'month' && styles.timeRangeButtonActive
                      ]}
                      onPress={() => setSelectedTimeRange('month')}
                    >
                      <Text 
                        style={[
                          styles.timeRangeButtonText,
                          selectedTimeRange === 'month' && styles.timeRangeButtonTextActive
                        ]}
                      >
                        Month
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
                
                {renderChart()}
              </Card>

              {/* Sensor Details Card */}
              <Card style={styles.detailsCard}>
                <Text style={styles.detailsTitle}>Sensor Details</Text>
                
                <View style={styles.detailsList}>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Manufacturer</Text>
                    <Text style={styles.detailValue}>{selectedSensor.manufacturer}</Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Model</Text>
                    <Text style={styles.detailValue}>{selectedSensor.model}</Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Serial Number</Text>
                    <Text style={styles.detailValue}>{selectedSensor.serialNumber}</Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Installation Date</Text>
                    <Text style={styles.detailValue}>{formatDate(selectedSensor.installDate)}</Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Last Calibration</Text>
                    <Text style={styles.detailValue}>{formatDate(selectedSensor.lastCalibration)}</Text>
                  </View>
                </View>
              </Card>

              {/* Action Buttons */}
              <View style={styles.actionButtons}>
                <TouchableOpacity style={[styles.actionButton, styles.actionButtonSecondary]}>
                  <Ionicons name="refresh-outline" size={20} color="#0096FF" />
                  <Text style={styles.actionButtonSecondaryText}>Calibrate</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={[styles.actionButton, styles.actionButtonPrimary]}>
                  <Ionicons name="construct-outline" size={20} color="#FFF" />
                  <Text style={styles.actionButtonPrimaryText}>Replace</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#0096FF',
    paddingTop: 16,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFF',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  // Sensor List Styles
  sensorListContainer: {
    backgroundColor: '#FFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  sensorList: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  sensorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 12,
    marginRight: 12,
    width: 180,
  },
  sensorItemSelected: {
    backgroundColor: 'rgba(0, 150, 255, 0.1)',
    borderWidth: 1,
    borderColor: '#0096FF',
  },
  sensorItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 150, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sensorItemInfo: {
    flex: 1,
  },
  sensorItemType: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  sensorItemLocation: {
    fontSize: 12,
    color: '#666',
  },
  sensorItemValue: {
    alignItems: 'flex-end',
  },
  sensorItemValueText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  sensorItemStatus: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  scrollView: {
    flex: 1,
  },
  // Overview Card Styles
  overviewCard: {
    margin: 16,
    padding: 16,
  },
  overviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  overviewIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(0, 150, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  overviewInfo: {
    flex: 1,
  },
  overviewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  overviewSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  overviewStatus: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 16,
  },
  overviewStatusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFF',
  },
  overviewValue: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'center',
    marginBottom: 16,
  },
  overviewValueText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#0096FF',
  },
  overviewValueUnit: {
    fontSize: 20,
    color: '#0096FF',
    marginLeft: 4,
  },
  // Threshold Styles
  thresholdContainer: {
    marginBottom: 16,
  },
  thresholdBar: {
    height: 16,
    backgroundColor: '#E0E0E0',
    borderRadius: 8,
    marginBottom: 8,
    position: 'relative',
  },
  thresholdOptimal: {
    position: 'absolute',
    height: '100%',
    backgroundColor: 'rgba(52, 199, 89, 0.3)',
    borderRadius: 8,
  },
  thresholdIndicator: {
    position: 'absolute',
    width: 4,
    height: 24,
    backgroundColor: '#0096FF',
    borderRadius: 2,
    top: -4,
    marginLeft: -2,
  },
  thresholdLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  thresholdLabel: {
    fontSize: 12,
    color: '#666',
  },
  // Sensor Stats Styles
  sensorStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  sensorStat: {
    alignItems: 'center',
    flex: 1,
  },
  sensorStatIcon: {
    marginBottom: 4,
  },
  sensorStatValue: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  sensorStatLabel: {
    fontSize: 12,
    color: '#666',
  },
  // History Card Styles
  historyCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  timeRangeSelector: {
    flexDirection: 'row',
  },
  timeRangeButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 16,
    marginLeft: 8,
    backgroundColor: '#F0F0F0',
  },
  timeRangeButtonActive: {
    backgroundColor: '#0096FF',
  },
  timeRangeButtonText: {
    fontSize: 12,
    color: '#666',
  },
  timeRangeButtonTextActive: {
    color: '#FFF',
    fontWeight: '500',
  },
  // Chart Styles
  chartContainer: {
    flexDirection: 'row',
    height: 200,
  },
  chartYAxis: {
    width: 40,
    height: '100%',
    justifyContent: 'space-between',
    paddingVertical: 10,
  },
  chartYAxisLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'right',
    paddingRight: 8,
  },
  chartContent: {
    paddingBottom: 20,
    alignItems: 'flex-end',
  },
  chartBarContainer: {
    alignItems: 'center',
    marginHorizontal: 8,
  },
  chartBar: {
    width: 16,
    height: 150,
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    justifyContent: 'flex-end',
    overflow: 'hidden',
  },
  chartBarFill: {
    width: '100%',
    borderRadius: 8,
  },
  chartBarLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
  },
  // Details Card Styles
  detailsCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  detailsList: {
    
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  detailLabel: {
    fontSize: 16,
    color: '#666',
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  // Action Buttons Styles
  actionButtons: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginHorizontal: 8,
  },
  actionButtonPrimary: {
    backgroundColor: '#0096FF',
  },
  actionButtonSecondary: {
    backgroundColor: '#FFF',
    borderWidth: 1,
    borderColor: '#0096FF',
  },
  actionButtonPrimaryText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFF',
    marginLeft: 8,
  },
  actionButtonSecondaryText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0096FF',
    marginLeft: 8,
  },
});

export default SensorDetailScreen;