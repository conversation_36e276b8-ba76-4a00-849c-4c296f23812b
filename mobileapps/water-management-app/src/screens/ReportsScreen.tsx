import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Card, Loading } from 'shared';
import { useNavigation } from '@react-navigation/native';

/**
 * Reports screen for the Water Management App
 * Displays water usage reports and analytics
 */
const ReportsScreen = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reportData, setReportData] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('week'); // 'day', 'week', 'month', 'year'
  const [selectedReport, setSelectedReport] = useState('usage'); // 'usage', 'savings', 'efficiency'

  // Load report data
  useEffect(() => {
    loadReportData();
  }, [selectedPeriod, selectedReport]);

  // Function to load report data
  const loadReportData = async () => {
    try {
      setIsLoading(true);
      // In a real app, this would fetch data from an API
      // For now, we'll use mock data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      // Mock data for different report types and periods
      const mockData = {
        usage: {
          day: {
            total: 12500, // gallons
            change: 5, // percentage change from previous period
            breakdown: [
              { name: 'North Field', value: 4200, percentage: 33.6 },
              { name: 'South Field', value: 3100, percentage: 24.8 },
              { name: 'East Field', value: 1800, percentage: 14.4 },
              { name: 'West Field', value: 2900, percentage: 23.2 },
              { name: 'Greenhouse', value: 500, percentage: 4 }
            ],
            trend: [
              { time: '12 AM', value: 200 },
              { time: '3 AM', value: 150 },
              { time: '6 AM', value: 800 },
              { time: '9 AM', value: 2500 },
              { time: '12 PM', value: 3200 },
              { time: '3 PM', value: 2800 },
              { time: '6 PM', value: 1900 },
              { time: '9 PM', value: 950 }
            ]
          },
          week: {
            total: 87500, // gallons
            change: -3, // percentage change from previous period
            breakdown: [
              { name: 'North Field', value: 28000, percentage: 32 },
              { name: 'South Field', value: 22000, percentage: 25.1 },
              { name: 'East Field', value: 12500, percentage: 14.3 },
              { name: 'West Field', value: 21000, percentage: 24 },
              { name: 'Greenhouse', value: 4000, percentage: 4.6 }
            ],
            trend: [
              { time: 'Mon', value: 12500 },
              { time: 'Tue', value: 13200 },
              { time: 'Wed', value: 11800 },
              { time: 'Thu', value: 12900 },
              { time: 'Fri', value: 13500 },
              { time: 'Sat', value: 12600 },
              { time: 'Sun', value: 11000 }
            ]
          },
          month: {
            total: 375000, // gallons
            change: -8, // percentage change from previous period
            breakdown: [
              { name: 'North Field', value: 120000, percentage: 32 },
              { name: 'South Field', value: 93750, percentage: 25 },
              { name: 'East Field', value: 56250, percentage: 15 },
              { name: 'West Field', value: 86250, percentage: 23 },
              { name: 'Greenhouse', value: 18750, percentage: 5 }
            ],
            trend: [
              { time: 'Week 1', value: 98000 },
              { time: 'Week 2', value: 95000 },
              { time: 'Week 3', value: 92000 },
              { time: 'Week 4', value: 90000 }
            ]
          },
          year: {
            total: 4500000, // gallons
            change: -12, // percentage change from previous period
            breakdown: [
              { name: 'North Field', value: 1440000, percentage: 32 },
              { name: 'South Field', value: 1125000, percentage: 25 },
              { name: 'East Field', value: 675000, percentage: 15 },
              { name: 'West Field', value: 1035000, percentage: 23 },
              { name: 'Greenhouse', value: 225000, percentage: 5 }
            ],
            trend: [
              { time: 'Jan', value: 280000 },
              { time: 'Feb', value: 250000 },
              { time: 'Mar', value: 320000 },
              { time: 'Apr', value: 380000 },
              { time: 'May', value: 450000 },
              { time: 'Jun', value: 520000 },
              { time: 'Jul', value: 580000 },
              { time: 'Aug', value: 520000 },
              { time: 'Sep', value: 420000 },
              { time: 'Oct', value: 350000 },
              { time: 'Nov', value: 230000 },
              { time: 'Dec', value: 200000 }
            ]
          }
        },
        savings: {
          day: {
            total: 2500, // gallons
            change: 15, // percentage change from previous period
            breakdown: [
              { name: 'Smart Scheduling', value: 1200, percentage: 48 },
              { name: 'Leak Prevention', value: 800, percentage: 32 },
              { name: 'Weather Adjustment', value: 500, percentage: 20 }
            ],
            trend: [
              { time: '12 AM', value: 50 },
              { time: '3 AM', value: 30 },
              { time: '6 AM', value: 200 },
              { time: '9 AM', value: 500 },
              { time: '12 PM', value: 650 },
              { time: '3 PM', value: 550 },
              { time: '6 PM', value: 350 },
              { time: '9 PM', value: 170 }
            ]
          },
          week: {
            total: 17500, // gallons
            change: 12, // percentage change from previous period
            breakdown: [
              { name: 'Smart Scheduling', value: 8400, percentage: 48 },
              { name: 'Leak Prevention', value: 5600, percentage: 32 },
              { name: 'Weather Adjustment', value: 3500, percentage: 20 }
            ],
            trend: [
              { time: 'Mon', value: 2500 },
              { time: 'Tue', value: 2600 },
              { time: 'Wed', value: 2400 },
              { time: 'Thu', value: 2500 },
              { time: 'Fri', value: 2600 },
              { time: 'Sat', value: 2500 },
              { time: 'Sun', value: 2400 }
            ]
          },
          month: {
            total: 75000, // gallons
            change: 18, // percentage change from previous period
            breakdown: [
              { name: 'Smart Scheduling', value: 36000, percentage: 48 },
              { name: 'Leak Prevention', value: 24000, percentage: 32 },
              { name: 'Weather Adjustment', value: 15000, percentage: 20 }
            ],
            trend: [
              { time: 'Week 1', value: 18000 },
              { time: 'Week 2', value: 18500 },
              { time: 'Week 3', value: 19000 },
              { time: 'Week 4', value: 19500 }
            ]
          },
          year: {
            total: 900000, // gallons
            change: 22, // percentage change from previous period
            breakdown: [
              { name: 'Smart Scheduling', value: 432000, percentage: 48 },
              { name: 'Leak Prevention', value: 288000, percentage: 32 },
              { name: 'Weather Adjustment', value: 180000, percentage: 20 }
            ],
            trend: [
              { time: 'Jan', value: 60000 },
              { time: 'Feb', value: 55000 },
              { time: 'Mar', value: 65000 },
              { time: 'Apr', value: 70000 },
              { time: 'May', value: 75000 },
              { time: 'Jun', value: 80000 },
              { time: 'Jul', value: 85000 },
              { time: 'Aug', value: 80000 },
              { time: 'Sep', value: 75000 },
              { time: 'Oct', value: 70000 },
              { time: 'Nov', value: 65000 },
              { time: 'Dec', value: 60000 }
            ]
          }
        },
        efficiency: {
          day: {
            score: 85, // percentage
            change: 3, // percentage change from previous period
            breakdown: [
              { name: 'North Field', value: 88, percentage: 88 },
              { name: 'South Field', value: 82, percentage: 82 },
              { name: 'East Field', value: 78, percentage: 78 },
              { name: 'West Field', value: 90, percentage: 90 },
              { name: 'Greenhouse', value: 92, percentage: 92 }
            ],
            trend: [
              { time: '12 AM', value: 85 },
              { time: '3 AM', value: 85 },
              { time: '6 AM', value: 84 },
              { time: '9 AM', value: 83 },
              { time: '12 PM', value: 82 },
              { time: '3 PM', value: 84 },
              { time: '6 PM', value: 86 },
              { time: '9 PM', value: 85 }
            ]
          },
          week: {
            score: 83, // percentage
            change: 5, // percentage change from previous period
            breakdown: [
              { name: 'North Field', value: 86, percentage: 86 },
              { name: 'South Field', value: 80, percentage: 80 },
              { name: 'East Field', value: 76, percentage: 76 },
              { name: 'West Field', value: 88, percentage: 88 },
              { name: 'Greenhouse', value: 90, percentage: 90 }
            ],
            trend: [
              { time: 'Mon', value: 82 },
              { time: 'Tue', value: 82 },
              { time: 'Wed', value: 83 },
              { time: 'Thu', value: 83 },
              { time: 'Fri', value: 84 },
              { time: 'Sat', value: 84 },
              { time: 'Sun', value: 85 }
            ]
          },
          month: {
            score: 80, // percentage
            change: 8, // percentage change from previous period
            breakdown: [
              { name: 'North Field', value: 83, percentage: 83 },
              { name: 'South Field', value: 78, percentage: 78 },
              { name: 'East Field', value: 74, percentage: 74 },
              { name: 'West Field', value: 85, percentage: 85 },
              { name: 'Greenhouse', value: 88, percentage: 88 }
            ],
            trend: [
              { time: 'Week 1', value: 78 },
              { time: 'Week 2', value: 79 },
              { time: 'Week 3', value: 81 },
              { time: 'Week 4', value: 83 }
            ]
          },
          year: {
            score: 76, // percentage
            change: 12, // percentage change from previous period
            breakdown: [
              { name: 'North Field', value: 79, percentage: 79 },
              { name: 'South Field', value: 74, percentage: 74 },
              { name: 'East Field', value: 70, percentage: 70 },
              { name: 'West Field', value: 81, percentage: 81 },
              { name: 'Greenhouse', value: 84, percentage: 84 }
            ],
            trend: [
              { time: 'Jan', value: 70 },
              { time: 'Feb', value: 71 },
              { time: 'Mar', value: 72 },
              { time: 'Apr', value: 73 },
              { time: 'May', value: 74 },
              { time: 'Jun', value: 75 },
              { time: 'Jul', value: 76 },
              { time: 'Aug', value: 77 },
              { time: 'Sep', value: 78 },
              { time: 'Oct', value: 79 },
              { time: 'Nov', value: 80 },
              { time: 'Dec', value: 81 }
            ]
          }
        }
      };
      
      setReportData(mockData[selectedReport][selectedPeriod]);
    } catch (error) {
      console.error('Error loading report data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await loadReportData();
    setRefreshing(false);
  };

  // Function to format water volume
  const formatWaterVolume = (gallons) => {
    if (gallons >= 1000000) {
      return `${(gallons / 1000000).toFixed(2)} M gal`;
    } else if (gallons >= 1000) {
      return `${(gallons / 1000).toFixed(1)} K gal`;
    }
    return `${gallons} gal`;
  };

  // Function to navigate to water usage screen
  const navigateToWaterUsage = () => {
    navigation.navigate('WaterUsage');
  };

  // Function to get change indicator color
  const getChangeColor = (change, isEfficiency = false) => {
    if (isEfficiency) {
      return change >= 0 ? '#34C759' : '#FF3B30';
    } else {
      return change <= 0 ? '#34C759' : '#FF3B30';
    }
  };

  // Function to get change indicator icon
  const getChangeIcon = (change, isEfficiency = false) => {
    if (isEfficiency) {
      return change >= 0 ? 'arrow-up' : 'arrow-down';
    } else {
      return change <= 0 ? 'arrow-down' : 'arrow-up';
    }
  };

  // Function to get period label
  const getPeriodLabel = () => {
    switch (selectedPeriod) {
      case 'day':
        return 'Today';
      case 'week':
        return 'This Week';
      case 'month':
        return 'This Month';
      case 'year':
        return 'This Year';
      default:
        return 'This Week';
    }
  };

  // Function to get report title
  const getReportTitle = () => {
    switch (selectedReport) {
      case 'usage':
        return 'Water Usage';
      case 'savings':
        return 'Water Savings';
      case 'efficiency':
        return 'Water Efficiency';
      default:
        return 'Water Usage';
    }
  };

  // Function to render trend chart
  const renderTrendChart = () => {
    if (!reportData || !reportData.trend) return null;
    
    const maxValue = Math.max(...reportData.trend.map(item => item.value));
    
    return (
      <View style={styles.trendChart}>
        {reportData.trend.map((item, index) => (
          <View key={index} style={styles.trendBar}>
            <View 
              style={[
                styles.trendBarFill, 
                { 
                  height: `${(item.value / maxValue) * 100}%`,
                  backgroundColor: selectedReport === 'efficiency' ? '#34C759' : '#0096FF'
                }
              ]} 
            />
            <Text style={styles.trendBarLabel}>{item.time}</Text>
          </View>
        ))}
      </View>
    );
  };

  // Function to render breakdown chart
  const renderBreakdownChart = () => {
    if (!reportData || !reportData.breakdown) return null;
    
    return (
      <View style={styles.breakdownChart}>
        {reportData.breakdown.map((item, index) => (
          <View key={index} style={styles.breakdownItem}>
            <View style={styles.breakdownItemHeader}>
              <Text style={styles.breakdownItemName}>{item.name}</Text>
              <Text style={styles.breakdownItemValue}>
                {selectedReport === 'efficiency' 
                  ? `${item.value}%` 
                  : formatWaterVolume(item.value)}
              </Text>
            </View>
            <View style={styles.breakdownItemBar}>
              <View 
                style={[
                  styles.breakdownItemBarFill, 
                  { 
                    width: `${item.percentage}%`,
                    backgroundColor: selectedReport === 'efficiency'
                      ? (item.value >= 80 ? '#34C759' : item.value >= 70 ? '#FF9500' : '#FF3B30')
                      : getBreakdownColor(index)
                  }
                ]} 
              />
            </View>
          </View>
        ))}
      </View>
    );
  };

  // Function to get breakdown color
  const getBreakdownColor = (index) => {
    const colors = ['#0096FF', '#34C759', '#FF9500', '#FF3B30', '#5856D6'];
    return colors[index % colors.length];
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Report Type Selector */}
        <View style={styles.reportTypeSelector}>
          <TouchableOpacity
            style={[styles.reportTypeButton, selectedReport === 'usage' && styles.reportTypeButtonActive]}
            onPress={() => setSelectedReport('usage')}
          >
            <Ionicons 
              name="water" 
              size={20} 
              color={selectedReport === 'usage' ? '#FFF' : '#666'} 
            />
            <Text 
              style={[styles.reportTypeText, selectedReport === 'usage' && styles.reportTypeTextActive]}
            >
              Usage
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.reportTypeButton, selectedReport === 'savings' && styles.reportTypeButtonActive]}
            onPress={() => setSelectedReport('savings')}
          >
            <Ionicons 
              name="leaf" 
              size={20} 
              color={selectedReport === 'savings' ? '#FFF' : '#666'} 
            />
            <Text 
              style={[styles.reportTypeText, selectedReport === 'savings' && styles.reportTypeTextActive]}
            >
              Savings
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.reportTypeButton, selectedReport === 'efficiency' && styles.reportTypeButtonActive]}
            onPress={() => setSelectedReport('efficiency')}
          >
            <Ionicons 
              name="speedometer" 
              size={20} 
              color={selectedReport === 'efficiency' ? '#FFF' : '#666'} 
            />
            <Text 
              style={[styles.reportTypeText, selectedReport === 'efficiency' && styles.reportTypeTextActive]}
            >
              Efficiency
            </Text>
          </TouchableOpacity>
        </View>

        {/* Period Selector */}
        <View style={styles.periodSelector}>
          <TouchableOpacity
            style={[styles.periodButton, selectedPeriod === 'day' && styles.periodButtonActive]}
            onPress={() => setSelectedPeriod('day')}
          >
            <Text 
              style={[styles.periodButtonText, selectedPeriod === 'day' && styles.periodButtonTextActive]}
            >
              Day
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.periodButton, selectedPeriod === 'week' && styles.periodButtonActive]}
            onPress={() => setSelectedPeriod('week')}
          >
            <Text 
              style={[styles.periodButtonText, selectedPeriod === 'week' && styles.periodButtonTextActive]}
            >
              Week
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.periodButton, selectedPeriod === 'month' && styles.periodButtonActive]}
            onPress={() => setSelectedPeriod('month')}
          >
            <Text 
              style={[styles.periodButtonText, selectedPeriod === 'month' && styles.periodButtonTextActive]}
            >
              Month
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.periodButton, selectedPeriod === 'year' && styles.periodButtonActive]}
            onPress={() => setSelectedPeriod('year')}
          >
            <Text 
              style={[styles.periodButtonText, selectedPeriod === 'year' && styles.periodButtonTextActive]}
            >
              Year
            </Text>
          </TouchableOpacity>
        </View>

        {/* Summary Card */}
        <Card style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>{getReportTitle()}: {getPeriodLabel()}</Text>
          
          <View style={styles.summaryContent}>
            <Text style={styles.summaryValue}>
              {selectedReport === 'efficiency' 
                ? `${reportData.score}%` 
                : formatWaterVolume(reportData.total)}
            </Text>
            
            <View style={styles.summaryChange}>
              <Ionicons 
                name={getChangeIcon(reportData.change, selectedReport === 'efficiency')} 
                size={16} 
                color={getChangeColor(reportData.change, selectedReport === 'efficiency')} 
              />
              <Text 
                style={[
                  styles.summaryChangeText,
                  { color: getChangeColor(reportData.change, selectedReport === 'efficiency') }
                ]}
              >
                {Math.abs(reportData.change)}%
              </Text>
              <Text style={styles.summaryChangePeriod}>vs. previous {selectedPeriod}</Text>
            </View>
          </View>
        </Card>

        {/* Trend Chart Card */}
        <Card style={styles.chartCard}>
          <View style={styles.chartHeader}>
            <Text style={styles.chartTitle}>Trend</Text>
          </View>
          
          {renderTrendChart()}
        </Card>

        {/* Breakdown Chart Card */}
        <Card style={styles.chartCard}>
          <View style={styles.chartHeader}>
            <Text style={styles.chartTitle}>Breakdown</Text>
          </View>
          
          {renderBreakdownChart()}
        </Card>

        {/* Action Button */}
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={navigateToWaterUsage}
        >
          <Text style={styles.actionButtonText}>View Detailed Reports</Text>
          <Ionicons name="chevron-forward" size={16} color="#FFF" />
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  scrollView: {
    flex: 1,
  },
  // Report Type Selector Styles
  reportTypeSelector: {
    flexDirection: 'row',
    backgroundColor: '#FFF',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  reportTypeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginHorizontal: 4,
    backgroundColor: '#F0F0F0',
  },
  reportTypeButtonActive: {
    backgroundColor: '#0096FF',
  },
  reportTypeText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  reportTypeTextActive: {
    color: '#FFF',
    fontWeight: '500',
  },
  // Period Selector Styles
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#FFF',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  periodButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  periodButtonActive: {
    borderBottomColor: '#0096FF',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#666',
  },
  periodButtonTextActive: {
    color: '#0096FF',
    fontWeight: '500',
  },
  // Summary Card Styles
  summaryCard: {
    margin: 16,
    padding: 16,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 16,
  },
  summaryContent: {
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#0096FF',
    marginBottom: 8,
  },
  summaryChange: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  summaryChangeText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  summaryChangePeriod: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  // Chart Card Styles
  chartCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
  },
  chartHeader: {
    marginBottom: 16,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  // Trend Chart Styles
  trendChart: {
    height: 200,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
    paddingBottom: 24,
  },
  trendBar: {
    flex: 1,
    alignItems: 'center',
    height: '100%',
    justifyContent: 'flex-end',
  },
  trendBarFill: {
    width: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  trendBarLabel: {
    fontSize: 12,
    color: '#666',
  },
  // Breakdown Chart Styles
  breakdownChart: {
    marginTop: 8,
  },
  breakdownItem: {
    marginBottom: 16,
  },
  breakdownItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  breakdownItemName: {
    fontSize: 14,
    color: '#333',
  },
  breakdownItemValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  breakdownItemBar: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  breakdownItemBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  // Action Button Styles
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0096FF',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginBottom: 24,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#FFF',
    marginRight: 4,
  },
});

export default ReportsScreen;