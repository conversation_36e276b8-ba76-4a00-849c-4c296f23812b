import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Card, Loading } from 'shared';
import { useNavigation } from '@react-navigation/native';

/**
 * Home screen for the Water Management App
 * Displays a dashboard with summary information about irrigation systems, water usage, and alerts
 */
const HomeScreen = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, []);

  // Function to load dashboard data
  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      // In a real app, this would fetch data from an API
      // For now, we'll use mock data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      setDashboardData({
        activeSystems: 12,
        totalSystems: 15,
        waterUsageToday: 25600, // gallons
        waterUsageWeek: 178500, // gallons
        soilMoistureAvg: 42, // percentage
        alerts: [
          { id: '1', type: 'warning', message: 'Low soil moisture in Field 3', timestamp: new Date() },
          { id: '2', type: 'critical', message: 'Irrigation system 5 offline', timestamp: new Date() },
          { id: '3', type: 'info', message: 'Scheduled maintenance due for Pump 2', timestamp: new Date() }
        ],
        weatherForecast: {
          today: { condition: 'sunny', tempHigh: 85, tempLow: 62, precipitation: 0 },
          tomorrow: { condition: 'partly-cloudy', tempHigh: 82, tempLow: 65, precipitation: 20 }
        },
        waterConservation: {
          savingsThisMonth: 45600, // gallons
          savingsPercentage: 18 // percentage compared to previous month
        }
      });
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  // Function to format water volume
  const formatWaterVolume = (gallons) => {
    if (gallons >= 1000000) {
      return `${(gallons / 1000000).toFixed(2)} M gal`;
    } else if (gallons >= 1000) {
      return `${(gallons / 1000).toFixed(1)} K gal`;
    }
    return `${gallons} gal`;
  };

  // Function to navigate to irrigation systems screen
  const navigateToIrrigation = () => {
    navigation.navigate('Irrigation');
  };

  // Function to navigate to water usage screen
  const navigateToWaterUsage = () => {
    navigation.navigate('WaterUsage');
  };

  // Function to navigate to monitoring screen
  const navigateToMonitoring = () => {
    navigation.navigate('Monitoring');
  };

  // Function to navigate to conservation screen
  const navigateToConservation = () => {
    navigation.navigate('Conservation');
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Water Management</Text>
          <Text style={styles.headerSubtitle}>Dashboard</Text>
        </View>

        {/* Irrigation Systems Summary */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Ionicons name="water" size={24} color="#0096FF" />
            <Text style={styles.cardTitle}>Irrigation Systems</Text>
          </View>
          <View style={styles.systemsContainer}>
            <View style={styles.systemStat}>
              <Text style={styles.systemStatValue}>{dashboardData.activeSystems}</Text>
              <Text style={styles.systemStatLabel}>Active</Text>
            </View>
            <View style={styles.systemDivider} />
            <View style={styles.systemStat}>
              <Text style={styles.systemStatValue}>{dashboardData.totalSystems - dashboardData.activeSystems}</Text>
              <Text style={styles.systemStatLabel}>Inactive</Text>
            </View>
            <View style={styles.systemDivider} />
            <View style={styles.systemStat}>
              <Text style={styles.systemStatValue}>{dashboardData.totalSystems}</Text>
              <Text style={styles.systemStatLabel}>Total</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.cardButton} onPress={navigateToIrrigation}>
            <Text style={styles.cardButtonText}>View All Systems</Text>
            <Ionicons name="chevron-forward" size={16} color="#0096FF" />
          </TouchableOpacity>
        </Card>

        {/* Water Usage Summary */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Ionicons name="analytics" size={24} color="#0096FF" />
            <Text style={styles.cardTitle}>Water Usage</Text>
          </View>
          <View style={styles.usageContainer}>
            <View style={styles.usageStat}>
              <Text style={styles.usageStatLabel}>Today</Text>
              <Text style={styles.usageStatValue}>{formatWaterVolume(dashboardData.waterUsageToday)}</Text>
            </View>
            <View style={styles.usageStat}>
              <Text style={styles.usageStatLabel}>This Week</Text>
              <Text style={styles.usageStatValue}>{formatWaterVolume(dashboardData.waterUsageWeek)}</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.cardButton} onPress={navigateToWaterUsage}>
            <Text style={styles.cardButtonText}>View Detailed Usage</Text>
            <Ionicons name="chevron-forward" size={16} color="#0096FF" />
          </TouchableOpacity>
        </Card>

        {/* Soil Moisture Summary */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Ionicons name="leaf" size={24} color="#0096FF" />
            <Text style={styles.cardTitle}>Soil Moisture</Text>
          </View>
          <View style={styles.moistureContainer}>
            <View style={styles.moistureGauge}>
              <View 
                style={[
                  styles.moistureFill, 
                  { 
                    width: `${dashboardData.soilMoistureAvg}%`,
                    backgroundColor: 
                      dashboardData.soilMoistureAvg < 30 ? '#FF3B30' : 
                      dashboardData.soilMoistureAvg < 40 ? '#FF9500' : '#34C759'
                  }
                ]} 
              />
            </View>
            <Text style={styles.moistureValue}>{dashboardData.soilMoistureAvg}%</Text>
            <Text style={styles.moistureLabel}>Average Across All Fields</Text>
          </View>
          <TouchableOpacity style={styles.cardButton} onPress={navigateToMonitoring}>
            <Text style={styles.cardButtonText}>View Monitoring Data</Text>
            <Ionicons name="chevron-forward" size={16} color="#0096FF" />
          </TouchableOpacity>
        </Card>

        {/* Alerts */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Ionicons name="warning" size={24} color="#0096FF" />
            <Text style={styles.cardTitle}>Alerts</Text>
          </View>
          <View style={styles.alertsContainer}>
            {dashboardData.alerts.map(alert => (
              <View 
                key={alert.id} 
                style={[
                  styles.alertItem,
                  alert.type === 'critical' ? styles.alertCritical :
                  alert.type === 'warning' ? styles.alertWarning : styles.alertInfo
                ]}
              >
                <Ionicons 
                  name={
                    alert.type === 'critical' ? 'alert-circle' :
                    alert.type === 'warning' ? 'warning' : 'information-circle'
                  } 
                  size={20} 
                  color={
                    alert.type === 'critical' ? '#FF3B30' :
                    alert.type === 'warning' ? '#FF9500' : '#0096FF'
                  } 
                />
                <Text style={styles.alertMessage}>{alert.message}</Text>
              </View>
            ))}
          </View>
        </Card>

        {/* Weather Forecast */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Ionicons name="partly-sunny" size={24} color="#0096FF" />
            <Text style={styles.cardTitle}>Weather Forecast</Text>
          </View>
          <View style={styles.weatherContainer}>
            <View style={styles.weatherDay}>
              <Text style={styles.weatherDayLabel}>Today</Text>
              <Ionicons 
                name={
                  dashboardData.weatherForecast.today.condition === 'sunny' ? 'sunny' :
                  dashboardData.weatherForecast.today.condition === 'partly-cloudy' ? 'partly-sunny' :
                  dashboardData.weatherForecast.today.condition === 'cloudy' ? 'cloudy' :
                  dashboardData.weatherForecast.today.condition === 'rainy' ? 'rainy' : 'thunderstorm'
                } 
                size={32} 
                color="#FF9500" 
              />
              <Text style={styles.weatherTemp}>
                {dashboardData.weatherForecast.today.tempHigh}°F / {dashboardData.weatherForecast.today.tempLow}°F
              </Text>
              <Text style={styles.weatherPrecip}>
                {dashboardData.weatherForecast.today.precipitation}% Precip
              </Text>
            </View>
            <View style={styles.weatherDivider} />
            <View style={styles.weatherDay}>
              <Text style={styles.weatherDayLabel}>Tomorrow</Text>
              <Ionicons 
                name={
                  dashboardData.weatherForecast.tomorrow.condition === 'sunny' ? 'sunny' :
                  dashboardData.weatherForecast.tomorrow.condition === 'partly-cloudy' ? 'partly-sunny' :
                  dashboardData.weatherForecast.tomorrow.condition === 'cloudy' ? 'cloudy' :
                  dashboardData.weatherForecast.tomorrow.condition === 'rainy' ? 'rainy' : 'thunderstorm'
                } 
                size={32} 
                color="#FF9500" 
              />
              <Text style={styles.weatherTemp}>
                {dashboardData.weatherForecast.tomorrow.tempHigh}°F / {dashboardData.weatherForecast.tomorrow.tempLow}°F
              </Text>
              <Text style={styles.weatherPrecip}>
                {dashboardData.weatherForecast.tomorrow.precipitation}% Precip
              </Text>
            </View>
          </View>
        </Card>

        {/* Water Conservation */}
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Ionicons name="leaf" size={24} color="#0096FF" />
            <Text style={styles.cardTitle}>Water Conservation</Text>
          </View>
          <View style={styles.conservationContainer}>
            <View style={styles.conservationStat}>
              <Text style={styles.conservationValue}>{formatWaterVolume(dashboardData.waterConservation.savingsThisMonth)}</Text>
              <Text style={styles.conservationLabel}>Saved This Month</Text>
            </View>
            <View style={styles.conservationStat}>
              <Text style={[styles.conservationValue, styles.conservationPercentage]}>
                {dashboardData.waterConservation.savingsPercentage}%
              </Text>
              <Text style={styles.conservationLabel}>Improvement</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.cardButton} onPress={navigateToConservation}>
            <Text style={styles.cardButtonText}>View Conservation Plan</Text>
            <Ionicons name="chevron-forward" size={16} color="#0096FF" />
          </TouchableOpacity>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 16,
    backgroundColor: '#0096FF',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  card: {
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  cardButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  cardButtonText: {
    color: '#0096FF',
    fontWeight: '500',
    marginRight: 4,
  },
  // Irrigation Systems styles
  systemsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 8,
  },
  systemStat: {
    alignItems: 'center',
  },
  systemStatValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0096FF',
  },
  systemStatLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  systemDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#E0E0E0',
  },
  // Water Usage styles
  usageContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 8,
  },
  usageStat: {
    alignItems: 'center',
  },
  usageStatValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0096FF',
  },
  usageStatLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  // Soil Moisture styles
  moistureContainer: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  moistureGauge: {
    width: '100%',
    height: 20,
    backgroundColor: '#E0E0E0',
    borderRadius: 10,
    overflow: 'hidden',
  },
  moistureFill: {
    height: '100%',
    borderRadius: 10,
  },
  moistureValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0096FF',
    marginTop: 8,
  },
  moistureLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  // Alerts styles
  alertsContainer: {
    paddingVertical: 8,
  },
  alertItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
    borderRadius: 8,
    borderLeftWidth: 4,
  },
  alertCritical: {
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    borderLeftColor: '#FF3B30',
  },
  alertWarning: {
    backgroundColor: 'rgba(255, 149, 0, 0.1)',
    borderLeftColor: '#FF9500',
  },
  alertInfo: {
    backgroundColor: 'rgba(0, 150, 255, 0.1)',
    borderLeftColor: '#0096FF',
  },
  alertMessage: {
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  // Weather styles
  weatherContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 8,
  },
  weatherDay: {
    alignItems: 'center',
    flex: 1,
  },
  weatherDayLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  weatherTemp: {
    fontSize: 14,
    marginTop: 8,
  },
  weatherPrecip: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  weatherDivider: {
    width: 1,
    height: 80,
    backgroundColor: '#E0E0E0',
  },
  // Conservation styles
  conservationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 8,
  },
  conservationStat: {
    alignItems: 'center',
  },
  conservationValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#34C759',
  },
  conservationPercentage: {
    fontSize: 24,
  },
  conservationLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
});

export default HomeScreen;