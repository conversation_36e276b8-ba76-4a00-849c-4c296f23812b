{"expo": {"name": "Water Management App", "slug": "water-management-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.nxtacre.watermanagement", "infoPlist": {"UIBackgroundModes": ["location", "fetch"], "NSLocationWhenInUseUsageDescription": "This app uses your location to track irrigation systems and provide field-specific data.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses your location in the background to monitor irrigation systems and provide alerts even when the app is not open."}, "config": {"googleMapsApiKey": "YOUR_I<PERSON>_GOOGLE_MAPS_API_KEY"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.nxtacre.watermanagement", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION"], "config": {"googleMaps": {"apiKey": "YOUR_ANDROID_GOOGLE_MAPS_API_KEY"}}}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location", "expo-file-system", "expo-image-picker", "expo-camera", "expo-sqlite", "expo-notifications", "expo-task-manager", "expo-background-fetch"], "extra": {"eas": {"projectId": "water-management-app"}}}}