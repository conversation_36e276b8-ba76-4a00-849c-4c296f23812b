{"expo": {"name": "Driver App", "slug": "driver-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.nxtacre.driver", "infoPlist": {"UIBackgroundModes": ["location", "fetch"], "NSLocationWhenInUseUsageDescription": "This app uses your location to track deliveries and provide navigation.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses your location in the background to track deliveries and provide navigation even when the app is not open."}, "config": {"googleMapsApiKey": "YOUR_I<PERSON>_GOOGLE_MAPS_API_KEY"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.nxtacre.driver", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION"], "config": {"googleMaps": {"apiKey": "YOUR_ANDROID_GOOGLE_MAPS_API_KEY"}}}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location", "expo-file-system", "expo-image-picker", "expo-camera", "expo-notifications", "expo-task-manager", "expo-background-fetch"], "extra": {"eas": {"projectId": "driver-app"}}}}