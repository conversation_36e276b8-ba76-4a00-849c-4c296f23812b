import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, FlatList } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Button } from 'shared';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';

/**
 * Photo Capture screen for the Driver App
 * Allows taking photos for delivery documentation
 */
const PhotoCaptureScreen = ({ route, navigation }) => {
  const { deliveryId } = route.params;
  const [photos, setPhotos] = useState([]);
  const [hasPermission, setHasPermission] = useState(null);

  // Request camera permissions
  useEffect(() => {
    (async () => {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  // Take a photo
  const takePhoto = async () => {
    try {
      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Add the new photo to the list
        setPhotos([...photos, result.assets[0].uri]);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
    }
  };

  // Pick a photo from the gallery
  const pickPhoto = async () => {
    try {
      // Launch image library
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Add the new photo to the list
        setPhotos([...photos, result.assets[0].uri]);
      }
    } catch (error) {
      console.error('Error picking photo:', error);
    }
  };

  // Remove a photo
  const removePhoto = (index) => {
    const newPhotos = [...photos];
    newPhotos.splice(index, 1);
    setPhotos(newPhotos);
  };

  // Save photos and return to delivery detail
  const savePhotos = async () => {
    try {
      // In a real app, this would upload the photos to a server
      // For this example, we'll just simulate saving
      
      // Navigate back to the delivery detail screen with the photos
      navigation.navigate('DeliveryDetail', { 
        deliveryId,
        photos
      });
    } catch (error) {
      console.error('Error saving photos:', error);
    }
  };

  // Render a photo item
  const renderPhotoItem = ({ item, index }) => (
    <View style={styles.photoItem}>
      <Image source={{ uri: item }} style={styles.photoThumbnail} />
      <TouchableOpacity 
        style={styles.removeButton}
        onPress={() => removePhoto(index)}
      >
        <Ionicons name="close-circle" size={24} color="#FF3B30" />
      </TouchableOpacity>
    </View>
  );

  if (hasPermission === null) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Requesting camera permission...</Text>
      </SafeAreaView>
    );
  }

  if (hasPermission === false) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>No access to camera. Please enable camera permissions in your device settings.</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Delivery Photos</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.content}>
        <Text style={styles.instructions}>
          Take photos to document the delivery. You can take multiple photos.
        </Text>

        {/* Photo grid */}
        {photos.length > 0 ? (
          <FlatList
            data={photos}
            renderItem={renderPhotoItem}
            keyExtractor={(item, index) => index.toString()}
            numColumns={3}
            contentContainerStyle={styles.photoGrid}
          />
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="images-outline" size={48} color="#999" />
            <Text style={styles.emptyStateText}>No photos yet</Text>
          </View>
        )}

        {/* Camera buttons */}
        <View style={styles.cameraButtons}>
          <Button
            title="Take Photo"
            onPress={takePhoto}
            style={styles.cameraButton}
          />
          <Button
            title="Choose from Gallery"
            onPress={pickPhoto}
            variant="secondary"
            style={styles.cameraButton}
          />
        </View>

        {/* Save button */}
        <Button
          title="Save Photos"
          onPress={savePhotos}
          disabled={photos.length === 0}
          style={styles.saveButton}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 24,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  instructions: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  photoGrid: {
    paddingBottom: 16,
  },
  photoItem: {
    margin: 4,
    position: 'relative',
  },
  photoThumbnail: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyStateText: {
    marginTop: 8,
    fontSize: 16,
    color: '#999',
  },
  cameraButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  cameraButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  saveButton: {
    marginTop: 8,
  },
});

export default PhotoCaptureScreen;