import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, TextInput, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Card, Button } from 'shared';

/**
 * Delivery Detail screen for the Driver App
 * Displays detailed information about a delivery and allows status updates
 */
const DeliveryDetailScreen = ({ route, navigation }) => {
  const { deliveryId } = route.params;

  // Placeholder data - would be fetched from an API in a real app
  const [delivery, setDelivery] = useState({
    id: deliveryId,
    customer: 'Acme Farms',
    address: '123 Farm Road, Farmville, CA',
    contactName: 'Jane Smith',
    contactPhone: '(*************',
    status: 'pending', // 'pending', 'in_progress', 'completed', 'cancelled'
    time: '10:00 AM',
    date: new Date().toLocaleDateString(),
    notes: 'Please call customer upon arrival. Gate code: 1234',
    items: [
      { id: '1', name: 'Organic Fertilizer', quantity: 2, unit: 'bags' },
      { id: '2', name: 'Tomato Seeds', quantity: 5, unit: 'packets' },
      { id: '3', name: 'Irrigation Supplies', quantity: 1, unit: 'box' },
    ],
    signature: null, // Would be an image URL in a real app
    photos: [], // Would be image URLs in a real app
    eta: null, // Estimated time of arrival
    etaLastUpdated: null, // When the ETA was last updated
    etaNotificationSent: false, // Whether an ETA notification has been sent
  });

  // State for ETA input
  const [etaHours, setEtaHours] = useState('');
  const [etaMinutes, setEtaMinutes] = useState('');
  const [isEtaUpdating, setIsEtaUpdating] = useState(false);

  // Update delivery status
  const updateStatus = (newStatus) => {
    setDelivery({
      ...delivery,
      status: newStatus,
    });
  };

  // Calculate and update ETA
  const updateETA = () => {
    // Validate input
    const hours = parseInt(etaHours, 10);
    const minutes = parseInt(etaMinutes, 10);

    if (isNaN(hours) || isNaN(minutes) || hours < 0 || minutes < 0 || minutes > 59) {
      Alert.alert('Invalid ETA', 'Please enter a valid time (hours and minutes).');
      return;
    }

    // Calculate ETA in milliseconds
    const now = new Date();
    const etaTime = new Date(now.getTime() + (hours * 60 * 60 * 1000) + (minutes * 60 * 1000));

    // Update delivery with new ETA
    setIsEtaUpdating(true);

    // Simulate API call to update ETA
    setTimeout(() => {
      setDelivery({
        ...delivery,
        eta: etaTime.toISOString(),
        etaLastUpdated: new Date().toISOString(),
        etaNotificationSent: false, // Reset notification status when ETA is updated
      });
      setIsEtaUpdating(false);

      // Clear input fields
      setEtaHours('');
      setEtaMinutes('');

      Alert.alert('ETA Updated', 'The estimated time of arrival has been updated.');
    }, 1000);
  };

  // Send ETA notification to customer
  const sendETANotification = () => {
    if (!delivery.eta) {
      Alert.alert('No ETA Set', 'Please set an ETA before sending a notification.');
      return;
    }

    // Simulate API call to send notification
    setIsEtaUpdating(true);

    setTimeout(() => {
      setDelivery({
        ...delivery,
        etaNotificationSent: true,
      });
      setIsEtaUpdating(false);

      Alert.alert('Notification Sent', 'The customer has been notified of the updated ETA.');
    }, 1000);
  };

  // Format ETA for display
  const formatETA = (etaString) => {
    if (!etaString) return 'Not set';

    const eta = new Date(etaString);
    const now = new Date();

    // Calculate time difference in minutes
    const diffMs = eta.getTime() - now.getTime();
    const diffMinutes = Math.round(diffMs / 60000);

    if (diffMinutes <= 0) {
      return 'Arrived/Past due';
    }

    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;

    if (hours > 0) {
      return `${hours} hr ${minutes} min`;
    } else {
      return `${minutes} min`;
    }
  };

  // Get status badge color
  const getStatusColor = () => {
    switch (delivery.status) {
      case 'pending':
        return '#FFE8CC';
      case 'in_progress':
        return '#CCE8FF';
      case 'completed':
        return '#D1F2D3';
      case 'cancelled':
        return '#FFD1D1';
      default:
        return '#E5E5E5';
    }
  };

  // Get status text
  const getStatusText = () => {
    switch (delivery.status) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Unknown';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Delivery Details</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView>
        {/* Delivery summary */}
        <Card style={styles.card}>
          <View style={styles.deliveryHeader}>
            <Text style={styles.customerName}>{delivery.customer}</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor() }]}>
              <Text style={styles.statusText}>{getStatusText()}</Text>
            </View>
          </View>
          <Text style={styles.address}>{delivery.address}</Text>
          <View style={styles.deliveryMeta}>
            <Text style={styles.metaText}>
              <Ionicons name="time-outline" size={14} /> {delivery.time}
            </Text>
            <Text style={styles.metaText}>
              <Ionicons name="calendar-outline" size={14} /> {delivery.date}
            </Text>
          </View>
        </Card>

        {/* Contact information */}
        <Card style={styles.card}>
          <Text style={styles.cardTitle}>Contact Information</Text>
          <View style={styles.contactItem}>
            <Ionicons name="person-outline" size={20} color="#666" style={styles.contactIcon} />
            <Text style={styles.contactText}>{delivery.contactName}</Text>
          </View>
          <View style={styles.contactItem}>
            <Ionicons name="call-outline" size={20} color="#666" style={styles.contactIcon} />
            <Text style={styles.contactText}>{delivery.contactPhone}</Text>
          </View>
          <View style={styles.contactActions}>
            <TouchableOpacity style={styles.contactAction}>
              <Ionicons name="call" size={20} color="#007AFF" />
              <Text style={styles.contactActionText}>Call</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.contactAction}>
              <Ionicons name="chatbubble" size={20} color="#007AFF" />
              <Text style={styles.contactActionText}>Message</Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Delivery items */}
        <Card style={styles.card}>
          <Text style={styles.cardTitle}>Items</Text>
          {delivery.items.map((item) => (
            <View key={item.id} style={styles.itemRow}>
              <Text style={styles.itemName}>{item.name}</Text>
              <Text style={styles.itemQuantity}>
                {item.quantity} {item.unit}
              </Text>
            </View>
          ))}
        </Card>

        {/* Notes */}
        {delivery.notes && (
          <Card style={styles.card}>
            <Text style={styles.cardTitle}>Notes</Text>
            <Text style={styles.notes}>{delivery.notes}</Text>
          </Card>
        )}

        {/* Signature and photos */}
        <Card style={styles.card}>
          <Text style={styles.cardTitle}>Delivery Confirmation</Text>

          {delivery.signature ? (
            <View style={styles.signatureContainer}>
              <Text style={styles.signatureLabel}>Signature:</Text>
              <Image source={{ uri: delivery.signature }} style={styles.signature} />
            </View>
          ) : (
            <TouchableOpacity 
              style={styles.captureButton}
              onPress={() => navigation.navigate('Signature', { deliveryId })}
            >
              <Ionicons name="create-outline" size={20} color="#007AFF" />
              <Text style={styles.captureButtonText}>Capture Signature</Text>
            </TouchableOpacity>
          )}

          <View style={styles.photosContainer}>
            <Text style={styles.photosLabel}>Photos:</Text>
            {delivery.photos.length > 0 ? (
              <View style={styles.photoGrid}>
                {delivery.photos.map((photo, index) => (
                  <Image key={index} source={{ uri: photo }} style={styles.photo} />
                ))}
              </View>
            ) : (
              <TouchableOpacity 
                style={styles.captureButton}
                onPress={() => navigation.navigate('PhotoCapture', { deliveryId })}
              >
                <Ionicons name="camera-outline" size={20} color="#007AFF" />
                <Text style={styles.captureButtonText}>Take Photos</Text>
              </TouchableOpacity>
            )}
          </View>
        </Card>

        {/* Customer ETA Updates */}
        <Card style={styles.card}>
          <Text style={styles.cardTitle}>Customer ETA Updates</Text>

          <View style={styles.etaStatusContainer}>
            <View style={styles.etaStatusItem}>
              <Text style={styles.etaLabel}>Current ETA:</Text>
              <Text style={styles.etaValue}>{formatETA(delivery.eta)}</Text>
            </View>

            {delivery.etaLastUpdated && (
              <View style={styles.etaStatusItem}>
                <Text style={styles.etaLabel}>Last Updated:</Text>
                <Text style={styles.etaValue}>
                  {new Date(delivery.etaLastUpdated).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </View>
            )}

            <View style={styles.etaStatusItem}>
              <Text style={styles.etaLabel}>Notification Status:</Text>
              <Text style={styles.etaValue}>
                {delivery.etaNotificationSent ? 'Sent' : 'Not Sent'}
              </Text>
            </View>
          </View>

          <View style={styles.etaInputContainer}>
            <Text style={styles.etaInputLabel}>Update ETA (time until arrival):</Text>
            <View style={styles.etaInputRow}>
              <View style={styles.etaInputGroup}>
                <TextInput
                  style={styles.etaInput}
                  placeholder="0"
                  keyboardType="number-pad"
                  maxLength={2}
                  value={etaHours}
                  onChangeText={setEtaHours}
                  editable={!isEtaUpdating}
                />
                <Text style={styles.etaInputUnit}>hr</Text>
              </View>

              <View style={styles.etaInputGroup}>
                <TextInput
                  style={styles.etaInput}
                  placeholder="0"
                  keyboardType="number-pad"
                  maxLength={2}
                  value={etaMinutes}
                  onChangeText={setEtaMinutes}
                  editable={!isEtaUpdating}
                />
                <Text style={styles.etaInputUnit}>min</Text>
              </View>

              <TouchableOpacity
                style={[styles.etaButton, isEtaUpdating && styles.etaButtonDisabled]}
                onPress={updateETA}
                disabled={isEtaUpdating}
              >
                <Text style={styles.etaButtonText}>Update</Text>
              </TouchableOpacity>
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.notifyButton,
              (isEtaUpdating || !delivery.eta || delivery.etaNotificationSent) && styles.notifyButtonDisabled
            ]}
            onPress={sendETANotification}
            disabled={isEtaUpdating || !delivery.eta || delivery.etaNotificationSent}
          >
            <Ionicons name="notifications-outline" size={20} color="white" />
            <Text style={styles.notifyButtonText}>
              {delivery.etaNotificationSent ? 'Notification Sent' : 'Notify Customer of ETA'}
            </Text>
          </TouchableOpacity>
        </Card>

        {/* Status update buttons */}
        <View style={styles.statusButtons}>
          {delivery.status === 'pending' && (
            <Button
              title="Start Delivery"
              onPress={() => updateStatus('in_progress')}
              style={styles.statusButton}
            />
          )}

          {delivery.status === 'in_progress' && (
            <Button
              title="Complete Delivery"
              onPress={() => updateStatus('completed')}
              style={styles.statusButton}
            />
          )}

          {(delivery.status === 'pending' || delivery.status === 'in_progress') && (
            <Button
              title="Cancel Delivery"
              onPress={() => updateStatus('cancelled')}
              variant="outline"
              style={styles.statusButton}
            />
          )}

          {(delivery.status === 'completed' || delivery.status === 'cancelled') && (
            <Button
              title="Reset Status"
              onPress={() => updateStatus('pending')}
              variant="secondary"
              style={styles.statusButton}
            />
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 24,
  },
  card: {
    margin: 16,
    marginBottom: 8,
    padding: 16,
  },
  deliveryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  address: {
    fontSize: 16,
    color: '#333',
    marginBottom: 8,
  },
  deliveryMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metaText: {
    fontSize: 14,
    color: '#666',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  contactIcon: {
    marginRight: 8,
  },
  contactText: {
    fontSize: 16,
    color: '#333',
  },
  contactActions: {
    flexDirection: 'row',
    marginTop: 8,
  },
  contactAction: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  contactActionText: {
    marginLeft: 4,
    color: '#007AFF',
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  itemName: {
    fontSize: 16,
    color: '#333',
  },
  itemQuantity: {
    fontSize: 16,
    color: '#666',
  },
  notes: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
  signatureContainer: {
    marginBottom: 16,
  },
  signatureLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  signature: {
    height: 100,
    width: '100%',
    backgroundColor: '#F9F9F9',
    borderRadius: 8,
  },
  photosContainer: {
    marginTop: 16,
  },
  photosLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  photo: {
    height: 80,
    width: 80,
    margin: 4,
    borderRadius: 4,
    backgroundColor: '#F9F9F9',
  },
  captureButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    marginVertical: 8,
  },
  captureButtonText: {
    marginLeft: 8,
    color: '#007AFF',
    fontWeight: '500',
  },
  // ETA styles
  etaStatusContainer: {
    marginBottom: 16,
  },
  etaStatusItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  etaLabel: {
    fontSize: 14,
    color: '#666',
  },
  etaValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  etaInputContainer: {
    marginBottom: 16,
  },
  etaInputLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  etaInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  etaInputGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  etaInput: {
    width: 50,
    height: 40,
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 4,
    paddingHorizontal: 8,
    fontSize: 16,
    textAlign: 'center',
  },
  etaInputUnit: {
    marginLeft: 4,
    fontSize: 14,
    color: '#666',
  },
  etaButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
  },
  etaButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  etaButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  notifyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    borderRadius: 4,
  },
  notifyButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  notifyButtonText: {
    color: 'white',
    fontWeight: '500',
    marginLeft: 8,
  },
  statusButtons: {
    padding: 16,
  },
  statusButton: {
    marginBottom: 12,
  },
});

export default DeliveryDetailScreen;
