import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, TextInput, Switch, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Card, Loading, Button } from 'shared';

/**
 * Vehicle Profile Screen for the Driver App
 * Allows drivers to select or create vehicle profiles with specific dimensions and characteristics
 */
const VehicleProfileScreen = ({ navigation, route }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [vehicles, setVehicles] = useState([]);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState({
    id: '',
    name: '',
    type: 'truck',
    height: '',
    width: '',
    length: '',
    weight: '',
    hasHazmat: false,
    hazmatClass: '',
    notes: ''
  });

  // Placeholder data - would be fetched from an API in a real app
  useEffect(() => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      const mockVehicles = [
        {
          id: '1',
          name: 'Box Truck',
          type: 'truck',
          height: '12',
          width: '8',
          length: '24',
          weight: '26000',
          hasHazmat: false,
          hazmatClass: '',
          notes: 'Standard delivery truck'
        },
        {
          id: '2',
          name: 'Semi Trailer',
          type: 'semi',
          height: '13.5',
          width: '8.5',
          length: '53',
          weight: '80000',
          hasHazmat: true,
          hazmatClass: '3',
          notes: 'Refrigerated trailer'
        },
        {
          id: '3',
          name: 'Cargo Van',
          type: 'van',
          height: '9',
          width: '6',
          length: '15',
          weight: '10000',
          hasHazmat: false,
          hazmatClass: '',
          notes: 'For smaller deliveries'
        }
      ];
      setVehicles(mockVehicles);
      
      // Set first vehicle as selected by default if none is selected
      if (!selectedVehicle && mockVehicles.length > 0) {
        setSelectedVehicle(mockVehicles[0]);
      }
      
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleSelectVehicle = (vehicle) => {
    setSelectedVehicle(vehicle);
    // In a real app, this would save the selection to state/context/storage
  };

  const handleCreateVehicle = () => {
    setEditingVehicle({
      id: '',
      name: '',
      type: 'truck',
      height: '',
      width: '',
      length: '',
      weight: '',
      hasHazmat: false,
      hazmatClass: '',
      notes: ''
    });
    setIsEditing(true);
  };

  const handleEditVehicle = (vehicle) => {
    setEditingVehicle({ ...vehicle });
    setIsEditing(true);
  };

  const handleSaveVehicle = () => {
    // Validate form
    if (!editingVehicle.name) {
      Alert.alert('Error', 'Vehicle name is required');
      return;
    }

    if (!editingVehicle.height || !editingVehicle.width || !editingVehicle.length || !editingVehicle.weight) {
      Alert.alert('Error', 'All dimensions and weight are required');
      return;
    }

    if (editingVehicle.hasHazmat && !editingVehicle.hazmatClass) {
      Alert.alert('Error', 'Hazmat class is required when hazmat is enabled');
      return;
    }

    // In a real app, this would save to an API
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      if (editingVehicle.id) {
        // Update existing vehicle
        const updatedVehicles = vehicles.map(v => 
          v.id === editingVehicle.id ? editingVehicle : v
        );
        setVehicles(updatedVehicles);
        setSelectedVehicle(editingVehicle);
      } else {
        // Create new vehicle
        const newVehicle = {
          ...editingVehicle,
          id: Date.now().toString() // Generate a temporary ID
        };
        setVehicles([...vehicles, newVehicle]);
        setSelectedVehicle(newVehicle);
      }
      
      setIsEditing(false);
      setIsLoading(false);
    }, 1000);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const renderVehicleForm = () => {
    return (
      <ScrollView style={styles.formContainer}>
        <Text style={styles.formLabel}>Vehicle Name</Text>
        <TextInput
          style={styles.input}
          value={editingVehicle.name}
          onChangeText={(text) => setEditingVehicle({ ...editingVehicle, name: text })}
          placeholder="Enter vehicle name"
        />
        
        <Text style={styles.formLabel}>Vehicle Type</Text>
        <View style={styles.typeContainer}>
          {['truck', 'semi', 'van', 'pickup'].map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.typeButton,
                editingVehicle.type === type && styles.selectedTypeButton
              ]}
              onPress={() => setEditingVehicle({ ...editingVehicle, type })}
            >
              <Text
                style={[
                  styles.typeButtonText,
                  editingVehicle.type === type && styles.selectedTypeButtonText
                ]}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <Text style={styles.formLabel}>Dimensions (feet)</Text>
        <View style={styles.dimensionsContainer}>
          <View style={styles.dimensionField}>
            <Text style={styles.dimensionLabel}>Height</Text>
            <TextInput
              style={styles.dimensionInput}
              value={editingVehicle.height}
              onChangeText={(text) => setEditingVehicle({ ...editingVehicle, height: text })}
              placeholder="0.0"
              keyboardType="numeric"
            />
          </View>
          
          <View style={styles.dimensionField}>
            <Text style={styles.dimensionLabel}>Width</Text>
            <TextInput
              style={styles.dimensionInput}
              value={editingVehicle.width}
              onChangeText={(text) => setEditingVehicle({ ...editingVehicle, width: text })}
              placeholder="0.0"
              keyboardType="numeric"
            />
          </View>
          
          <View style={styles.dimensionField}>
            <Text style={styles.dimensionLabel}>Length</Text>
            <TextInput
              style={styles.dimensionInput}
              value={editingVehicle.length}
              onChangeText={(text) => setEditingVehicle({ ...editingVehicle, length: text })}
              placeholder="0.0"
              keyboardType="numeric"
            />
          </View>
        </View>
        
        <Text style={styles.formLabel}>Weight (lbs)</Text>
        <TextInput
          style={styles.input}
          value={editingVehicle.weight}
          onChangeText={(text) => setEditingVehicle({ ...editingVehicle, weight: text })}
          placeholder="Enter weight"
          keyboardType="numeric"
        />
        
        <View style={styles.switchContainer}>
          <Text style={styles.formLabel}>Hazardous Materials</Text>
          <Switch
            value={editingVehicle.hasHazmat}
            onValueChange={(value) => setEditingVehicle({ ...editingVehicle, hasHazmat: value })}
          />
        </View>
        
        {editingVehicle.hasHazmat && (
          <>
            <Text style={styles.formLabel}>Hazmat Class</Text>
            <View style={styles.typeContainer}>
              {['1', '2', '3', '4', '5', '6', '7', '8', '9'].map((hazmatClass) => (
                <TouchableOpacity
                  key={hazmatClass}
                  style={[
                    styles.hazmatButton,
                    editingVehicle.hazmatClass === hazmatClass && styles.selectedHazmatButton
                  ]}
                  onPress={() => setEditingVehicle({ ...editingVehicle, hazmatClass })}
                >
                  <Text
                    style={[
                      styles.hazmatButtonText,
                      editingVehicle.hazmatClass === hazmatClass && styles.selectedHazmatButtonText
                    ]}
                  >
                    {hazmatClass}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </>
        )}
        
        <Text style={styles.formLabel}>Notes</Text>
        <TextInput
          style={[styles.input, styles.notesInput]}
          value={editingVehicle.notes}
          onChangeText={(text) => setEditingVehicle({ ...editingVehicle, notes: text })}
          placeholder="Enter notes about this vehicle"
          multiline
        />
        
        <View style={styles.formButtons}>
          <Button title="Cancel" onPress={handleCancelEdit} type="secondary" />
          <Button title="Save Vehicle" onPress={handleSaveVehicle} />
        </View>
      </ScrollView>
    );
  };

  const renderVehicleList = () => {
    return (
      <>
        <ScrollView style={styles.vehicleList}>
          {vehicles.map((vehicle) => (
            <TouchableOpacity
              key={vehicle.id}
              style={[
                styles.vehicleCard,
                selectedVehicle?.id === vehicle.id && styles.selectedVehicleCard
              ]}
              onPress={() => handleSelectVehicle(vehicle)}
            >
              <View style={styles.vehicleCardContent}>
                <View style={styles.vehicleInfo}>
                  <Text style={styles.vehicleName}>{vehicle.name}</Text>
                  <Text style={styles.vehicleType}>
                    {vehicle.type.charAt(0).toUpperCase() + vehicle.type.slice(1)}
                  </Text>
                  <Text style={styles.vehicleDimensions}>
                    {vehicle.length}' L × {vehicle.width}' W × {vehicle.height}' H
                  </Text>
                  <Text style={styles.vehicleWeight}>{vehicle.weight} lbs</Text>
                  {vehicle.hasHazmat && (
                    <View style={styles.hazmatTag}>
                      <Text style={styles.hazmatTagText}>HAZMAT Class {vehicle.hazmatClass}</Text>
                    </View>
                  )}
                </View>
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => handleEditVehicle(vehicle)}
                >
                  <Ionicons name="pencil" size={20} color="#007AFF" />
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        <View style={styles.actionButtons}>
          <Button
            title="Create New Vehicle"
            onPress={handleCreateVehicle}
            icon="add-circle-outline"
          />
          <Button
            title="Use Selected Vehicle"
            onPress={() => navigation.goBack()}
            disabled={!selectedVehicle}
          />
        </View>
      </>
    );
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Vehicle Profiles</Text>
        <Text style={styles.headerSubtitle}>
          Select or create a vehicle profile for optimized routing
        </Text>
      </View>
      
      {isEditing ? renderVehicleForm() : renderVehicleList()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  vehicleList: {
    flex: 1,
    padding: 16,
  },
  vehicleCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  selectedVehicleCard: {
    borderColor: '#007AFF',
    borderWidth: 2,
  },
  vehicleCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  vehicleInfo: {
    flex: 1,
  },
  vehicleName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  vehicleType: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  vehicleDimensions: {
    fontSize: 14,
    marginBottom: 4,
  },
  vehicleWeight: {
    fontSize: 14,
    marginBottom: 8,
  },
  hazmatTag: {
    backgroundColor: '#FF3B30',
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
  },
  hazmatTagText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  editButton: {
    padding: 8,
  },
  actionButtons: {
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    gap: 12,
  },
  formContainer: {
    flex: 1,
    padding: 16,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 16,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    padding: 12,
    fontSize: 16,
  },
  notesInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  typeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  typeButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    padding: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedTypeButton: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  typeButtonText: {
    color: '#333333',
  },
  selectedTypeButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  dimensionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  dimensionField: {
    flex: 1,
    marginRight: 8,
  },
  dimensionLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  dimensionInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    padding: 12,
    fontSize: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
  },
  hazmatButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    padding: 12,
    marginRight: 8,
    marginBottom: 8,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedHazmatButton: {
    backgroundColor: '#FF3B30',
    borderColor: '#FF3B30',
  },
  hazmatButtonText: {
    color: '#333333',
    fontWeight: 'bold',
  },
  selectedHazmatButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  formButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    marginBottom: 24,
  },
});

export default VehicleProfileScreen;