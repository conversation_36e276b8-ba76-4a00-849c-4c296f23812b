# NxtAcre Driver App

## Overview
The NxtAcre Driver App is designed for delivery drivers and transport staff to manage deliveries, track location, navigate to pickup/delivery locations, and manage their schedule. The app provides a comprehensive set of tools to make the delivery process efficient and transparent.

## Features

### Delivery Management
- View and manage all assigned deliveries
- Access detailed delivery information including pickup and delivery locations
- Update delivery status (pending, in progress, completed, etc.)
- Capture signatures and photos for delivery confirmation

### Real-time Location Tracking
- Track driver location in real-time with background updates
- Optimize battery usage with configurable accuracy settings (high, balanced, low-power)
- Share location with customers and dispatch
- Set up geofences for delivery zones with arrival/departure detection
- View location history and tracking status

### Navigation
- Get directions to pickup and delivery locations
- View optimal routes for deliveries
- Access gate codes and access information
- See estimated arrival times

### Schedule Management
- View daily, weekly, and monthly delivery schedules
- Manage time and availability
- Receive notifications for upcoming deliveries

### Vehicle Management
- Select and manage vehicle profiles
- Track vehicle usage for deliveries

## Technical Implementation

### Location Tracking
The app uses Expo's Location and TaskManager APIs to implement background location tracking. Key features include:

- Real-time location updates using `expo-location`
- Background tracking with `expo-task-manager`
- Configurable accuracy settings to optimize battery usage
- Geofencing for delivery zones
- Location history storage using AsyncStorage
- Map visualization with `react-native-maps`

### Navigation Structure
The app uses React Navigation with a combination of tab and stack navigators:

- Tab Navigator for main screens (Home, Deliveries, Navigation, Tracking, Schedule, Profile)
- Stack Navigator for detail screens (DeliveryDetail, Signature, PhotoCapture, VehicleProfile, LocationTracking)

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm (v7 or higher)
- Expo CLI (`npm install -g expo-cli`)

### Installation
1. Clone the repository
2. Navigate to the driver-app directory
3. Install dependencies:
   ```
   npm install
   ```
4. Start the development server:
   ```
   npm start
   ```
5. Use the Expo Go app on your device to scan the QR code, or run in an emulator/simulator

### Testing
To verify that the app builds correctly, run the test script:
```
./test-build.sh
```

## Dependencies
The app uses the following key dependencies:
- expo: ^53.0.0
- react-native: 0.73.2
- @react-navigation/native: ^6.1.9
- @react-navigation/stack: ^6.3.20
- @react-navigation/bottom-tabs: ^6.5.11
- expo-location: ~16.5.2
- expo-task-manager: ~11.3.0
- react-native-maps: 1.10.0
- @react-native-async-storage/async-storage: 1.21.0

## Recent Updates

### July 14, 2024
- Implemented LocationTrackingScreen with comprehensive location tracking functionality
- Added real-time location updates with background tracking
- Implemented battery optimization settings (high-accuracy, balanced, low-power)
- Added location sharing options for customers and dispatch
- Implemented geofencing for delivery zones with arrival/departure detection
- Added tracking history and status monitoring
- Created map view showing current location, history path, and geofences
- Updated navigation to include LocationTrackingScreen in both stack and tab navigators