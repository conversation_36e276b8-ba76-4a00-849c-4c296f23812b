{"expo": {"name": "Crop Monitor App", "slug": "crop-monitor-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.nxtacre.cropmonitor", "infoPlist": {"UIBackgroundModes": ["location", "fetch"], "NSLocationWhenInUseUsageDescription": "This app uses your location to track field positions and provide mapping features.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses your location in the background to track field positions and provide mapping features even when the app is not open.", "NSCameraUsageDescription": "This app uses the camera to capture images of crops for health monitoring and analysis.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to upload images for crop health analysis."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.nxtacre.cropmonitor", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-location", "expo-file-system", "expo-image-picker", "expo-camera", "expo-sqlite", "expo-notifications"], "extra": {"eas": {"projectId": "crop-monitor-app"}}}}