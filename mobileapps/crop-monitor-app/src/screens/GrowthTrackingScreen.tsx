import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Container, Text, Button } from 'shared';

const GrowthTrackingScreen = () => {
  const navigation = useNavigation();
  const [selectedField, setSelectedField] = useState(null);

  // Mock data for fields
  const fields = [
    {
      id: '1',
      name: 'North Field',
      crop: 'Corn',
      plantingDate: '2023-04-15',
      currentStage: 'V8 (8-Leaf)',
      progress: 45,
      estimatedHarvest: '2023-09-20',
      image: 'https://via.placeholder.com/100',
      growthStages: [
        { stage: 'Planting', date: '2023-04-15', completed: true },
        { stage: 'Emergence', date: '2023-04-22', completed: true },
        { stage: 'V2 (2-Leaf)', date: '2023-05-01', completed: true },
        { stage: 'V4 (4-Leaf)', date: '2023-05-15', completed: true },
        { stage: 'V6 (6-Leaf)', date: '2023-05-30', completed: true },
        { stage: 'V8 (8-Leaf)', date: '2023-06-15', completed: true },
        { stage: 'V12 (12-Leaf)', date: '2023-07-01', completed: false },
        { stage: 'VT (Tasseling)', date: '2023-07-15', completed: false },
        { stage: 'R1 (Silking)', date: '2023-07-25', completed: false },
        { stage: 'R4 (Dough)', date: '2023-08-15', completed: false },
        { stage: 'R6 (Maturity)', date: '2023-09-10', completed: false },
        { stage: 'Harvest', date: '2023-09-20', completed: false },
      ],
    },
    {
      id: '2',
      name: 'East Field',
      crop: 'Soybeans',
      plantingDate: '2023-05-01',
      currentStage: 'V5 (5th Node)',
      progress: 35,
      estimatedHarvest: '2023-10-05',
      image: 'https://via.placeholder.com/100',
      growthStages: [
        { stage: 'Planting', date: '2023-05-01', completed: true },
        { stage: 'VE (Emergence)', date: '2023-05-08', completed: true },
        { stage: 'VC (Cotyledon)', date: '2023-05-15', completed: true },
        { stage: 'V1 (1st Node)', date: '2023-05-22', completed: true },
        { stage: 'V3 (3rd Node)', date: '2023-06-05', completed: true },
        { stage: 'V5 (5th Node)', date: '2023-06-20', completed: true },
        { stage: 'R1 (Beginning Bloom)', date: '2023-07-10', completed: false },
        { stage: 'R3 (Beginning Pod)', date: '2023-07-25', completed: false },
        { stage: 'R5 (Beginning Seed)', date: '2023-08-15', completed: false },
        { stage: 'R7 (Beginning Maturity)', date: '2023-09-20', completed: false },
        { stage: 'R8 (Full Maturity)', date: '2023-10-01', completed: false },
        { stage: 'Harvest', date: '2023-10-05', completed: false },
      ],
    },
    {
      id: '3',
      name: 'West Field',
      crop: 'Wheat',
      plantingDate: '2023-03-01',
      currentStage: 'Heading',
      progress: 65,
      estimatedHarvest: '2023-07-15',
      image: 'https://via.placeholder.com/100',
      growthStages: [
        { stage: 'Planting', date: '2023-03-01', completed: true },
        { stage: 'Germination', date: '2023-03-10', completed: true },
        { stage: 'Seedling', date: '2023-03-20', completed: true },
        { stage: 'Tillering', date: '2023-04-10', completed: true },
        { stage: 'Stem Extension', date: '2023-05-01', completed: true },
        { stage: 'Heading', date: '2023-06-01', completed: true },
        { stage: 'Flowering', date: '2023-06-15', completed: false },
        { stage: 'Milk Development', date: '2023-06-25', completed: false },
        { stage: 'Dough Development', date: '2023-07-05', completed: false },
        { stage: 'Ripening', date: '2023-07-10', completed: false },
        { stage: 'Harvest', date: '2023-07-15', completed: false },
      ],
    },
  ];

  // Navigate to field detail screen
  const navigateToFieldDetail = (fieldId) => {
    navigation.navigate('FieldDetail', { fieldId });
  };

  // Navigate to camera screen for growth documentation
  const navigateToCamera = () => {
    navigation.navigate('Camera', { mode: 'growth', fieldId: selectedField?.id });
  };

  // Render progress bar
  const renderProgressBar = (progress) => (
    <View style={styles.progressBarContainer}>
      <View style={[styles.progressBar, { width: `${progress}%` }]} />
    </View>
  );

  // Render growth stage item
  const renderGrowthStageItem = (stage, index) => (
    <View key={index} style={styles.stageItem}>
      <View style={[styles.stageIndicator, stage.completed ? styles.completedStage : styles.pendingStage]}>
        {stage.completed && <Ionicons name="checkmark" size={16} color="white" />}
      </View>
      <View style={styles.stageInfo}>
        <Text type="bold">{stage.stage}</Text>
        <Text type="caption">{stage.date}</Text>
      </View>
      {stage.completed && <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />}
    </View>
  );

  return (
    <Container>
      <View style={styles.header}>
        <Text type="h1">Growth Tracking</Text>
        {selectedField && (
          <TouchableOpacity style={styles.cameraButton} onPress={navigateToCamera}>
            <Ionicons name="camera" size={24} color="white" />
          </TouchableOpacity>
        )}
      </View>

      {/* Field Selection */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.fieldSelection}>
        {fields.map((field) => (
          <TouchableOpacity
            key={field.id}
            style={[
              styles.fieldCard,
              selectedField?.id === field.id && styles.selectedFieldCard,
            ]}
            onPress={() => setSelectedField(field)}
          >
            <Image source={{ uri: field.image }} style={styles.fieldImage} />
            <View style={styles.fieldInfo}>
              <Text type="bold">{field.name}</Text>
              <Text>{field.crop}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Selected Field Details */}
      {selectedField ? (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Card style={styles.summaryCard}>
            <View style={styles.summaryHeader}>
              <View>
                <Text type="h2">{selectedField.name}</Text>
                <Text>{selectedField.crop}</Text>
              </View>
              <TouchableOpacity onPress={() => navigateToFieldDetail(selectedField.id)}>
                <Text type="link">View Field</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.summaryDetails}>
              <View style={styles.summaryItem}>
                <Text type="caption">Planting Date</Text>
                <Text type="bold">{selectedField.plantingDate}</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text type="caption">Current Stage</Text>
                <Text type="bold">{selectedField.currentStage}</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text type="caption">Estimated Harvest</Text>
                <Text type="bold">{selectedField.estimatedHarvest}</Text>
              </View>
            </View>

            <View style={styles.progressContainer}>
              <View style={styles.progressHeader}>
                <Text type="bold">Growth Progress</Text>
                <Text>{selectedField.progress}%</Text>
              </View>
              {renderProgressBar(selectedField.progress)}
            </View>
          </Card>

          <Text type="h2" style={styles.sectionTitle}>Growth Timeline</Text>
          <Card style={styles.timelineCard}>
            {selectedField.growthStages.map((stage, index) => renderGrowthStageItem(stage, index))}
          </Card>

          <View style={styles.buttonContainer}>
            <Button
              title="Document Growth Stage"
              icon={<Ionicons name="camera" size={20} color="white" />}
              onPress={navigateToCamera}
              style={styles.button}
            />
          </View>
        </ScrollView>
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="leaf" size={64} color="#CCC" />
          <Text type="h2" style={styles.emptyTitle}>Select a Field</Text>
          <Text style={styles.emptyDescription}>
            Choose a field from above to view and track its growth stages.
          </Text>
        </View>
      )}
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cameraButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fieldSelection: {
    marginBottom: 16,
  },
  fieldCard: {
    width: 120,
    marginRight: 12,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#F5F5F5',
  },
  selectedFieldCard: {
    borderWidth: 2,
    borderColor: '#4CAF50',
  },
  fieldImage: {
    width: '100%',
    height: 80,
  },
  fieldInfo: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  summaryCard: {
    marginBottom: 16,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  summaryDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryItem: {
    alignItems: 'center',
  },
  progressContainer: {
    marginTop: 8,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#4CAF50',
  },
  sectionTitle: {
    marginBottom: 12,
  },
  timelineCard: {
    marginBottom: 16,
  },
  stageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  stageIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  completedStage: {
    backgroundColor: '#4CAF50',
  },
  pendingStage: {
    backgroundColor: '#E0E0E0',
  },
  stageInfo: {
    flex: 1,
  },
  buttonContainer: {
    marginBottom: 24,
  },
  button: {
    marginTop: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    textAlign: 'center',
    color: '#666',
  },
});

export default GrowthTrackingScreen;