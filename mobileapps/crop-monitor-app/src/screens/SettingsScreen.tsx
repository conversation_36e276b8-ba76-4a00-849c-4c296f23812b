import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Switch, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Container, Text, Button } from 'shared';

const SettingsScreen = () => {
  const navigation = useNavigation();
  
  // Mock user data
  const [userData, setUserData] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Farm Manager',
    profileImage: 'https://via.placeholder.com/100',
  });

  // Settings state
  const [settings, setSettings] = useState({
    notifications: {
      pushEnabled: true,
      emailEnabled: false,
      taskReminders: true,
      weatherAlerts: true,
      cropHealthAlerts: true,
    },
    appearance: {
      darkMode: false,
      highContrastMode: false,
      fontSize: 'medium', // 'small', 'medium', 'large'
    },
    privacy: {
      locationTracking: true,
      dataSharingEnabled: true,
      analyticsEnabled: true,
    },
    sync: {
      autoSyncEnabled: true,
      syncOnWifiOnly: true,
      syncFrequency: 'hourly', // 'hourly', 'daily', 'manual'
    },
    units: {
      measurementSystem: 'imperial', // 'imperial', 'metric'
      temperatureUnit: 'fahrenheit', // 'fahrenheit', 'celsius'
      areaUnit: 'acres', // 'acres', 'hectares'
    },
  });

  // Toggle switch settings
  const toggleSetting = (category, setting) => {
    setSettings({
      ...settings,
      [category]: {
        ...settings[category],
        [setting]: !settings[category][setting],
      },
    });
  };

  // Change dropdown settings
  const changeSetting = (category, setting, value) => {
    setSettings({
      ...settings,
      [category]: {
        ...settings[category],
        [setting]: value,
      },
    });
  };

  // Sign out handler
  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          onPress: () => {
            // In a real app, this would clear authentication state and navigate to login
            Alert.alert('Signed Out', 'You have been signed out successfully.');
          },
          style: 'destructive',
        },
      ],
      { cancelable: true }
    );
  };

  // Clear cache handler
  const handleClearCache = () => {
    Alert.alert(
      'Clear Cache',
      'Are you sure you want to clear the app cache? This will not delete any of your data.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Clear',
          onPress: () => {
            // In a real app, this would clear the app cache
            Alert.alert('Cache Cleared', 'App cache has been cleared successfully.');
          },
        },
      ],
      { cancelable: true }
    );
  };

  // Render setting item with switch
  const renderSwitchSetting = (label, value, onToggle) => (
    <View style={styles.settingItem}>
      <Text>{label}</Text>
      <Switch
        value={value}
        onValueChange={onToggle}
        trackColor={{ false: '#D1D1D1', true: '#AED581' }}
        thumbColor={value ? '#4CAF50' : '#F5F5F5'}
      />
    </View>
  );

  // Render setting item with options
  const renderOptionSetting = (label, value, options, onChange) => (
    <View style={styles.settingItem}>
      <Text>{label}</Text>
      <View style={styles.optionsContainer}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.optionButton,
              value === option.value && styles.selectedOption,
            ]}
            onPress={() => onChange(option.value)}
          >
            <Text
              style={[
                styles.optionText,
                value === option.value && styles.selectedOptionText,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <Container>
      <View style={styles.header}>
        <Text type="h1">Settings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <Card style={styles.card}>
          <View style={styles.profileHeader}>
            <View style={styles.profileInfo}>
              <Text type="h2">{userData.name}</Text>
              <Text>{userData.email}</Text>
              <Text type="caption">{userData.role}</Text>
            </View>
            <TouchableOpacity style={styles.editProfileButton}>
              <Ionicons name="pencil" size={20} color="white" />
            </TouchableOpacity>
          </View>
        </Card>

        {/* Notifications Section */}
        <Text type="h2" style={styles.sectionTitle}>Notifications</Text>
        <Card style={styles.card}>
          {renderSwitchSetting(
            'Push Notifications',
            settings.notifications.pushEnabled,
            () => toggleSetting('notifications', 'pushEnabled')
          )}
          {renderSwitchSetting(
            'Email Notifications',
            settings.notifications.emailEnabled,
            () => toggleSetting('notifications', 'emailEnabled')
          )}
          {renderSwitchSetting(
            'Task Reminders',
            settings.notifications.taskReminders,
            () => toggleSetting('notifications', 'taskReminders')
          )}
          {renderSwitchSetting(
            'Weather Alerts',
            settings.notifications.weatherAlerts,
            () => toggleSetting('notifications', 'weatherAlerts')
          )}
          {renderSwitchSetting(
            'Crop Health Alerts',
            settings.notifications.cropHealthAlerts,
            () => toggleSetting('notifications', 'cropHealthAlerts')
          )}
        </Card>

        {/* Appearance Section */}
        <Text type="h2" style={styles.sectionTitle}>Appearance</Text>
        <Card style={styles.card}>
          {renderSwitchSetting(
            'Dark Mode',
            settings.appearance.darkMode,
            () => toggleSetting('appearance', 'darkMode')
          )}
          {renderSwitchSetting(
            'High Contrast Mode',
            settings.appearance.highContrastMode,
            () => toggleSetting('appearance', 'highContrastMode')
          )}
          {renderOptionSetting(
            'Font Size',
            settings.appearance.fontSize,
            [
              { label: 'Small', value: 'small' },
              { label: 'Medium', value: 'medium' },
              { label: 'Large', value: 'large' },
            ],
            (value) => changeSetting('appearance', 'fontSize', value)
          )}
        </Card>

        {/* Units Section */}
        <Text type="h2" style={styles.sectionTitle}>Units</Text>
        <Card style={styles.card}>
          {renderOptionSetting(
            'Measurement System',
            settings.units.measurementSystem,
            [
              { label: 'Imperial', value: 'imperial' },
              { label: 'Metric', value: 'metric' },
            ],
            (value) => changeSetting('units', 'measurementSystem', value)
          )}
          {renderOptionSetting(
            'Temperature',
            settings.units.temperatureUnit,
            [
              { label: 'Fahrenheit', value: 'fahrenheit' },
              { label: 'Celsius', value: 'celsius' },
            ],
            (value) => changeSetting('units', 'temperatureUnit', value)
          )}
          {renderOptionSetting(
            'Area',
            settings.units.areaUnit,
            [
              { label: 'Acres', value: 'acres' },
              { label: 'Hectares', value: 'hectares' },
            ],
            (value) => changeSetting('units', 'areaUnit', value)
          )}
        </Card>

        {/* Sync Section */}
        <Text type="h2" style={styles.sectionTitle}>Sync & Storage</Text>
        <Card style={styles.card}>
          {renderSwitchSetting(
            'Auto Sync',
            settings.sync.autoSyncEnabled,
            () => toggleSetting('sync', 'autoSyncEnabled')
          )}
          {renderSwitchSetting(
            'Sync on Wi-Fi Only',
            settings.sync.syncOnWifiOnly,
            () => toggleSetting('sync', 'syncOnWifiOnly')
          )}
          {renderOptionSetting(
            'Sync Frequency',
            settings.sync.syncFrequency,
            [
              { label: 'Hourly', value: 'hourly' },
              { label: 'Daily', value: 'daily' },
              { label: 'Manual', value: 'manual' },
            ],
            (value) => changeSetting('sync', 'syncFrequency', value)
          )}
          <TouchableOpacity style={styles.actionButton} onPress={handleClearCache}>
            <Text style={styles.actionButtonText}>Clear Cache</Text>
          </TouchableOpacity>
        </Card>

        {/* Privacy Section */}
        <Text type="h2" style={styles.sectionTitle}>Privacy</Text>
        <Card style={styles.card}>
          {renderSwitchSetting(
            'Location Tracking',
            settings.privacy.locationTracking,
            () => toggleSetting('privacy', 'locationTracking')
          )}
          {renderSwitchSetting(
            'Data Sharing',
            settings.privacy.dataSharingEnabled,
            () => toggleSetting('privacy', 'dataSharingEnabled')
          )}
          {renderSwitchSetting(
            'Analytics',
            settings.privacy.analyticsEnabled,
            () => toggleSetting('privacy', 'analyticsEnabled')
          )}
        </Card>

        {/* Account Section */}
        <Text type="h2" style={styles.sectionTitle}>Account</Text>
        <Card style={styles.card}>
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="person" size={24} color="#4CAF50" style={styles.menuIcon} />
            <Text>Edit Profile</Text>
            <Ionicons name="chevron-forward" size={20} color="#888" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="lock-closed" size={24} color="#4CAF50" style={styles.menuIcon} />
            <Text>Change Password</Text>
            <Ionicons name="chevron-forward" size={20} color="#888" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="help-circle" size={24} color="#4CAF50" style={styles.menuIcon} />
            <Text>Help & Support</Text>
            <Ionicons name="chevron-forward" size={20} color="#888" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="document-text" size={24} color="#4CAF50" style={styles.menuIcon} />
            <Text>Terms & Privacy Policy</Text>
            <Ionicons name="chevron-forward" size={20} color="#888" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.menuItem}>
            <Ionicons name="information-circle" size={24} color="#4CAF50" style={styles.menuIcon} />
            <Text>About</Text>
            <Ionicons name="chevron-forward" size={20} color="#888" />
          </TouchableOpacity>
        </Card>

        {/* Sign Out Button */}
        <Button
          title="Sign Out"
          icon={<Ionicons name="log-out" size={20} color="white" />}
          onPress={handleSignOut}
          style={styles.signOutButton}
          type="danger"
        />

        {/* App Version */}
        <Text style={styles.versionText}>Version 1.0.0</Text>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    marginBottom: 16,
  },
  content: {
    flex: 1,
  },
  card: {
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  profileInfo: {
    flex: 1,
  },
  editProfileButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sectionTitle: {
    marginBottom: 12,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  optionsContainer: {
    flexDirection: 'row',
  },
  optionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
    marginLeft: 8,
  },
  selectedOption: {
    backgroundColor: '#4CAF50',
  },
  optionText: {
    fontSize: 14,
    color: '#333',
  },
  selectedOptionText: {
    color: 'white',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  menuIcon: {
    marginRight: 12,
  },
  actionButton: {
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  actionButtonText: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  signOutButton: {
    marginBottom: 24,
  },
  versionText: {
    textAlign: 'center',
    color: '#888',
    marginBottom: 24,
  },
});

export default SettingsScreen;