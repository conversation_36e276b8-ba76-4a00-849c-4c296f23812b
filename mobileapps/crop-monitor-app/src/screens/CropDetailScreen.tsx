import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Container, Text, Button } from 'shared';

const CropDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { cropId, fieldId } = route.params || {};
  const [selectedTab, setSelectedTab] = useState('overview');

  // Mock data for crop details
  // In a real app, this would be fetched based on cropId and fieldId
  const cropData = {
    id: '1',
    name: 'Corn',
    variety: 'Pioneer P1197',
    field: 'North Field',
    plantingDate: '2023-04-15',
    emergenceDate: '2023-04-22',
    currentStage: 'V8 (8-Leaf)',
    daysInField: 68,
    growingDegreeDays: 1250,
    estimatedHarvest: '2023-09-20',
    daysToHarvest: 90,
    healthStatus: 'excellent',
    healthScore: 92,
    plantPopulation: 34000,
    rowSpacing: 30,
    seedDepth: 1.5,
    yieldGoal: 220,
    yieldForecast: 195,
    moistureLevel: 'Optimal',
    nutrientStatus: {
      nitrogen: 'Good',
      phosphorus: 'Good',
      potassium: 'Good',
      sulfur: 'Fair',
    },
    images: [
      { id: '1', date: '2023-04-22', stage: 'Emergence', uri: 'https://via.placeholder.com/300' },
      { id: '2', date: '2023-05-15', stage: 'V4 (4-Leaf)', uri: 'https://via.placeholder.com/300' },
      { id: '3', date: '2023-06-15', stage: 'V8 (8-Leaf)', uri: 'https://via.placeholder.com/300' },
    ],
    growthStages: [
      { stage: 'Planting', date: '2023-04-15', completed: true },
      { stage: 'Emergence', date: '2023-04-22', completed: true },
      { stage: 'V2 (2-Leaf)', date: '2023-05-01', completed: true },
      { stage: 'V4 (4-Leaf)', date: '2023-05-15', completed: true },
      { stage: 'V6 (6-Leaf)', date: '2023-05-30', completed: true },
      { stage: 'V8 (8-Leaf)', date: '2023-06-15', completed: true },
      { stage: 'V12 (12-Leaf)', date: '2023-07-01', completed: false },
      { stage: 'VT (Tasseling)', date: '2023-07-15', completed: false },
      { stage: 'R1 (Silking)', date: '2023-07-25', completed: false },
      { stage: 'R4 (Dough)', date: '2023-08-15', completed: false },
      { stage: 'R6 (Maturity)', date: '2023-09-10', completed: false },
      { stage: 'Harvest', date: '2023-09-20', completed: false },
    ],
    issues: [
      { id: '1', type: 'Nutrient Deficiency', severity: 'low', status: 'monitoring', reportedDate: '2023-06-10', notes: 'Slight yellowing in lower leaves, possibly sulfur deficiency' },
    ],
    notes: 'Crop is developing well with good stand establishment. Some minor nutrient deficiency observed but not severe enough to warrant immediate action.',
  };

  // Navigate back
  const goBack = () => {
    navigation.goBack();
  };

  // Navigate to camera screen
  const navigateToCamera = () => {
    navigation.navigate('Camera', { mode: 'crop', cropId, fieldId });
  };

  // Render health status indicator
  const renderHealthIndicator = (status) => {
    let color;
    let label;
    switch (status) {
      case 'excellent':
        color = '#4CAF50'; // Green
        label = 'Excellent';
        break;
      case 'good':
        color = '#8BC34A'; // Light Green
        label = 'Good';
        break;
      case 'fair':
        color = '#FFC107'; // Amber
        label = 'Fair';
        break;
      case 'poor':
        color = '#F44336'; // Red
        label = 'Poor';
        break;
      default:
        color = '#808080'; // Gray
        label = 'Unknown';
    }
    return (
      <View style={styles.healthStatus}>
        <View style={[styles.healthIndicator, { backgroundColor: color }]} />
        <Text>{label}</Text>
      </View>
    );
  };

  // Render nutrient status indicator
  const renderNutrientIndicator = (status) => {
    let color;
    switch (status) {
      case 'Excellent':
        color = '#4CAF50'; // Green
        break;
      case 'Good':
        color = '#8BC34A'; // Light Green
        break;
      case 'Fair':
        color = '#FFC107'; // Amber
        break;
      case 'Poor':
        color = '#F44336'; // Red
        break;
      default:
        color = '#9E9E9E'; // Gray
    }
    return (
      <View style={[styles.nutrientIndicator, { backgroundColor: color }]} />
    );
  };

  // Render growth stage item
  const renderGrowthStageItem = (stage, index) => (
    <View key={index} style={styles.stageItem}>
      <View style={[styles.stageIndicator, stage.completed ? styles.completedStage : styles.pendingStage]}>
        {stage.completed && <Ionicons name="checkmark" size={16} color="white" />}
      </View>
      <View style={styles.stageInfo}>
        <Text type="bold">{stage.stage}</Text>
        <Text type="caption">{stage.date}</Text>
      </View>
      {stage.completed && <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />}
    </View>
  );

  // Render overview tab
  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      {/* Crop Summary */}
      <Card style={styles.card}>
        <View style={styles.cardHeader}>
          <Text type="h2">Crop Summary</Text>
        </View>
        <View style={styles.cropDetails}>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Variety</Text>
              <Text type="bold">{cropData.variety}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Field</Text>
              <Text type="bold">{cropData.field}</Text>
            </View>
          </View>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Planting Date</Text>
              <Text type="bold">{cropData.plantingDate}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Emergence Date</Text>
              <Text type="bold">{cropData.emergenceDate}</Text>
            </View>
          </View>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Current Stage</Text>
              <Text type="bold">{cropData.currentStage}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Days in Field</Text>
              <Text type="bold">{cropData.daysInField}</Text>
            </View>
          </View>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">GDDs</Text>
              <Text type="bold">{cropData.growingDegreeDays}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Est. Harvest</Text>
              <Text type="bold">{cropData.estimatedHarvest}</Text>
            </View>
          </View>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Health Status</Text>
              {renderHealthIndicator(cropData.healthStatus)}
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Health Score</Text>
              <Text type="bold" style={{ color: '#4CAF50' }}>{cropData.healthScore}</Text>
            </View>
          </View>
        </View>
      </Card>

      {/* Planting Details */}
      <Text type="h2" style={styles.sectionTitle}>Planting Details</Text>
      <Card style={styles.card}>
        <View style={styles.cropDetails}>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Plant Population</Text>
              <Text type="bold">{cropData.plantPopulation} plants/acre</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Row Spacing</Text>
              <Text type="bold">{cropData.rowSpacing} inches</Text>
            </View>
          </View>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Seed Depth</Text>
              <Text type="bold">{cropData.seedDepth} inches</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Moisture Level</Text>
              <Text type="bold">{cropData.moistureLevel}</Text>
            </View>
          </View>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Yield Goal</Text>
              <Text type="bold">{cropData.yieldGoal} bu/acre</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Yield Forecast</Text>
              <Text type="bold">{cropData.yieldForecast} bu/acre</Text>
            </View>
          </View>
        </View>
      </Card>

      {/* Nutrient Status */}
      <Text type="h2" style={styles.sectionTitle}>Nutrient Status</Text>
      <Card style={styles.card}>
        <View style={styles.nutrientGrid}>
          <View style={styles.nutrientItem}>
            <View style={styles.nutrientHeader}>
              <Text type="bold">Nitrogen</Text>
              {renderNutrientIndicator(cropData.nutrientStatus.nitrogen)}
            </View>
            <Text>{cropData.nutrientStatus.nitrogen}</Text>
          </View>
          <View style={styles.nutrientItem}>
            <View style={styles.nutrientHeader}>
              <Text type="bold">Phosphorus</Text>
              {renderNutrientIndicator(cropData.nutrientStatus.phosphorus)}
            </View>
            <Text>{cropData.nutrientStatus.phosphorus}</Text>
          </View>
          <View style={styles.nutrientItem}>
            <View style={styles.nutrientHeader}>
              <Text type="bold">Potassium</Text>
              {renderNutrientIndicator(cropData.nutrientStatus.potassium)}
            </View>
            <Text>{cropData.nutrientStatus.potassium}</Text>
          </View>
          <View style={styles.nutrientItem}>
            <View style={styles.nutrientHeader}>
              <Text type="bold">Sulfur</Text>
              {renderNutrientIndicator(cropData.nutrientStatus.sulfur)}
            </View>
            <Text>{cropData.nutrientStatus.sulfur}</Text>
          </View>
        </View>
      </Card>

      {/* Notes */}
      <Text type="h2" style={styles.sectionTitle}>Notes</Text>
      <Card style={styles.card}>
        <Text>{cropData.notes}</Text>
      </Card>
    </View>
  );

  // Render growth tab
  const renderGrowthTab = () => (
    <View style={styles.tabContent}>
      {/* Growth Timeline */}
      <Text type="h2" style={styles.sectionTitle}>Growth Timeline</Text>
      <Card style={styles.timelineCard}>
        {cropData.growthStages.map((stage, index) => renderGrowthStageItem(stage, index))}
      </Card>

      {/* Growth Images */}
      <Text type="h2" style={styles.sectionTitle}>Growth Documentation</Text>
      <View style={styles.imagesContainer}>
        {cropData.images.map((image) => (
          <View key={image.id} style={styles.imageCard}>
            <Image source={{ uri: image.uri }} style={styles.cropImage} />
            <View style={styles.imageInfo}>
              <Text type="bold">{image.stage}</Text>
              <Text type="caption">{image.date}</Text>
            </View>
          </View>
        ))}
        <TouchableOpacity style={styles.addImageButton} onPress={navigateToCamera}>
          <Ionicons name="camera" size={32} color="#4CAF50" />
          <Text style={styles.addImageText}>Add Photo</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title="Document Growth Stage"
          icon={<Ionicons name="camera" size={20} color="white" />}
          onPress={navigateToCamera}
          style={styles.button}
        />
      </View>
    </View>
  );

  // Render issues tab
  const renderIssuesTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.activityHeader}>
        <Text type="h2">Crop Issues</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>
      
      {cropData.issues.length > 0 ? (
        cropData.issues.map((issue) => (
          <Card key={issue.id} style={styles.activityCard}>
            <View style={styles.activityContent}>
              <View style={styles.activityIcon}>
                {issue.type === 'Nutrient Deficiency' && <Ionicons name="flask" size={24} color="#F44336" />}
                {issue.type === 'Pest' && <Ionicons name="bug" size={24} color="#F44336" />}
                {issue.type === 'Disease' && <Ionicons name="medkit" size={24} color="#F44336" />}
              </View>
              <View style={styles.activityInfo}>
                <View style={styles.issueHeader}>
                  <Text type="h3">{issue.type}</Text>
                  <View style={[
                    styles.issueStatus, 
                    issue.status === 'resolved' ? styles.resolvedStatus : 
                    issue.status === 'monitoring' ? styles.monitoringStatus : styles.activeStatus
                  ]}>
                    <Text style={styles.issueStatusText}>
                      {issue.status.charAt(0).toUpperCase() + issue.status.slice(1)}
                    </Text>
                  </View>
                </View>
                <Text type="caption">Reported: {issue.reportedDate}</Text>
                <Text style={styles.activityNotes}>{issue.notes}</Text>
              </View>
            </View>
          </Card>
        ))
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="checkmark-circle" size={64} color="#4CAF50" />
          <Text type="h3" style={styles.emptyTitle}>No Issues Reported</Text>
          <Text style={styles.emptyDescription}>
            This crop currently has no reported issues.
          </Text>
        </View>
      )}
    </View>
  );

  return (
    <Container>
      <View style={styles.header}>
        <TouchableOpacity onPress={goBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text type="h1">{cropData.name}</Text>
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-vertical" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'overview' && styles.activeTab]}
          onPress={() => setSelectedTab('overview')}
        >
          <Text style={selectedTab === 'overview' && styles.activeTabText}>Overview</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'growth' && styles.activeTab]}
          onPress={() => setSelectedTab('growth')}
        >
          <Text style={selectedTab === 'growth' && styles.activeTabText}>Growth</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'issues' && styles.activeTab]}
          onPress={() => setSelectedTab('issues')}
        >
          <Text style={selectedTab === 'issues' && styles.activeTabText}>Issues</Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {selectedTab === 'overview' && renderOverviewTab()}
        {selectedTab === 'growth' && renderGrowthTab()}
        {selectedTab === 'issues' && renderIssuesTab()}
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    padding: 4,
  },
  moreButton: {
    padding: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#4CAF50',
  },
  activeTabText: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    paddingBottom: 16,
  },
  card: {
    marginBottom: 16,
  },
  cardHeader: {
    marginBottom: 12,
  },
  cropDetails: {
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailItem: {
    flex: 1,
  },
  healthStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  healthIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  nutrientGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  nutrientItem: {
    width: '48%',
    marginBottom: 16,
  },
  nutrientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  nutrientIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  timelineCard: {
    marginBottom: 16,
  },
  stageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  stageIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  completedStage: {
    backgroundColor: '#4CAF50',
  },
  pendingStage: {
    backgroundColor: '#E0E0E0',
  },
  stageInfo: {
    flex: 1,
  },
  imagesContainer: {
    marginBottom: 16,
  },
  imageCard: {
    marginBottom: 12,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#FFF',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  cropImage: {
    width: '100%',
    height: 200,
  },
  imageInfo: {
    padding: 12,
  },
  addImageButton: {
    height: 150,
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderStyle: 'dashed',
    marginBottom: 12,
  },
  addImageText: {
    marginTop: 8,
    color: '#4CAF50',
  },
  buttonContainer: {
    marginBottom: 24,
  },
  button: {
    marginTop: 8,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityCard: {
    marginBottom: 12,
  },
  activityContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  activityIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFEBEE',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityInfo: {
    flex: 1,
  },
  activityNotes: {
    marginTop: 8,
  },
  issueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  issueStatus: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  resolvedStatus: {
    backgroundColor: '#E8F5E9',
  },
  monitoringStatus: {
    backgroundColor: '#FFF8E1',
  },
  activeStatus: {
    backgroundColor: '#FFEBEE',
  },
  issueStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    textAlign: 'center',
    color: '#666',
  },
});

export default CropDetailScreen;