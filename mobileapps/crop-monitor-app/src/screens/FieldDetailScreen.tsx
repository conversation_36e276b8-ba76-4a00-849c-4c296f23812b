import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Container, Text, Button } from 'shared';

const FieldDetailScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { fieldId } = route.params || {};
  const [selectedTab, setSelectedTab] = useState('overview');

  // Mock data for field details
  // In a real app, this would be fetched based on fieldId
  const fieldData = {
    id: '1',
    name: 'North Field',
    crop: 'Corn',
    acres: 120,
    plantingDate: '2023-04-15',
    variety: 'Pioneer P1197',
    currentStage: 'V8 (8-Leaf)',
    estimatedHarvest: '2023-09-20',
    healthStatus: 'excellent',
    healthScore: 92,
    soilType: 'Silty Clay Loam',
    lastInspection: '2023-06-22',
    location: {
      latitude: 41.8781,
      longitude: -87.6298,
    },
    boundaries: [
      { latitude: 41.8781, longitude: -87.6298 },
      { latitude: 41.8791, longitude: -87.6298 },
      { latitude: 41.8791, longitude: -87.6308 },
      { latitude: 41.8781, longitude: -87.6308 },
    ],
    images: [
      'https://via.placeholder.com/300',
      'https://via.placeholder.com/300',
      'https://via.placeholder.com/300',
    ],
    notes: 'Field is performing well. Good stand establishment and minimal pest pressure.',
    weather: {
      current: {
        temp: 75,
        condition: 'Partly Cloudy',
        humidity: 65,
        windSpeed: 8,
        precipitation: 0,
      },
      forecast: [
        { day: 'Today', high: 78, low: 62, condition: 'Partly Cloudy', precipitation: 10 },
        { day: 'Tomorrow', high: 82, low: 65, condition: 'Sunny', precipitation: 0 },
        { day: 'Wednesday', high: 85, low: 68, condition: 'Sunny', precipitation: 0 },
      ],
    },
    activities: [
      { id: '1', type: 'Planting', date: '2023-04-15', notes: 'Planted at 34,000 seeds/acre' },
      { id: '2', type: 'Fertilizer', date: '2023-04-15', notes: 'Applied starter fertilizer' },
      { id: '3', type: 'Herbicide', date: '2023-05-10', notes: 'Applied post-emergence herbicide' },
      { id: '4', type: 'Inspection', date: '2023-06-22', notes: 'Field looking good, minimal weed pressure' },
    ],
    issues: [
      { id: '1', type: 'Weed', severity: 'low', status: 'resolved', reportedDate: '2023-05-05', resolvedDate: '2023-05-10', notes: 'Some broadleaf weeds emerging, treated with herbicide' },
    ],
    treatments: [
      { id: '1', type: 'Herbicide', product: 'Roundup PowerMAX', rate: '32 oz/acre', date: '2023-05-10', status: 'completed', effectiveness: 'excellent' },
      { id: '2', type: 'Fungicide', product: 'Headline AMP', rate: '10 oz/acre', date: '2023-07-05', status: 'scheduled', priority: 'high' },
    ],
  };

  // Navigate back
  const goBack = () => {
    navigation.goBack();
  };

  // Navigate to camera screen
  const navigateToCamera = () => {
    navigation.navigate('Camera', { mode: 'field', fieldId });
  };

  // Render health status indicator
  const renderHealthIndicator = (status) => {
    let color;
    let label;
    switch (status) {
      case 'excellent':
        color = '#4CAF50'; // Green
        label = 'Excellent';
        break;
      case 'good':
        color = '#8BC34A'; // Light Green
        label = 'Good';
        break;
      case 'fair':
        color = '#FFC107'; // Amber
        label = 'Fair';
        break;
      case 'poor':
        color = '#F44336'; // Red
        label = 'Poor';
        break;
      default:
        color = '#808080'; // Gray
        label = 'Unknown';
    }
    return (
      <View style={styles.healthStatus}>
        <View style={[styles.healthIndicator, { backgroundColor: color }]} />
        <Text>{label}</Text>
      </View>
    );
  };

  // Render overview tab
  const renderOverviewTab = () => (
    <View style={styles.tabContent}>
      {/* Field Summary */}
      <Card style={styles.card}>
        <View style={styles.cardHeader}>
          <Text type="h2">Field Summary</Text>
        </View>
        <View style={styles.fieldDetails}>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Crop</Text>
              <Text type="bold">{fieldData.crop}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Acres</Text>
              <Text type="bold">{fieldData.acres}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Soil Type</Text>
              <Text type="bold">{fieldData.soilType}</Text>
            </View>
          </View>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Planting Date</Text>
              <Text type="bold">{fieldData.plantingDate}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Variety</Text>
              <Text type="bold">{fieldData.variety}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Est. Harvest</Text>
              <Text type="bold">{fieldData.estimatedHarvest}</Text>
            </View>
          </View>
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <Text type="caption">Current Stage</Text>
              <Text type="bold">{fieldData.currentStage}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Health Status</Text>
              {renderHealthIndicator(fieldData.healthStatus)}
            </View>
            <View style={styles.detailItem}>
              <Text type="caption">Health Score</Text>
              <Text type="bold" style={{ color: '#4CAF50' }}>{fieldData.healthScore}</Text>
            </View>
          </View>
        </View>
      </Card>

      {/* Field Images */}
      <Text type="h2" style={styles.sectionTitle}>Field Images</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imagesContainer}>
        {fieldData.images.map((image, index) => (
          <TouchableOpacity key={index} style={styles.imageWrapper}>
            <Image source={{ uri: image }} style={styles.fieldImage} />
          </TouchableOpacity>
        ))}
        <TouchableOpacity style={styles.addImageButton} onPress={navigateToCamera}>
          <Ionicons name="camera" size={32} color="#4CAF50" />
          <Text style={styles.addImageText}>Add Photo</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Weather */}
      <Text type="h2" style={styles.sectionTitle}>Weather</Text>
      <Card style={styles.card}>
        <View style={styles.currentWeather}>
          <View style={styles.weatherMain}>
            <Text type="h3">{fieldData.weather.current.temp}°F</Text>
            <Text>{fieldData.weather.current.condition}</Text>
          </View>
          <View style={styles.weatherDetails}>
            <View style={styles.weatherDetail}>
              <Ionicons name="water" size={16} color="#4682B4" />
              <Text>{fieldData.weather.current.humidity}%</Text>
            </View>
            <View style={styles.weatherDetail}>
              <Ionicons name="speedometer" size={16} color="#9E9E9E" />
              <Text>{fieldData.weather.current.windSpeed} mph</Text>
            </View>
            <View style={styles.weatherDetail}>
              <Ionicons name="rainy" size={16} color="#4682B4" />
              <Text>{fieldData.weather.current.precipitation}%</Text>
            </View>
          </View>
        </View>
        <View style={styles.weatherForecast}>
          {fieldData.weather.forecast.map((day, index) => (
            <View key={index} style={styles.forecastDay}>
              <Text type="bold">{day.day}</Text>
              <Ionicons 
                name={day.condition === 'Sunny' ? 'sunny' : 'partly-sunny'} 
                size={24} 
                color={day.condition === 'Sunny' ? '#FFD700' : '#87CEEB'} 
              />
              <Text>{day.high}° / {day.low}°</Text>
              <Text type="caption">{day.precipitation}%</Text>
            </View>
          ))}
        </View>
      </Card>

      {/* Notes */}
      <Text type="h2" style={styles.sectionTitle}>Notes</Text>
      <Card style={styles.card}>
        <Text>{fieldData.notes}</Text>
      </Card>
    </View>
  );

  // Render activities tab
  const renderActivitiesTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.activityHeader}>
        <Text type="h2">Field Activities</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>
      
      {fieldData.activities.map((activity) => (
        <Card key={activity.id} style={styles.activityCard}>
          <View style={styles.activityContent}>
            <View style={styles.activityIcon}>
              {activity.type === 'Planting' && <Ionicons name="leaf" size={24} color="#4CAF50" />}
              {activity.type === 'Fertilizer' && <Ionicons name="flask" size={24} color="#4CAF50" />}
              {activity.type === 'Herbicide' && <Ionicons name="flask" size={24} color="#4CAF50" />}
              {activity.type === 'Inspection' && <Ionicons name="search" size={24} color="#4CAF50" />}
            </View>
            <View style={styles.activityInfo}>
              <Text type="h3">{activity.type}</Text>
              <Text type="caption">{activity.date}</Text>
              <Text style={styles.activityNotes}>{activity.notes}</Text>
            </View>
          </View>
        </Card>
      ))}
    </View>
  );

  // Render issues tab
  const renderIssuesTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.activityHeader}>
        <Text type="h2">Field Issues</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>
      
      {fieldData.issues.length > 0 ? (
        fieldData.issues.map((issue) => (
          <Card key={issue.id} style={styles.activityCard}>
            <View style={styles.activityContent}>
              <View style={styles.activityIcon}>
                {issue.type === 'Weed' && <Ionicons name="leaf" size={24} color="#F44336" />}
                {issue.type === 'Pest' && <Ionicons name="bug" size={24} color="#F44336" />}
                {issue.type === 'Disease' && <Ionicons name="medkit" size={24} color="#F44336" />}
              </View>
              <View style={styles.activityInfo}>
                <View style={styles.issueHeader}>
                  <Text type="h3">{issue.type} Issue</Text>
                  <View style={[
                    styles.issueStatus, 
                    issue.status === 'resolved' ? styles.resolvedStatus : styles.activeStatus
                  ]}>
                    <Text style={styles.issueStatusText}>
                      {issue.status.charAt(0).toUpperCase() + issue.status.slice(1)}
                    </Text>
                  </View>
                </View>
                <Text type="caption">Reported: {issue.reportedDate}</Text>
                {issue.resolvedDate && (
                  <Text type="caption">Resolved: {issue.resolvedDate}</Text>
                )}
                <Text style={styles.activityNotes}>{issue.notes}</Text>
              </View>
            </View>
          </Card>
        ))
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="checkmark-circle" size={64} color="#4CAF50" />
          <Text type="h3" style={styles.emptyTitle}>No Issues Reported</Text>
          <Text style={styles.emptyDescription}>
            This field currently has no reported issues.
          </Text>
        </View>
      )}
    </View>
  );

  // Render treatments tab
  const renderTreatmentsTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.activityHeader}>
        <Text type="h2">Field Treatments</Text>
        <TouchableOpacity style={styles.addButton}>
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>
      
      {fieldData.treatments.map((treatment) => (
        <Card key={treatment.id} style={styles.activityCard}>
          <View style={styles.activityContent}>
            <View style={styles.activityIcon}>
              <Ionicons name="flask" size={24} color="#4CAF50" />
            </View>
            <View style={styles.activityInfo}>
              <View style={styles.treatmentHeader}>
                <Text type="h3">{treatment.type}</Text>
                {treatment.status === 'completed' ? (
                  <View style={[styles.treatmentStatus, styles.completedStatus]}>
                    <Text style={styles.treatmentStatusText}>Completed</Text>
                  </View>
                ) : (
                  <View style={[styles.treatmentStatus, styles.scheduledStatus]}>
                    <Text style={styles.treatmentStatusText}>Scheduled</Text>
                  </View>
                )}
              </View>
              <Text type="caption">{treatment.date}</Text>
              <Text style={styles.treatmentDetail}>
                {treatment.product} at {treatment.rate}
              </Text>
              {treatment.effectiveness && (
                <Text style={styles.treatmentDetail}>
                  Effectiveness: {treatment.effectiveness.charAt(0).toUpperCase() + treatment.effectiveness.slice(1)}
                </Text>
              )}
              {treatment.priority && (
                <Text style={styles.treatmentDetail}>
                  Priority: {treatment.priority.charAt(0).toUpperCase() + treatment.priority.slice(1)}
                </Text>
              )}
            </View>
          </View>
        </Card>
      ))}
    </View>
  );

  return (
    <Container>
      <View style={styles.header}>
        <TouchableOpacity onPress={goBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text type="h1">{fieldData.name}</Text>
        <TouchableOpacity style={styles.moreButton}>
          <Ionicons name="ellipsis-vertical" size={24} color="#333" />
        </TouchableOpacity>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'overview' && styles.activeTab]}
          onPress={() => setSelectedTab('overview')}
        >
          <Text style={selectedTab === 'overview' && styles.activeTabText}>Overview</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'activities' && styles.activeTab]}
          onPress={() => setSelectedTab('activities')}
        >
          <Text style={selectedTab === 'activities' && styles.activeTabText}>Activities</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'issues' && styles.activeTab]}
          onPress={() => setSelectedTab('issues')}
        >
          <Text style={selectedTab === 'issues' && styles.activeTabText}>Issues</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'treatments' && styles.activeTab]}
          onPress={() => setSelectedTab('treatments')}
        >
          <Text style={selectedTab === 'treatments' && styles.activeTabText}>Treatments</Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {selectedTab === 'overview' && renderOverviewTab()}
        {selectedTab === 'activities' && renderActivitiesTab()}
        {selectedTab === 'issues' && renderIssuesTab()}
        {selectedTab === 'treatments' && renderTreatmentsTab()}
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    padding: 4,
  },
  moreButton: {
    padding: 4,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#4CAF50',
  },
  activeTabText: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    paddingBottom: 16,
  },
  card: {
    marginBottom: 16,
  },
  cardHeader: {
    marginBottom: 12,
  },
  fieldDetails: {
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailItem: {
    flex: 1,
  },
  healthStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  healthIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  imagesContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  imageWrapper: {
    marginRight: 12,
  },
  fieldImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
  },
  addImageButton: {
    width: 200,
    height: 150,
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderStyle: 'dashed',
  },
  addImageText: {
    marginTop: 8,
    color: '#4CAF50',
  },
  currentWeather: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEE',
  },
  weatherMain: {
    flex: 1,
  },
  weatherDetails: {
    flex: 1,
    alignItems: 'flex-end',
  },
  weatherDetail: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  weatherForecast: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  forecastDay: {
    alignItems: 'center',
    flex: 1,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activityCard: {
    marginBottom: 12,
  },
  activityContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  activityIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E8F5E9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityInfo: {
    flex: 1,
  },
  activityNotes: {
    marginTop: 8,
  },
  issueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  issueStatus: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  resolvedStatus: {
    backgroundColor: '#E8F5E9',
  },
  activeStatus: {
    backgroundColor: '#FFEBEE',
  },
  issueStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  treatmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  treatmentStatus: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  completedStatus: {
    backgroundColor: '#E8F5E9',
  },
  scheduledStatus: {
    backgroundColor: '#E3F2FD',
  },
  treatmentStatusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  treatmentDetail: {
    marginTop: 4,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    textAlign: 'center',
    color: '#666',
  },
});

export default FieldDetailScreen;