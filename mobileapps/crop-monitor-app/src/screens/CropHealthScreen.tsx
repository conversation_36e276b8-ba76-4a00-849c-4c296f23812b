import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { Card, Container, Text } from 'shared';

const CropHealthScreen = () => {
  const navigation = useNavigation();
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Mock data for crop health
  const cropHealthData = {
    summary: {
      excellent: 3,
      good: 5,
      fair: 2,
      poor: 1,
    },
    fields: [
      {
        id: '1',
        name: 'North Field',
        crop: 'Corn',
        healthStatus: 'excellent',
        healthScore: 92,
        issues: [],
        lastUpdated: '2023-06-22',
        image: 'https://via.placeholder.com/100',
      },
      {
        id: '2',
        name: 'East Field',
        crop: 'Soybeans',
        healthStatus: 'good',
        healthScore: 85,
        issues: ['Minor nutrient deficiency'],
        lastUpdated: '2023-06-20',
        image: 'https://via.placeholder.com/100',
      },
      {
        id: '3',
        name: 'West Field',
        crop: 'Wheat',
        healthStatus: 'fair',
        healthScore: 68,
        issues: ['Water stress', 'Early signs of rust'],
        lastUpdated: '2023-06-18',
        image: 'https://via.placeholder.com/100',
      },
      {
        id: '4',
        name: 'South Field',
        crop: 'Alfalfa',
        healthStatus: 'poor',
        healthScore: 42,
        issues: ['Pest infestation', 'Nutrient deficiency', 'Water stress'],
        lastUpdated: '2023-06-15',
        image: 'https://via.placeholder.com/100',
      },
      {
        id: '5',
        name: 'Central Field',
        crop: 'Corn',
        healthStatus: 'good',
        healthScore: 78,
        issues: ['Minor water stress'],
        lastUpdated: '2023-06-17',
        image: 'https://via.placeholder.com/100',
      },
    ],
  };

  // Filter fields based on selected filter
  const filteredFields = cropHealthData.fields.filter((field) => {
    if (selectedFilter === 'all') return true;
    return field.healthStatus === selectedFilter;
  });

  // Navigate to field detail screen
  const navigateToFieldDetail = (fieldId) => {
    navigation.navigate('FieldDetail', { fieldId });
  };

  // Navigate to camera screen for health assessment
  const navigateToCamera = () => {
    navigation.navigate('Camera', { mode: 'health' });
  };

  // Render health status indicator
  const renderHealthIndicator = (status) => {
    let color;
    let label;
    switch (status) {
      case 'excellent':
        color = '#4CAF50'; // Green
        label = 'Excellent';
        break;
      case 'good':
        color = '#8BC34A'; // Light Green
        label = 'Good';
        break;
      case 'fair':
        color = '#FFC107'; // Amber
        label = 'Fair';
        break;
      case 'poor':
        color = '#F44336'; // Red
        label = 'Poor';
        break;
      default:
        color = '#808080'; // Gray
        label = 'Unknown';
    }
    return (
      <View style={styles.healthStatus}>
        <View style={[styles.healthIndicator, { backgroundColor: color }]} />
        <Text>{label}</Text>
      </View>
    );
  };

  // Render health score with color
  const renderHealthScore = (score) => {
    let color;
    if (score >= 90) {
      color = '#4CAF50'; // Green
    } else if (score >= 70) {
      color = '#8BC34A'; // Light Green
    } else if (score >= 50) {
      color = '#FFC107'; // Amber
    } else {
      color = '#F44336'; // Red
    }
    return <Text style={[styles.healthScore, { color }]}>{score}</Text>;
  };

  return (
    <Container>
      <View style={styles.header}>
        <Text type="h1">Crop Health</Text>
        <TouchableOpacity style={styles.cameraButton} onPress={navigateToCamera}>
          <Ionicons name="camera" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Health Summary */}
      <Card style={styles.summaryCard}>
        <Text type="h2">Health Summary</Text>
        <View style={styles.summaryContainer}>
          <View style={styles.summaryItem}>
            <View style={[styles.summaryIndicator, styles.excellent]} />
            <Text>{cropHealthData.summary.excellent} Excellent</Text>
          </View>
          <View style={styles.summaryItem}>
            <View style={[styles.summaryIndicator, styles.good]} />
            <Text>{cropHealthData.summary.good} Good</Text>
          </View>
          <View style={styles.summaryItem}>
            <View style={[styles.summaryIndicator, styles.fair]} />
            <Text>{cropHealthData.summary.fair} Fair</Text>
          </View>
          <View style={styles.summaryItem}>
            <View style={[styles.summaryIndicator, styles.poor]} />
            <Text>{cropHealthData.summary.poor} Poor</Text>
          </View>
        </View>
      </Card>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity
            style={[styles.filterTab, selectedFilter === 'all' && styles.activeFilterTab]}
            onPress={() => setSelectedFilter('all')}
          >
            <Text style={selectedFilter === 'all' && styles.activeFilterText}>All</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterTab, selectedFilter === 'excellent' && styles.activeFilterTab]}
            onPress={() => setSelectedFilter('excellent')}
          >
            <Text style={selectedFilter === 'excellent' && styles.activeFilterText}>Excellent</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterTab, selectedFilter === 'good' && styles.activeFilterTab]}
            onPress={() => setSelectedFilter('good')}
          >
            <Text style={selectedFilter === 'good' && styles.activeFilterText}>Good</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterTab, selectedFilter === 'fair' && styles.activeFilterTab]}
            onPress={() => setSelectedFilter('fair')}
          >
            <Text style={selectedFilter === 'fair' && styles.activeFilterText}>Fair</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterTab, selectedFilter === 'poor' && styles.activeFilterTab]}
            onPress={() => setSelectedFilter('poor')}
          >
            <Text style={selectedFilter === 'poor' && styles.activeFilterText}>Poor</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {/* Field List */}
      <ScrollView style={styles.fieldList} showsVerticalScrollIndicator={false}>
        {filteredFields.map((field) => (
          <TouchableOpacity key={field.id} onPress={() => navigateToFieldDetail(field.id)}>
            <Card style={styles.fieldCard}>
              <View style={styles.fieldHeader}>
                <Image source={{ uri: field.image }} style={styles.fieldImage} />
                <View style={styles.fieldInfo}>
                  <Text type="h3">{field.name}</Text>
                  <Text>{field.crop}</Text>
                  <Text type="caption">Updated: {field.lastUpdated}</Text>
                </View>
                {renderHealthScore(field.healthScore)}
              </View>
              <View style={styles.fieldDetails}>
                <View style={styles.fieldDetail}>
                  <Text type="bold">Health Status:</Text>
                  {renderHealthIndicator(field.healthStatus)}
                </View>
                {field.issues.length > 0 && (
                  <View style={styles.fieldDetail}>
                    <Text type="bold">Issues:</Text>
                    <View style={styles.issuesList}>
                      {field.issues.map((issue, index) => (
                        <View key={index} style={styles.issueItem}>
                          <Ionicons name="alert-circle" size={16} color="#F44336" />
                          <Text style={styles.issueText}>{issue}</Text>
                        </View>
                      ))}
                    </View>
                  </View>
                )}
              </View>
            </Card>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cameraButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryCard: {
    marginBottom: 16,
  },
  summaryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '50%',
    marginBottom: 8,
  },
  summaryIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 8,
  },
  excellent: {
    backgroundColor: '#4CAF50', // Green
  },
  good: {
    backgroundColor: '#8BC34A', // Light Green
  },
  fair: {
    backgroundColor: '#FFC107', // Amber
  },
  poor: {
    backgroundColor: '#F44336', // Red
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
  },
  activeFilterTab: {
    backgroundColor: '#4CAF50',
  },
  activeFilterText: {
    color: 'white',
  },
  fieldList: {
    flex: 1,
  },
  fieldCard: {
    marginBottom: 12,
  },
  fieldHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  fieldImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  fieldInfo: {
    flex: 1,
  },
  healthScore: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  fieldDetails: {
    borderTopWidth: 1,
    borderTopColor: '#EEE',
    paddingTop: 12,
  },
  fieldDetail: {
    marginBottom: 8,
  },
  healthStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  healthIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  issuesList: {
    marginTop: 4,
  },
  issueItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  issueText: {
    marginLeft: 8,
  },
});

export default CropHealthScreen;