{"name": "nxtacre-mobile-apps", "version": "1.0.0", "description": "NxtAcre Farm Management Platform Mobile Apps", "private": true, "workspaces": ["field-operations-app", "farm-manager-app", "inventory-equipment-app", "financial-manager-app", "employee-app", "marketplace-app", "driver-app", "drive-tracker-app", "crop-monitor-app", "livestock-manager-app", "water-management-app", "farm-safety-app", "precision-agriculture-app", "shared"], "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "lint": "eslint ."}, "dependencies": {"expo": "^53.0.0", "expo-status-bar": "~1.11.1", "expo-task-manager": "~11.3.0", "react": "18.2.0", "react-native": "0.73.2", "@expo/vector-icons": "^14.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "eslint": "^8.19.0", "jest": "^29.2.1", "typescript": "^5.1.3"}}