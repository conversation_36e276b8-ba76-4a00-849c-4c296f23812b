# NxtAcre Mobile Apps

This directory contains the mobile applications for the NxtAcre Farm Management Platform. The apps are built using React Native with Expo.

## Project Structure

The project is set up as a monorepo using npm workspaces, with each app in its own directory and shared code in the `shared` directory.

```
mobileapps/
├── drive-tracker-app/     # Drive Tracker App for mileage tracking
├── driver-app/            # Driver App for delivery drivers
├── employee-app/          # Employee App for farm employees
├── farm-manager-app/      # Farm Manager App for farm managers
├── field-operations-app/  # Field Operations App for field workers
├── financial-manager-app/ # Financial Manager App for financial managers
├── inventory-equipment-app/ # Inventory & Equipment App for inventory managers
├── marketplace-app/       # Marketplace App for buyers and sellers
├── shared/                # Shared code used across all apps
│   ├── components/        # Shared UI components
│   ├── hooks/             # Shared React hooks
│   ├── navigation/        # Shared navigation utilities
│   ├── services/          # Shared API services
│   ├── store/             # Shared state management
│   ├── types/             # Shared TypeScript types
│   └── utils/             # Shared utility functions
└── package.json           # Root package.json for workspaces
```

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm (v7 or higher)
- Expo CLI (`npm install -g expo-cli`)

### Installation

1. Install dependencies:
   ```
   npm install
   ```

2. Start the development server for a specific app:
   ```
   cd [app-directory]
   npm start
   ```

   Or from the root directory:
   ```
   npm start -- --scope=[app-name]
   ```

### App Structure

Each app follows a similar structure:

```
app-directory/
├── assets/                # Static assets (images, fonts, etc.)
├── src/                   # Source code
│   ├── components/        # App-specific UI components
│   ├── hooks/             # App-specific React hooks
│   ├── navigation/        # Navigation configuration
│   ├── screens/           # Screen components
│   ├── services/          # API services
│   ├── store/             # State management
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Utility functions
├── App.tsx                # Main application component
├── app.json               # Expo configuration
├── index.js               # Entry point
└── package.json           # App-specific dependencies and scripts
```

## Development Guidelines

### Code Style

- Use TypeScript for type safety
- Use functional components with hooks
- Follow the existing patterns and conventions
- Use the shared components and utilities whenever possible
- Keep components modular and reusable

### Shared Code

The `shared` directory contains code that is used across multiple apps. When implementing a feature that could be useful in other apps, consider adding it to the shared code.

### Navigation

All apps use React Navigation for navigation. The navigation structure typically consists of:
- A bottom tab navigator for the main screens
- Stack navigators for screen flows
- Drawer navigator for additional options (when needed)

### State Management

For state management, use:
- React Context for global state
- React hooks (useState, useReducer) for local state
- AsyncStorage for persistent storage

### API Integration

API services should:
- Be organized by domain (e.g., auth, user, field, etc.)
- Handle error cases gracefully
- Support offline mode when possible
- Use the shared API utilities for common functionality

### Testing

- Test your changes on both iOS and Android
- Verify that the app works in offline mode
- Check for any performance issues
- Ensure accessibility features work correctly

## Implementation Status

The implementation status of each app is tracked in the following files:
- [mobileapps_implementation_status.md](../featurespec/mobileapps_implementation_status.md)
- [mobileappfeatures_implementation_status.md](../featurespec/mobileappfeatures_implementation_status.md)

Please update these files as you implement new features.

## Resources

- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [React Navigation Documentation](https://reactnavigation.org/docs/getting-started)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)