import { useState, useEffect, useCallback } from 'react';
import { Trip, Vehicle, Expense, User, PaginatedResponse, ApiResponse } from '../types';
import { tripService, vehicleService, expenseService, userService } from '../services';
import { handleApiError } from '../utils';

// Hook for fetching trips with pagination
export const useTrips = (initialPage = 1, initialLimit = 20) => {
  const [trips, setTrips] = useState<Trip[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const fetchTrips = useCallback(async (page: number, limit: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response: PaginatedResponse<Trip> = await tripService.getTrips(page, limit);
      
      setTrips(prevTrips => (page === 1 ? response.data : [...prevTrips, ...response.data]));
      setHasMore(response.hasMore);
      setTotal(response.total);
    } catch (error) {
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      setPage(prevPage => prevPage + 1);
    }
  }, [loading, hasMore]);

  const refresh = useCallback(() => {
    setPage(1);
  }, []);

  useEffect(() => {
    fetchTrips(page, limit);
  }, [fetchTrips, page, limit]);

  return { trips, loading, error, hasMore, total, loadMore, refresh };
};

// Hook for fetching a single trip
export const useTrip = (tripId: string | null) => {
  const [trip, setTrip] = useState<Trip | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTrip = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response: ApiResponse<Trip> = await tripService.getTrip(id);
      setTrip(response.data);
    } catch (error) {
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (tripId) {
      fetchTrip(tripId);
    }
  }, [fetchTrip, tripId]);

  return { trip, loading, error, refetch: () => tripId && fetchTrip(tripId) };
};

// Hook for fetching vehicles
export const useVehicles = () => {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchVehicles = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response: ApiResponse<Vehicle[]> = await vehicleService.getVehicles();
      setVehicles(response.data);
    } catch (error) {
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchVehicles();
  }, [fetchVehicles]);

  return { vehicles, loading, error, refetch: fetchVehicles };
};

// Hook for fetching a single vehicle
export const useVehicle = (vehicleId: string | null) => {
  const [vehicle, setVehicle] = useState<Vehicle | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchVehicle = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response: ApiResponse<Vehicle> = await vehicleService.getVehicle(id);
      setVehicle(response.data);
    } catch (error) {
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (vehicleId) {
      fetchVehicle(vehicleId);
    }
  }, [fetchVehicle, vehicleId]);

  return { vehicle, loading, error, refetch: () => vehicleId && fetchVehicle(vehicleId) };
};

// Hook for fetching expenses with pagination
export const useExpenses = (initialPage = 1, initialLimit = 20) => {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const fetchExpenses = useCallback(async (page: number, limit: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response: PaginatedResponse<Expense> = await expenseService.getExpenses(page, limit);
      
      setExpenses(prevExpenses => (page === 1 ? response.data : [...prevExpenses, ...response.data]));
      setHasMore(response.hasMore);
      setTotal(response.total);
    } catch (error) {
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      setPage(prevPage => prevPage + 1);
    }
  }, [loading, hasMore]);

  const refresh = useCallback(() => {
    setPage(1);
  }, []);

  useEffect(() => {
    fetchExpenses(page, limit);
  }, [fetchExpenses, page, limit]);

  return { expenses, loading, error, hasMore, total, loadMore, refresh };
};

// Hook for fetching a single expense
export const useExpense = (expenseId: string | null) => {
  const [expense, setExpense] = useState<Expense | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchExpense = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response: ApiResponse<Expense> = await expenseService.getExpense(id);
      setExpense(response.data);
    } catch (error) {
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (expenseId) {
      fetchExpense(expenseId);
    }
  }, [fetchExpense, expenseId]);

  return { expense, loading, error, refetch: () => expenseId && fetchExpense(expenseId) };
};

// Hook for fetching the current user
export const useUser = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUser = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response: ApiResponse<User> = await userService.getCurrentUser();
      setUser(response.data);
    } catch (error) {
      setError(handleApiError(error));
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return { user, loading, error, refetch: fetchUser };
};

// Hook for form submission with loading and error states
export const useFormSubmit = <T, R>(
  submitFn: (data: T) => Promise<R>,
  onSuccess?: (result: R) => void
) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const submit = useCallback(
    async (data: T) => {
      try {
        setLoading(true);
        setError(null);
        
        const result = await submitFn(data);
        
        if (onSuccess) {
          onSuccess(result);
        }
        
        return result;
      } catch (error) {
        const errorMessage = handleApiError(error);
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    [submitFn, onSuccess]
  );

  return { submit, loading, error };
};

// Export all hooks
export default {
  useTrips,
  useTrip,
  useVehicles,
  useVehicle,
  useExpenses,
  useExpense,
  useUser,
  useFormSubmit,
};