import { format, parseISO } from 'date-fns';
import { Location, Trip, Vehicle, Expense, UserSettings } from '../types';

// Date formatting utilities
export const formatDate = (dateString: string, formatString = 'MMM d, yyyy'): string => {
  try {
    return format(parseISO(dateString), formatString);
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
};

export const formatTime = (dateString: string, formatString = 'h:mm a'): string => {
  try {
    return format(parseISO(dateString), formatString);
  } catch (error) {
    console.error('Error formatting time:', error);
    return dateString;
  }
};

export const formatDateTime = (dateString: string, formatString = 'MMM d, yyyy h:mm a'): string => {
  try {
    return format(parseISO(dateString), formatString);
  } catch (error) {
    console.error('Error formatting date and time:', error);
    return dateString;
  }
};

// Distance formatting utilities
export const formatDistance = (distance: number, unit: UserSettings['distanceUnit'] = 'miles'): string => {
  if (unit === 'kilometers') {
    return `${(distance * 1.60934).toFixed(1)} km`;
  }
  return `${distance.toFixed(1)} mi`;
};

// Duration formatting utilities
export const formatDuration = (durationInSeconds: number): string => {
  const hours = Math.floor(durationInSeconds / 3600);
  const minutes = Math.floor((durationInSeconds % 3600) / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
};

// Currency formatting utilities
export const formatCurrency = (amount: number, currency = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount);
};

// Trip utilities
export const calculateTripDistance = (route: Location[]): number => {
  if (!route || route.length < 2) {
    return 0;
  }
  
  let totalDistance = 0;
  
  for (let i = 1; i < route.length; i++) {
    totalDistance += calculateDistance(
      route[i - 1].latitude,
      route[i - 1].longitude,
      route[i].latitude,
      route[i].longitude
    );
  }
  
  return totalDistance;
};

export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  // Haversine formula to calculate distance between two points
  const R = 3958.8; // Earth's radius in miles
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) *
      Math.cos(toRadians(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return distance;
};

export const toRadians = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

export const calculateTripDuration = (trip: Trip): number => {
  if (!trip.startTime || !trip.endTime) {
    return 0;
  }
  
  const startTime = new Date(trip.startTime).getTime();
  const endTime = new Date(trip.endTime).getTime();
  
  return Math.floor((endTime - startTime) / 1000);
};

// Vehicle utilities
export const getVehicleDisplayName = (vehicle: Vehicle): string => {
  return `${vehicle.year} ${vehicle.make} ${vehicle.model} (${vehicle.name})`;
};

// Expense utilities
export const categorizeExpenses = (expenses: Expense[]): Record<string, Expense[]> => {
  return expenses.reduce((acc, expense) => {
    if (!acc[expense.category]) {
      acc[expense.category] = [];
    }
    acc[expense.category].push(expense);
    return acc;
  }, {} as Record<string, Expense[]>);
};

export const calculateTotalExpenses = (expenses: Expense[]): number => {
  return expenses.reduce((total, expense) => total + expense.amount, 0);
};

export const calculateTaxDeductibleExpenses = (expenses: Expense[]): number => {
  return expenses
    .filter(expense => expense.isTaxDeductible)
    .reduce((total, expense) => total + expense.amount, 0);
};

// Error handling utilities
export const handleApiError = (error: any): string => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    const { status, data } = error.response;
    
    if (status === 401) {
      return 'Your session has expired. Please log in again.';
    }
    
    if (status === 403) {
      return 'You do not have permission to perform this action.';
    }
    
    if (status === 404) {
      return 'The requested resource was not found.';
    }
    
    if (status >= 500) {
      return 'A server error occurred. Please try again later.';
    }
    
    return data.message || 'An error occurred. Please try again.';
  } else if (error.request) {
    // The request was made but no response was received
    return 'No response from server. Please check your internet connection.';
  } else {
    // Something happened in setting up the request that triggered an Error
    return error.message || 'An unexpected error occurred.';
  }
};

// Validation utilities
export const isValidVehicle = (vehicle: Partial<Vehicle>): boolean => {
  return !!(
    vehicle.name &&
    vehicle.make &&
    vehicle.model &&
    vehicle.year &&
    vehicle.licensePlate &&
    vehicle.odometer
  );
};

export const isValidExpense = (expense: Partial<Expense>): boolean => {
  return !!(
    expense.date &&
    expense.amount &&
    expense.category &&
    expense.description &&
    expense.vehicleId
  );
};

// Export all utilities
export default {
  formatDate,
  formatTime,
  formatDateTime,
  formatDistance,
  formatDuration,
  formatCurrency,
  calculateTripDistance,
  calculateDistance,
  calculateTripDuration,
  getVehicleDisplayName,
  categorizeExpenses,
  calculateTotalExpenses,
  calculateTaxDeductibleExpenses,
  handleApiError,
  isValidVehicle,
  isValidExpense,
};