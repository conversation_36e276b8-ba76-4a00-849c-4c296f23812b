import axios from 'axios';
import { Trip, Vehicle, Expense, User, ApiResponse, PaginatedResponse } from '../types';

// API base URL - would be replaced with actual API URL in a real app
const API_BASE_URL = 'https://api.nxtacre.com/v1';

// Create axios instance with common configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    // Get token from secure storage
    // const token = await SecureStore.getItemAsync('authToken');
    const token = 'dummy-token'; // Placeholder for now
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Trip services
export const tripService = {
  getTrips: async (page = 1, limit = 20): Promise<PaginatedResponse<Trip>> => {
    try {
      const response = await api.get(`/trips?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching trips:', error);
      throw error;
    }
  },
  
  getTrip: async (tripId: string): Promise<ApiResponse<Trip>> => {
    try {
      const response = await api.get(`/trips/${tripId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching trip ${tripId}:`, error);
      throw error;
    }
  },
  
  createTrip: async (trip: Omit<Trip, 'id'>): Promise<ApiResponse<Trip>> => {
    try {
      const response = await api.post('/trips', trip);
      return response.data;
    } catch (error) {
      console.error('Error creating trip:', error);
      throw error;
    }
  },
  
  updateTrip: async (tripId: string, trip: Partial<Trip>): Promise<ApiResponse<Trip>> => {
    try {
      const response = await api.put(`/trips/${tripId}`, trip);
      return response.data;
    } catch (error) {
      console.error(`Error updating trip ${tripId}:`, error);
      throw error;
    }
  },
  
  deleteTrip: async (tripId: string): Promise<ApiResponse<null>> => {
    try {
      const response = await api.delete(`/trips/${tripId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting trip ${tripId}:`, error);
      throw error;
    }
  },
};

// Vehicle services
export const vehicleService = {
  getVehicles: async (): Promise<ApiResponse<Vehicle[]>> => {
    try {
      const response = await api.get('/vehicles');
      return response.data;
    } catch (error) {
      console.error('Error fetching vehicles:', error);
      throw error;
    }
  },
  
  getVehicle: async (vehicleId: string): Promise<ApiResponse<Vehicle>> => {
    try {
      const response = await api.get(`/vehicles/${vehicleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching vehicle ${vehicleId}:`, error);
      throw error;
    }
  },
  
  createVehicle: async (vehicle: Omit<Vehicle, 'id'>): Promise<ApiResponse<Vehicle>> => {
    try {
      const response = await api.post('/vehicles', vehicle);
      return response.data;
    } catch (error) {
      console.error('Error creating vehicle:', error);
      throw error;
    }
  },
  
  updateVehicle: async (vehicleId: string, vehicle: Partial<Vehicle>): Promise<ApiResponse<Vehicle>> => {
    try {
      const response = await api.put(`/vehicles/${vehicleId}`, vehicle);
      return response.data;
    } catch (error) {
      console.error(`Error updating vehicle ${vehicleId}:`, error);
      throw error;
    }
  },
  
  deleteVehicle: async (vehicleId: string): Promise<ApiResponse<null>> => {
    try {
      const response = await api.delete(`/vehicles/${vehicleId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting vehicle ${vehicleId}:`, error);
      throw error;
    }
  },
};

// Expense services
export const expenseService = {
  getExpenses: async (page = 1, limit = 20): Promise<PaginatedResponse<Expense>> => {
    try {
      const response = await api.get(`/expenses?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching expenses:', error);
      throw error;
    }
  },
  
  getExpense: async (expenseId: string): Promise<ApiResponse<Expense>> => {
    try {
      const response = await api.get(`/expenses/${expenseId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching expense ${expenseId}:`, error);
      throw error;
    }
  },
  
  createExpense: async (expense: Omit<Expense, 'id'>): Promise<ApiResponse<Expense>> => {
    try {
      const response = await api.post('/expenses', expense);
      return response.data;
    } catch (error) {
      console.error('Error creating expense:', error);
      throw error;
    }
  },
  
  updateExpense: async (expenseId: string, expense: Partial<Expense>): Promise<ApiResponse<Expense>> => {
    try {
      const response = await api.put(`/expenses/${expenseId}`, expense);
      return response.data;
    } catch (error) {
      console.error(`Error updating expense ${expenseId}:`, error);
      throw error;
    }
  },
  
  deleteExpense: async (expenseId: string): Promise<ApiResponse<null>> => {
    try {
      const response = await api.delete(`/expenses/${expenseId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting expense ${expenseId}:`, error);
      throw error;
    }
  },
  
  uploadReceipt: async (expenseId: string, receiptImage: string): Promise<ApiResponse<{ imageUrl: string }>> => {
    try {
      // Create form data for image upload
      const formData = new FormData();
      formData.append('receipt', {
        uri: receiptImage,
        name: 'receipt.jpg',
        type: 'image/jpeg',
      });
      
      const response = await api.post(`/expenses/${expenseId}/receipt`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      console.error(`Error uploading receipt for expense ${expenseId}:`, error);
      throw error;
    }
  },
};

// User services
export const userService = {
  getCurrentUser: async (): Promise<ApiResponse<User>> => {
    try {
      const response = await api.get('/user');
      return response.data;
    } catch (error) {
      console.error('Error fetching current user:', error);
      throw error;
    }
  },
  
  updateUserSettings: async (settings: Partial<User['settings']>): Promise<ApiResponse<User>> => {
    try {
      const response = await api.put('/user/settings', settings);
      return response.data;
    } catch (error) {
      console.error('Error updating user settings:', error);
      throw error;
    }
  },
  
  updateUserProfile: async (profile: Partial<Omit<User, 'id' | 'settings'>>): Promise<ApiResponse<User>> => {
    try {
      const response = await api.put('/user/profile', profile);
      return response.data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },
  
  uploadProfileImage: async (imageUri: string): Promise<ApiResponse<{ imageUrl: string }>> => {
    try {
      // Create form data for image upload
      const formData = new FormData();
      formData.append('profileImage', {
        uri: imageUri,
        name: 'profile.jpg',
        type: 'image/jpeg',
      });
      
      const response = await api.post('/user/profile-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('Error uploading profile image:', error);
      throw error;
    }
  },
};

// Export all services
export default {
  trip: tripService,
  vehicle: vehicleService,
  expense: expenseService,
  user: userService,
};