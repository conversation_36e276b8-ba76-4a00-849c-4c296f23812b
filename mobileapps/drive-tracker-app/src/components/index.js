import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { formatDate, formatTime, formatDistance, formatDuration, formatCurrency } from '../utils';

// Trip item component for list views
export const TripItem = ({ trip, onPress }) => {
  return (
    <TouchableOpacity style={styles.tripItem} onPress={onPress}>
      <View style={styles.tripHeader}>
        <View style={styles.tripPurposeIndicator}>
          <Ionicons 
            name={trip.purpose === 'business' ? 'briefcase' : 'home'} 
            size={16} 
            color={trip.purpose === 'business' ? '#007AFF' : '#FF9500'} 
          />
        </View>
        <Text style={styles.tripDate}>{formatDate(trip.startTime)}</Text>
      </View>
      <View style={styles.tripDetails}>
        <View style={styles.tripLocation}>
          <Ionicons name="location" size={16} color="#666" />
          <Text style={styles.tripLocationText} numberOfLines={1}>
            {trip.startLocation ? 'Start location' : 'Unknown location'}
          </Text>
        </View>
        {trip.endLocation && (
          <View style={styles.tripLocation}>
            <Ionicons name="location" size={16} color="#666" />
            <Text style={styles.tripLocationText} numberOfLines={1}>
              End location
            </Text>
          </View>
        )}
      </View>
      <View style={styles.tripFooter}>
        <View style={styles.tripStat}>
          <Ionicons name="speedometer-outline" size={16} color="#666" />
          <Text style={styles.tripStatText}>{formatDistance(trip.distance)}</Text>
        </View>
        <View style={styles.tripStat}>
          <Ionicons name="time-outline" size={16} color="#666" />
          <Text style={styles.tripStatText}>{formatDuration(trip.duration)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

// Vehicle item component for list views
export const VehicleItem = ({ vehicle, onPress }) => {
  return (
    <TouchableOpacity style={styles.vehicleItem} onPress={onPress}>
      <View style={styles.vehicleHeader}>
        <Text style={styles.vehicleName}>{vehicle.name}</Text>
        {vehicle.isActive && <View style={styles.activeIndicator} />}
      </View>
      <Text style={styles.vehicleDetails}>
        {vehicle.year} {vehicle.make} {vehicle.model}
      </Text>
      <View style={styles.vehicleFooter}>
        <View style={styles.vehicleStat}>
          <Ionicons name="speedometer-outline" size={16} color="#666" />
          <Text style={styles.vehicleStatText}>{vehicle.odometer} mi</Text>
        </View>
        <View style={styles.vehicleStat}>
          <Ionicons name="car-outline" size={16} color="#666" />
          <Text style={styles.vehicleStatText}>{vehicle.licensePlate}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

// Expense item component for list views
export const ExpenseItem = ({ expense, onPress }) => {
  return (
    <TouchableOpacity style={styles.expenseItem} onPress={onPress}>
      <View style={styles.expenseHeader}>
        <View style={styles.expenseCategory}>
          <Ionicons 
            name={getCategoryIcon(expense.category)} 
            size={20} 
            color="#007AFF" 
          />
        </View>
        <View style={styles.expenseInfo}>
          <Text style={styles.expenseDescription} numberOfLines={1}>
            {expense.description}
          </Text>
          <Text style={styles.expenseDate}>{formatDate(expense.date)}</Text>
        </View>
        <Text style={styles.expenseAmount}>
          {formatCurrency(expense.amount)}
        </Text>
      </View>
      <View style={styles.expenseFooter}>
        {expense.isTaxDeductible && (
          <View style={styles.taxDeductibleBadge}>
            <Text style={styles.taxDeductibleText}>Tax Deductible</Text>
          </View>
        )}
        {expense.receiptImage && (
          <View style={styles.receiptBadge}>
            <Ionicons name="receipt-outline" size={14} color="#666" />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

// Trip stats component for dashboard
export const TripStats = ({ trips, timeframe }) => {
  // Calculate stats based on trips and timeframe
  const totalTrips = trips.length;
  const businessTrips = trips.filter(trip => trip.purpose === 'business').length;
  const personalTrips = trips.filter(trip => trip.purpose === 'personal').length;
  const totalDistance = trips.reduce((sum, trip) => sum + trip.distance, 0);
  const businessDistance = trips
    .filter(trip => trip.purpose === 'business')
    .reduce((sum, trip) => sum + trip.distance, 0);
  
  return (
    <View style={styles.statsContainer}>
      <Text style={styles.statsTitle}>{timeframe} Trip Summary</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{totalTrips}</Text>
          <Text style={styles.statLabel}>Total Trips</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{businessTrips}</Text>
          <Text style={styles.statLabel}>Business</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{personalTrips}</Text>
          <Text style={styles.statLabel}>Personal</Text>
        </View>
      </View>
      <View style={styles.distanceContainer}>
        <View style={styles.distanceItem}>
          <Text style={styles.distanceValue}>{formatDistance(totalDistance)}</Text>
          <Text style={styles.distanceLabel}>Total Distance</Text>
        </View>
        <View style={styles.distanceItem}>
          <Text style={styles.distanceValue}>{formatDistance(businessDistance)}</Text>
          <Text style={styles.distanceLabel}>Business Distance</Text>
        </View>
      </View>
    </View>
  );
};

// Expense stats component for dashboard
export const ExpenseStats = ({ expenses, timeframe }) => {
  // Calculate stats based on expenses and timeframe
  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
  const taxDeductible = expenses
    .filter(expense => expense.isTaxDeductible)
    .reduce((sum, expense) => sum + expense.amount, 0);
  
  return (
    <View style={styles.statsContainer}>
      <Text style={styles.statsTitle}>{timeframe} Expense Summary</Text>
      <View style={styles.expenseStatsGrid}>
        <View style={styles.expenseStatItem}>
          <Text style={styles.expenseStatValue}>{formatCurrency(totalExpenses)}</Text>
          <Text style={styles.expenseStatLabel}>Total Expenses</Text>
        </View>
        <View style={styles.expenseStatItem}>
          <Text style={styles.expenseStatValue}>{formatCurrency(taxDeductible)}</Text>
          <Text style={styles.expenseStatLabel}>Tax Deductible</Text>
        </View>
      </View>
    </View>
  );
};

// Tracking toggle component for home screen
export const TrackingToggle = ({ isTracking, onToggle }) => {
  return (
    <TouchableOpacity 
      style={[styles.trackingToggle, isTracking ? styles.trackingActive : styles.trackingInactive]} 
      onPress={onToggle}
    >
      <Ionicons 
        name={isTracking ? 'pause-circle' : 'play-circle'} 
        size={24} 
        color={isTracking ? '#FFFFFF' : '#007AFF'} 
      />
      <Text style={[styles.trackingText, isTracking ? styles.trackingActiveText : styles.trackingInactiveText]}>
        {isTracking ? 'Tracking Active' : 'Start Tracking'}
      </Text>
    </TouchableOpacity>
  );
};

// Helper function to get icon for expense category
const getCategoryIcon = (category) => {
  const icons = {
    'fuel': 'gas-pump',
    'maintenance': 'construct',
    'parking': 'car',
    'tolls': 'cash',
    'food': 'restaurant',
    'lodging': 'bed',
    'office': 'briefcase',
    'other': 'ellipsis-horizontal-circle'
  };
  
  return icons[category] || 'ellipsis-horizontal-circle';
};

// Styles
const styles = StyleSheet.create({
  // Trip item styles
  tripItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tripHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tripPurposeIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  tripDate: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  tripDetails: {
    marginBottom: 8,
  },
  tripLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  tripLocationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  tripFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tripStat: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tripStatText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  
  // Vehicle item styles
  vehicleItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  vehicleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  vehicleName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  activeIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CD964',
  },
  vehicleDetails: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  vehicleFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  vehicleStat: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  vehicleStatText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  
  // Expense item styles
  expenseItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  expenseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  expenseCategory: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  expenseInfo: {
    flex: 1,
  },
  expenseDescription: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  expenseDate: {
    fontSize: 14,
    color: '#666',
  },
  expenseAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  expenseFooter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  taxDeductibleBadge: {
    backgroundColor: '#E9F0FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 8,
  },
  taxDeductibleText: {
    fontSize: 12,
    color: '#007AFF',
  },
  receiptBadge: {
    backgroundColor: '#F0F0F0',
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Stats styles
  statsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
  },
  distanceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  distanceItem: {
    alignItems: 'center',
  },
  distanceValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
  },
  distanceLabel: {
    fontSize: 12,
    color: '#666',
  },
  
  // Expense stats styles
  expenseStatsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  expenseStatItem: {
    alignItems: 'center',
  },
  expenseStatValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
  },
  expenseStatLabel: {
    fontSize: 12,
    color: '#666',
  },
  
  // Tracking toggle styles
  trackingToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  trackingActive: {
    backgroundColor: '#007AFF',
  },
  trackingInactive: {
    backgroundColor: '#E9F0FF',
  },
  trackingText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  trackingActiveText: {
    color: '#FFFFFF',
  },
  trackingInactiveText: {
    color: '#007AFF',
  },
});

// Export all components
export default {
  TripItem,
  VehicleItem,
  ExpenseItem,
  TripStats,
  ExpenseStats,
  TrackingToggle,
};