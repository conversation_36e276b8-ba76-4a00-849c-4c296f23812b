import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from 'shared';

// Placeholder component for screens
const PlaceholderScreen = ({ title }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.description}>This screen is under development.</Text>
      <Button title="Go Back" onPress={() => {}} variant="primary" style={styles.button} />
    </View>
  );
};

// Export all screen components
export { default as HomeScreen } from './HomeScreen';
export { default as TripsScreen } from './TripsScreen';
export { default as TripDetailScreen } from './TripDetailScreen';
export { default as VehiclesScreen } from './VehiclesScreen';
export { default as ExpensesScreen } from './ExpensesScreen';
export { default as ReportsScreen } from './ReportsScreen';
export { default as ProfileScreen } from './ProfileScreen';
export { default as VehicleDetailScreen } from './VehicleDetailScreen';
export { default as ExpenseDetailScreen } from './ExpenseDetailScreen';
export { default as AddExpenseScreen } from './AddExpenseScreen';
export { default as AddVehicleScreen } from './AddVehicleScreen';
export { default as SettingsScreen } from './SettingsScreen';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  button: {
    marginTop: 20,
  },
});
