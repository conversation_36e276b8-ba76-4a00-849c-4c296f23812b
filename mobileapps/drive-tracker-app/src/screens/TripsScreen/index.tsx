import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, TextInput, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, Badge } from 'shared';

// Mock data for trips
const mockTrips = [
  {
    id: '1',
    date: '2023-06-15',
    startTime: '09:30 AM',
    endTime: '10:15 AM',
    startLocation: '123 Home St',
    endLocation: '456 Office Blvd',
    distance: 12.5,
    duration: 45,
    type: 'business',
    purpose: 'Client Meeting',
    vehicle: 'Toyota Camry',
  },
  {
    id: '2',
    date: '2023-06-15',
    startTime: '05:30 PM',
    endTime: '06:15 PM',
    startLocation: '456 Office Blvd',
    endLocation: '123 Home St',
    distance: 12.5,
    duration: 45,
    type: 'business',
    purpose: 'Commute',
    vehicle: 'Toyota Camry',
  },
  {
    id: '3',
    date: '2023-06-14',
    startTime: '10:00 AM',
    endTime: '10:30 AM',
    startLocation: '123 Home St',
    endLocation: '789 Grocery St',
    distance: 5.2,
    duration: 30,
    type: 'personal',
    purpose: 'Shopping',
    vehicle: 'Toyota Camry',
  },
  {
    id: '4',
    date: '2023-06-13',
    startTime: '02:00 PM',
    endTime: '03:30 PM',
    startLocation: '123 Home St',
    endLocation: '321 Client Ave',
    distance: 28.5,
    duration: 90,
    type: 'business',
    purpose: 'Site Visit',
    vehicle: 'Toyota Camry',
  },
  {
    id: '5',
    date: '2023-06-12',
    startTime: '11:30 AM',
    endTime: '12:15 PM',
    startLocation: '123 Home St',
    endLocation: '555 Restaurant Rd',
    distance: 8.7,
    duration: 45,
    type: 'personal',
    purpose: 'Lunch',
    vehicle: 'Toyota Camry',
  },
];

const TripsScreen = () => {
  const navigation = useNavigation();
  const [trips, setTrips] = useState([]);
  const [filteredTrips, setFilteredTrips] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'business', 'personal'

  // Simulate loading trips data
  useEffect(() => {
    // In a real app, this would fetch data from a service or local storage
    setTimeout(() => {
      setTrips(mockTrips);
      setFilteredTrips(mockTrips);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter trips based on search query and filter type
  useEffect(() => {
    if (trips.length === 0) return;

    let filtered = [...trips];

    // Apply type filter
    if (filter !== 'all') {
      filtered = filtered.filter(trip => trip.type === filter);
    }

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        trip =>
          trip.startLocation.toLowerCase().includes(query) ||
          trip.endLocation.toLowerCase().includes(query) ||
          trip.purpose.toLowerCase().includes(query)
      );
    }

    setFilteredTrips(filtered);
  }, [trips, searchQuery, filter]);

  const handleTripPress = (trip) => {
    navigation.navigate('TripDetail', { tripId: trip.id });
  };

  const renderTripItem = ({ item }) => (
    <TouchableOpacity onPress={() => handleTripPress(item)}>
      <Card style={styles.tripCard}>
        <View style={styles.tripHeader}>
          <View style={styles.tripDateContainer}>
            <Text style={styles.tripDate}>{item.date}</Text>
            <Text style={styles.tripTime}>{item.startTime} - {item.endTime}</Text>
          </View>
          <Badge 
            text={item.type === 'business' ? 'Business' : 'Personal'} 
            color={item.type === 'business' ? '#007AFF' : '#FF9500'} 
          />
        </View>
        
        <View style={styles.tripDetails}>
          <View style={styles.locationContainer}>
            <View style={styles.locationItem}>
              <Ionicons name="location" size={16} color="#007AFF" />
              <Text style={styles.locationText} numberOfLines={1}>{item.startLocation}</Text>
            </View>
            <View style={styles.locationDivider}>
              <Ionicons name="arrow-down" size={12} color="#999" />
            </View>
            <View style={styles.locationItem}>
              <Ionicons name="location" size={16} color="#FF3B30" />
              <Text style={styles.locationText} numberOfLines={1}>{item.endLocation}</Text>
            </View>
          </View>
          
          <View style={styles.tripStats}>
            <View style={styles.statItem}>
              <Ionicons name="speedometer-outline" size={14} color="#666" />
              <Text style={styles.statText}>{item.distance} mi</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="time-outline" size={14} color="#666" />
              <Text style={styles.statText}>{item.duration} min</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="car-outline" size={14} color="#666" />
              <Text style={styles.statText}>{item.vehicle}</Text>
            </View>
          </View>
          
          <Text style={styles.purposeText}>{item.purpose}</Text>
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Trips</Text>
      </View>
      
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color="#999" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search trips..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery ? (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color="#999" />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
      
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
          onPress={() => setFilter('all')}
        >
          <Text style={[styles.filterText, filter === 'all' && styles.filterTextActive]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'business' && styles.filterButtonActive]}
          onPress={() => setFilter('business')}
        >
          <Text style={[styles.filterText, filter === 'business' && styles.filterTextActive]}>Business</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'personal' && styles.filterButtonActive]}
          onPress={() => setFilter('personal')}
        >
          <Text style={[styles.filterText, filter === 'personal' && styles.filterTextActive]}>Personal</Text>
        </TouchableOpacity>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading trips...</Text>
        </View>
      ) : filteredTrips.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="car-outline" size={64} color="#ccc" />
          <Text style={styles.emptyTitle}>No trips found</Text>
          <Text style={styles.emptyText}>
            {searchQuery || filter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Start tracking your trips to see them here'}
          </Text>
        </View>
      ) : (
        <FlatList
          data={filteredTrips}
          renderItem={renderTripItem}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
  },
  searchContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 24,
    fontSize: 16,
    color: '#333',
  },
  filterContainer: {
    flexDirection: 'row',
    padding: 8,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginHorizontal: 4,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
  },
  filterText: {
    fontSize: 14,
    color: '#666',
  },
  filterTextActive: {
    color: '#ffffff',
  },
  listContainer: {
    padding: 8,
  },
  tripCard: {
    marginVertical: 8,
    padding: 16,
  },
  tripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  tripDateContainer: {
    flexDirection: 'column',
  },
  tripDate: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  tripTime: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  tripDetails: {
    marginTop: 8,
  },
  locationContainer: {
    marginBottom: 12,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  locationDivider: {
    alignItems: 'center',
    marginLeft: 8,
    marginVertical: 2,
  },
  locationText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
    flex: 1,
  },
  tripStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  purposeText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
});

export default TripsScreen;