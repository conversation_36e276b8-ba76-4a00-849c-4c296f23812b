import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Switch, ScrollView, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button } from 'shared';

const HomeScreen = () => {
  const navigation = useNavigation();
  const [isTracking, setIsTracking] = useState(false);
  const [tripStats, setTripStats] = useState({
    totalTrips: 0,
    businessTrips: 0,
    personalTrips: 0,
    totalMiles: 0,
    businessMiles: 0,
    personalMiles: 0,
    totalExpenses: 0,
  });

  // Simulate loading trip statistics
  useEffect(() => {
    // In a real app, this would fetch data from a service or local storage
    setTripStats({
      totalTrips: 42,
      businessTrips: 28,
      personalTrips: 14,
      totalMiles: 1250.5,
      businessMiles: 950.2,
      personalMiles: 300.3,
      totalExpenses: 325.75,
    });
  }, []);

  const toggleTracking = () => {
    setIsTracking(!isTracking);
    // In a real app, this would start or stop location tracking services
  };

  const navigateTo = (screen) => {
    navigation.navigate(screen);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Drive Tracker</Text>
        <View style={styles.trackingContainer}>
          <Text style={styles.trackingText}>
            {isTracking ? 'Tracking Active' : 'Tracking Inactive'}
          </Text>
          <Switch
            value={isTracking}
            onValueChange={toggleTracking}
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={isTracking ? '#007AFF' : '#f4f3f4'}
          />
        </View>
      </View>

      <Card style={styles.statsCard}>
        <Text style={styles.cardTitle}>Trip Statistics</Text>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.totalTrips}</Text>
            <Text style={styles.statLabel}>Total Trips</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.businessTrips}</Text>
            <Text style={styles.statLabel}>Business</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.personalTrips}</Text>
            <Text style={styles.statLabel}>Personal</Text>
          </View>
        </View>
        <View style={styles.divider} />
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.totalMiles.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Total Miles</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.businessMiles.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Business</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{tripStats.personalMiles.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Personal</Text>
          </View>
        </View>
        <View style={styles.divider} />
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>${tripStats.totalExpenses.toFixed(2)}</Text>
            <Text style={styles.statLabel}>Total Expenses</Text>
          </View>
        </View>
      </Card>

      <Text style={styles.sectionTitle}>Quick Actions</Text>
      <View style={styles.quickActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigateTo('AddExpense')}
        >
          <View style={styles.actionIcon}>
            <Ionicons name="add-circle" size={24} color="#007AFF" />
          </View>
          <Text style={styles.actionText}>Add Expense</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigateTo('AddVehicle')}
        >
          <View style={styles.actionIcon}>
            <Ionicons name="car" size={24} color="#007AFF" />
          </View>
          <Text style={styles.actionText}>Add Vehicle</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigateTo('Trips')}
        >
          <View style={styles.actionIcon}>
            <Ionicons name="list" size={24} color="#007AFF" />
          </View>
          <Text style={styles.actionText}>View Trips</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigateTo('Reports')}
        >
          <View style={styles.actionIcon}>
            <Ionicons name="bar-chart" size={24} color="#007AFF" />
          </View>
          <Text style={styles.actionText}>Generate Report</Text>
        </TouchableOpacity>
      </View>

      <Card style={styles.recentActivityCard}>
        <Text style={styles.cardTitle}>Recent Activity</Text>
        <View style={styles.activityItem}>
          <Ionicons name="car-outline" size={20} color="#007AFF" />
          <View style={styles.activityContent}>
            <Text style={styles.activityTitle}>Trip to Client Meeting</Text>
            <Text style={styles.activityDetail}>Business • 28.5 miles • 45 min</Text>
          </View>
          <Text style={styles.activityTime}>Today</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.activityItem}>
          <Ionicons name="cash-outline" size={20} color="#007AFF" />
          <View style={styles.activityContent}>
            <Text style={styles.activityTitle}>Fuel Expense</Text>
            <Text style={styles.activityDetail}>$45.75 • Gas Station</Text>
          </View>
          <Text style={styles.activityTime}>Yesterday</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.activityItem}>
          <Ionicons name="car-outline" size={20} color="#007AFF" />
          <View style={styles.activityContent}>
            <Text style={styles.activityTitle}>Trip to Grocery Store</Text>
            <Text style={styles.activityDetail}>Personal • 5.2 miles • 15 min</Text>
          </View>
          <Text style={styles.activityTime}>Yesterday</Text>
        </View>
        <Button 
          title="View All Activity" 
          onPress={() => navigateTo('Trips')} 
          variant="text"
          style={styles.viewAllButton}
        />
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
  },
  trackingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trackingText: {
    marginRight: 8,
    fontSize: 14,
    color: '#666666',
  },
  statsCard: {
    margin: 16,
    padding: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#333333',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 8,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
  },
  divider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    color: '#333333',
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 8,
  },
  actionButton: {
    width: '48%',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionIcon: {
    marginRight: 12,
  },
  actionText: {
    fontSize: 14,
    color: '#333333',
  },
  recentActivityCard: {
    margin: 16,
    padding: 16,
    marginBottom: 32,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  activityContent: {
    flex: 1,
    marginLeft: 12,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333333',
  },
  activityDetail: {
    fontSize: 12,
    color: '#666666',
    marginTop: 2,
  },
  activityTime: {
    fontSize: 12,
    color: '#999999',
  },
  viewAllButton: {
    alignSelf: 'center',
    marginTop: 8,
  },
});

export default HomeScreen;