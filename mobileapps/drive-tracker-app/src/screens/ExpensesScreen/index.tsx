import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { Card, Button, SearchInput } from 'shared';

// Mock data for expenses
const mockExpenses = [
  {
    id: '1',
    date: '2023-06-25',
    type: 'Fuel',
    amount: 45.75,
    vehicle: {
      id: '1',
      name: 'Toyota Camry',
      licensePlate: 'ABC123',
    },
    category: 'Business',
    notes: 'Regular unleaded',
    receiptImage: null,
    taxDeductible: true,
  },
  {
    id: '2',
    date: '2023-06-20',
    type: 'Maintenance',
    amount: 65.00,
    vehicle: {
      id: '3',
      name: 'Ford F-150',
      licensePlate: 'DEF456',
    },
    category: 'Business',
    notes: 'Oil change',
    receiptImage: null,
    taxDeductible: true,
  },
  {
    id: '3',
    date: '2023-06-15',
    type: 'Parking',
    amount: 15.00,
    vehicle: {
      id: '1',
      name: 'Toyota Camry',
      licensePlate: 'ABC123',
    },
    category: 'Business',
    notes: 'Downtown parking',
    receiptImage: null,
    taxDeductible: true,
  },
  {
    id: '4',
    date: '2023-06-10',
    type: 'Toll',
    amount: 5.50,
    vehicle: {
      id: '2',
      name: 'Honda CR-V',
      licensePlate: 'XYZ789',
    },
    category: 'Personal',
    notes: 'Highway toll',
    receiptImage: null,
    taxDeductible: false,
  },
  {
    id: '5',
    date: '2023-06-05',
    type: 'Fuel',
    amount: 52.30,
    vehicle: {
      id: '2',
      name: 'Honda CR-V',
      licensePlate: 'XYZ789',
    },
    category: 'Personal',
    notes: 'Premium unleaded',
    receiptImage: null,
    taxDeductible: false,
  },
  {
    id: '6',
    date: '2023-05-30',
    type: 'Accessories',
    amount: 250.00,
    vehicle: {
      id: '3',
      name: 'Ford F-150',
      licensePlate: 'DEF456',
    },
    category: 'Business',
    notes: 'Bed liner',
    receiptImage: null,
    taxDeductible: true,
  },
  {
    id: '7',
    date: '2023-05-25',
    type: 'Insurance',
    amount: 150.00,
    vehicle: {
      id: '1',
      name: 'Toyota Camry',
      licensePlate: 'ABC123',
    },
    category: 'Business',
    notes: 'Monthly premium',
    receiptImage: null,
    taxDeductible: true,
  },
];

// Expense type icons
const expenseTypeIcons = {
  Fuel: 'gas-pump',
  Maintenance: 'wrench',
  Parking: 'car-park',
  Toll: 'road',
  Accessories: 'car-alt',
  Insurance: 'shield-alt',
  Other: 'receipt',
};

const ExpensesScreen = () => {
  const navigation = useNavigation();
  const [expenses, setExpenses] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredExpenses, setFilteredExpenses] = useState([]);
  const [activeFilter, setActiveFilter] = useState('all'); // 'all', 'business', 'personal'

  // Simulate loading expenses
  useEffect(() => {
    // In a real app, this would fetch data from a service or local storage
    setExpenses(mockExpenses);
    setFilteredExpenses(mockExpenses);
  }, []);

  // Filter expenses based on search query and active filter
  useEffect(() => {
    let filtered = expenses;
    
    // Apply category filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(
        (expense) => expense.category.toLowerCase() === activeFilter.toLowerCase()
      );
    }
    
    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (expense) =>
          expense.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
          expense.vehicle.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          expense.notes.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    setFilteredExpenses(filtered);
  }, [searchQuery, activeFilter, expenses]);

  const handleSearch = (text) => {
    setSearchQuery(text);
  };

  const navigateToExpenseDetail = (expenseId) => {
    navigation.navigate('ExpenseDetail', { expenseId });
  };

  const navigateToAddExpense = () => {
    navigation.navigate('AddExpense');
  };

  const calculateTotalAmount = (expenses) => {
    return expenses.reduce((total, expense) => total + expense.amount, 0);
  };

  const renderExpenseItem = ({ item }) => (
    <TouchableOpacity onPress={() => navigateToExpenseDetail(item.id)}>
      <Card style={styles.expenseCard}>
        <View style={styles.expenseHeader}>
          <View style={styles.expenseTypeContainer}>
            <Ionicons 
              name={expenseTypeIcons[item.type] || 'receipt-outline'} 
              size={20} 
              color="#007AFF" 
            />
          </View>
          <View style={styles.expenseInfo}>
            <Text style={styles.expenseType}>{item.type}</Text>
            <Text style={styles.expenseDate}>{new Date(item.date).toLocaleDateString()}</Text>
          </View>
          <View style={styles.expenseAmount}>
            <Text style={styles.amountText}>${item.amount.toFixed(2)}</Text>
            {item.taxDeductible && (
              <View style={styles.taxDeductibleBadge}>
                <Text style={styles.taxDeductibleText}>Tax</Text>
              </View>
            )}
          </View>
        </View>
        <View style={styles.expenseDetails}>
          <View style={styles.detailItem}>
            <Ionicons name="car-outline" size={16} color="#666" />
            <Text style={styles.detailText}>{item.vehicle.name} ({item.vehicle.licensePlate})</Text>
          </View>
          {item.notes && (
            <View style={styles.detailItem}>
              <Ionicons name="create-outline" size={16} color="#666" />
              <Text style={styles.detailText}>{item.notes}</Text>
            </View>
          )}
          <View style={styles.categoryBadge} style={[
            styles.categoryBadge,
            item.category === 'Business' ? styles.businessBadge : styles.personalBadge
          ]}>
            <Text style={styles.categoryText}>{item.category}</Text>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Expenses</Text>
        <TouchableOpacity onPress={navigateToAddExpense} style={styles.addButton}>
          <Ionicons name="add" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>
      
      <View style={styles.searchContainer}>
        <SearchInput
          placeholder="Search expenses..."
          value={searchQuery}
          onChangeText={handleSearch}
          style={styles.searchInput}
        />
      </View>
      
      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterButton, activeFilter === 'all' && styles.activeFilterButton]} 
          onPress={() => setActiveFilter('all')}
        >
          <Text style={[styles.filterText, activeFilter === 'all' && styles.activeFilterText]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, activeFilter === 'business' && styles.activeFilterButton]} 
          onPress={() => setActiveFilter('business')}
        >
          <Text style={[styles.filterText, activeFilter === 'business' && styles.activeFilterText]}>Business</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.filterButton, activeFilter === 'personal' && styles.activeFilterButton]} 
          onPress={() => setActiveFilter('personal')}
        >
          <Text style={[styles.filterText, activeFilter === 'personal' && styles.activeFilterText]}>Personal</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.summaryContainer}>
        <Card style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>${calculateTotalAmount(filteredExpenses).toFixed(2)}</Text>
              <Text style={styles.summaryLabel}>Total</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>{filteredExpenses.length}</Text>
              <Text style={styles.summaryLabel}>Expenses</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>
                ${calculateTotalAmount(filteredExpenses.filter(e => e.taxDeductible)).toFixed(2)}
              </Text>
              <Text style={styles.summaryLabel}>Tax Deductible</Text>
            </View>
          </View>
        </Card>
      </View>
      
      {filteredExpenses.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="receipt" size={64} color="#ccc" />
          <Text style={styles.emptyText}>No expenses found</Text>
          <Button 
            title="Add Expense" 
            onPress={navigateToAddExpense} 
            variant="primary"
            style={styles.addExpenseButton}
          />
        </View>
      ) : (
        <FlatList
          data={filteredExpenses}
          renderItem={renderExpenseItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
  },
  addButton: {
    padding: 8,
  },
  searchContainer: {
    padding: 16,
    paddingTop: 8,
    paddingBottom: 8,
  },
  searchInput: {
    backgroundColor: '#ffffff',
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  activeFilterButton: {
    backgroundColor: '#007AFF',
  },
  filterText: {
    fontSize: 14,
    color: '#666666',
  },
  activeFilterText: {
    color: '#ffffff',
    fontWeight: '500',
  },
  summaryContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  summaryCard: {
    padding: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
  },
  listContainer: {
    padding: 16,
    paddingTop: 8,
  },
  expenseCard: {
    marginBottom: 16,
    padding: 16,
  },
  expenseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  expenseTypeContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  expenseInfo: {
    flex: 1,
  },
  expenseType: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  expenseDate: {
    fontSize: 14,
    color: '#666666',
    marginTop: 2,
  },
  expenseAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
  },
  taxDeductibleBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginTop: 4,
  },
  taxDeductibleText: {
    fontSize: 10,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  expenseDetails: {
    marginTop: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  detailText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
  },
  categoryBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  businessBadge: {
    backgroundColor: '#007AFF',
  },
  personalBadge: {
    backgroundColor: '#FF9500',
  },
  categoryText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
    marginBottom: 24,
  },
  addExpenseButton: {
    width: 200,
  },
});

export default ExpensesScreen;