import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { Button, TextInput, Select, Header } from 'shared';

// Validation schema for the form
const VehicleSchema = Yup.object().shape({
  name: Yup.string().required('Vehicle name is required'),
  make: Yup.string().required('Make is required'),
  model: Yup.string().required('Model is required'),
  year: Yup.number()
    .required('Year is required')
    .min(1900, 'Year must be at least 1900')
    .max(new Date().getFullYear() + 1, `Year cannot be greater than ${new Date().getFullYear() + 1}`),
  licensePlate: Yup.string().required('License plate is required'),
  vin: Yup.string(),
  type: Yup.string().required('Vehicle type is required'),
  initialOdometer: Yup.number().required('Initial odometer reading is required'),
  fuelType: Yup.string(),
  insuranceProvider: Yup.string(),
  insurancePolicyNumber: Yup.string(),
  notes: Yup.string(),
});

// Vehicle types for the dropdown
const vehicleTypes = [
  { label: 'Car', value: 'car' },
  { label: 'Truck', value: 'truck' },
  { label: 'SUV', value: 'suv' },
  { label: 'Van', value: 'van' },
  { label: 'Motorcycle', value: 'motorcycle' },
  { label: 'Other', value: 'other' },
];

// Fuel types for the dropdown
const fuelTypes = [
  { label: 'Gasoline', value: 'gasoline' },
  { label: 'Diesel', value: 'diesel' },
  { label: 'Electric', value: 'electric' },
  { label: 'Hybrid', value: 'hybrid' },
  { label: 'Other', value: 'other' },
];

const AddVehicleScreen = () => {
  const navigation = useNavigation();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initial values for the form
  const initialValues = {
    name: '',
    make: '',
    model: '',
    year: '',
    licensePlate: '',
    vin: '',
    type: '',
    initialOdometer: '',
    fuelType: '',
    insuranceProvider: '',
    insurancePolicyNumber: '',
    notes: '',
  };

  // Handle form submission
  const handleSubmit = async (values) => {
    setIsSubmitting(true);
    try {
      // Here you would typically call an API to save the vehicle
      console.log('Saving vehicle:', values);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message
      Alert.alert(
        'Success',
        'Vehicle added successfully',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error saving vehicle:', error);
      Alert.alert('Error', 'Failed to add vehicle. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <Header title="Add Vehicle" showBackButton onBackPress={() => navigation.goBack()} />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView style={styles.scrollView}>
          <Formik
            initialValues={initialValues}
            validationSchema={VehicleSchema}
            onSubmit={handleSubmit}
          >
            {({ handleChange, handleBlur, handleSubmit, values, errors, touched, setFieldValue }) => (
              <View style={styles.formContainer}>
                <Text style={styles.sectionTitle}>Vehicle Information</Text>
                
                <TextInput
                  label="Vehicle Name"
                  placeholder="e.g., My Work Truck"
                  value={values.name}
                  onChangeText={handleChange('name')}
                  onBlur={handleBlur('name')}
                  error={touched.name && errors.name}
                  required
                />
                
                <TextInput
                  label="Make"
                  placeholder="e.g., Ford"
                  value={values.make}
                  onChangeText={handleChange('make')}
                  onBlur={handleBlur('make')}
                  error={touched.make && errors.make}
                  required
                />
                
                <TextInput
                  label="Model"
                  placeholder="e.g., F-150"
                  value={values.model}
                  onChangeText={handleChange('model')}
                  onBlur={handleBlur('model')}
                  error={touched.model && errors.model}
                  required
                />
                
                <TextInput
                  label="Year"
                  placeholder="e.g., 2022"
                  value={values.year}
                  onChangeText={handleChange('year')}
                  onBlur={handleBlur('year')}
                  error={touched.year && errors.year}
                  keyboardType="numeric"
                  required
                />
                
                <TextInput
                  label="License Plate"
                  placeholder="e.g., ABC123"
                  value={values.licensePlate}
                  onChangeText={handleChange('licensePlate')}
                  onBlur={handleBlur('licensePlate')}
                  error={touched.licensePlate && errors.licensePlate}
                  required
                />
                
                <TextInput
                  label="VIN"
                  placeholder="Vehicle Identification Number"
                  value={values.vin}
                  onChangeText={handleChange('vin')}
                  onBlur={handleBlur('vin')}
                  error={touched.vin && errors.vin}
                />
                
                <Select
                  label="Vehicle Type"
                  placeholder="Select vehicle type"
                  options={vehicleTypes}
                  value={values.type}
                  onValueChange={(value) => setFieldValue('type', value)}
                  error={touched.type && errors.type}
                  required
                />
                
                <TextInput
                  label="Initial Odometer Reading"
                  placeholder="e.g., 10000"
                  value={values.initialOdometer}
                  onChangeText={handleChange('initialOdometer')}
                  onBlur={handleBlur('initialOdometer')}
                  error={touched.initialOdometer && errors.initialOdometer}
                  keyboardType="numeric"
                  required
                />
                
                <Select
                  label="Fuel Type"
                  placeholder="Select fuel type"
                  options={fuelTypes}
                  value={values.fuelType}
                  onValueChange={(value) => setFieldValue('fuelType', value)}
                  error={touched.fuelType && errors.fuelType}
                />
                
                <Text style={styles.sectionTitle}>Insurance Information</Text>
                
                <TextInput
                  label="Insurance Provider"
                  placeholder="e.g., State Farm"
                  value={values.insuranceProvider}
                  onChangeText={handleChange('insuranceProvider')}
                  onBlur={handleBlur('insuranceProvider')}
                  error={touched.insuranceProvider && errors.insuranceProvider}
                />
                
                <TextInput
                  label="Insurance Policy Number"
                  placeholder="e.g., POL123456789"
                  value={values.insurancePolicyNumber}
                  onChangeText={handleChange('insurancePolicyNumber')}
                  onBlur={handleBlur('insurancePolicyNumber')}
                  error={touched.insurancePolicyNumber && errors.insurancePolicyNumber}
                />
                
                <Text style={styles.sectionTitle}>Additional Information</Text>
                
                <TextInput
                  label="Notes"
                  placeholder="Any additional information about this vehicle"
                  value={values.notes}
                  onChangeText={handleChange('notes')}
                  onBlur={handleBlur('notes')}
                  error={touched.notes && errors.notes}
                  multiline
                  numberOfLines={4}
                />
                
                <View style={styles.buttonContainer}>
                  <Button
                    title="Cancel"
                    onPress={() => navigation.goBack()}
                    variant="secondary"
                    style={styles.button}
                  />
                  <Button
                    title="Save Vehicle"
                    onPress={handleSubmit}
                    variant="primary"
                    style={styles.button}
                    loading={isSubmitting}
                    disabled={isSubmitting}
                  />
                </View>
              </View>
            )}
          </Formik>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    color: '#333',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    marginBottom: 32,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default AddVehicleScreen;