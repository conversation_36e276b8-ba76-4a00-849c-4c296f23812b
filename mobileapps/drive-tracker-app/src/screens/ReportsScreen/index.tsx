import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, DatePicker } from 'shared';
import { Ionicons } from '@expo/vector-icons';

// Report types
const reportTypes = [
  {
    id: 'trips',
    title: 'Trip Report',
    description: 'Summary of all trips with distances and purposes',
    icon: 'car-outline',
  },
  {
    id: 'expenses',
    title: 'Expense Report',
    description: 'Detailed breakdown of all expenses by category',
    icon: 'cash-outline',
  },
  {
    id: 'mileage',
    title: 'Mileage Report',
    description: 'Total mileage with tax deduction calculations',
    icon: 'speedometer-outline',
  },
  {
    id: 'tax',
    title: 'Tax Summary',
    description: 'Tax-related summary for deduction purposes',
    icon: 'document-text-outline',
  },
  {
    id: 'vehicle',
    title: 'Vehicle Report',
    description: 'Vehicle usage and expense breakdown',
    icon: 'car-sport-outline',
  },
];

// Export formats
const exportFormats = [
  { id: 'pdf', title: 'PDF', icon: 'document-outline' },
  { id: 'csv', title: 'CSV', icon: 'grid-outline' },
  { id: 'excel', title: 'Excel', icon: 'calculator-outline' },
];

const ReportsScreen = () => {
  const navigation = useNavigation();
  const [selectedReportType, setSelectedReportType] = useState(null);
  const [startDate, setStartDate] = useState(new Date(new Date().getFullYear(), new Date().getMonth(), 1)); // First day of current month
  const [endDate, setEndDate] = useState(new Date());
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState('pdf');
  const [generatedReport, setGeneratedReport] = useState(null);

  // Handle report generation
  const generateReport = async () => {
    if (!selectedReportType) {
      return;
    }

    setIsGenerating(true);
    setGeneratedReport(null);

    try {
      // Simulate API call to generate report
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock generated report data
      setGeneratedReport({
        type: selectedReportType,
        startDate,
        endDate,
        format: selectedFormat,
        url: 'https://example.com/report.pdf',
        summary: {
          totalItems: 42,
          totalAmount: 1250.75,
          taxDeductible: 875.50,
        },
      });
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle report type selection
  const selectReportType = (reportType) => {
    setSelectedReportType(reportType);
    setGeneratedReport(null);
  };

  // Handle export format selection
  const selectExportFormat = (format) => {
    setSelectedFormat(format);
  };

  // Render report type selection
  const renderReportTypeSelection = () => {
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Report Type</Text>
        <Text style={styles.sectionDescription}>Select the type of report you want to generate</Text>
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.reportTypesContainer}>
          {reportTypes.map((reportType) => (
            <TouchableOpacity
              key={reportType.id}
              style={[
                styles.reportTypeCard,
                selectedReportType?.id === reportType.id && styles.selectedReportTypeCard,
              ]}
              onPress={() => selectReportType(reportType)}
            >
              <Ionicons
                name={reportType.icon}
                size={32}
                color={selectedReportType?.id === reportType.id ? '#ffffff' : '#333333'}
              />
              <Text
                style={[
                  styles.reportTypeTitle,
                  selectedReportType?.id === reportType.id && styles.selectedReportTypeText,
                ]}
              >
                {reportType.title}
              </Text>
              <Text
                style={[
                  styles.reportTypeDescription,
                  selectedReportType?.id === reportType.id && styles.selectedReportTypeText,
                ]}
                numberOfLines={2}
              >
                {reportType.description}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // Render date range selection
  const renderDateRangeSelection = () => {
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Date Range</Text>
        <Text style={styles.sectionDescription}>Select the period for your report</Text>
        
        <View style={styles.dateRangeContainer}>
          <View style={styles.datePickerContainer}>
            <Text style={styles.datePickerLabel}>Start Date</Text>
            <DatePicker
              date={startDate}
              onDateChange={setStartDate}
              maximumDate={endDate}
            />
          </View>
          
          <View style={styles.datePickerContainer}>
            <Text style={styles.datePickerLabel}>End Date</Text>
            <DatePicker
              date={endDate}
              onDateChange={setEndDate}
              minimumDate={startDate}
              maximumDate={new Date()}
            />
          </View>
        </View>
        
        <View style={styles.quickDateButtons}>
          <Button
            title="This Month"
            onPress={() => {
              const now = new Date();
              setStartDate(new Date(now.getFullYear(), now.getMonth(), 1));
              setEndDate(new Date());
            }}
            variant="secondary"
            size="small"
            style={styles.quickDateButton}
          />
          <Button
            title="Last Month"
            onPress={() => {
              const now = new Date();
              setStartDate(new Date(now.getFullYear(), now.getMonth() - 1, 1));
              setEndDate(new Date(now.getFullYear(), now.getMonth(), 0));
            }}
            variant="secondary"
            size="small"
            style={styles.quickDateButton}
          />
          <Button
            title="This Year"
            onPress={() => {
              const now = new Date();
              setStartDate(new Date(now.getFullYear(), 0, 1));
              setEndDate(new Date());
            }}
            variant="secondary"
            size="small"
            style={styles.quickDateButton}
          />
        </View>
      </View>
    );
  };

  // Render export format selection
  const renderExportFormatSelection = () => {
    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Export Format</Text>
        <Text style={styles.sectionDescription}>Choose your preferred format</Text>
        
        <View style={styles.exportFormatsContainer}>
          {exportFormats.map((format) => (
            <TouchableOpacity
              key={format.id}
              style={[
                styles.exportFormatButton,
                selectedFormat === format.id && styles.selectedExportFormatButton,
              ]}
              onPress={() => selectExportFormat(format.id)}
            >
              <Ionicons
                name={format.icon}
                size={24}
                color={selectedFormat === format.id ? '#ffffff' : '#333333'}
              />
              <Text
                style={[
                  styles.exportFormatText,
                  selectedFormat === format.id && styles.selectedExportFormatText,
                ]}
              >
                {format.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  // Render generated report
  const renderGeneratedReport = () => {
    if (!generatedReport) {
      return null;
    }

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Generated Report</Text>
        
        <Card style={styles.reportCard}>
          <View style={styles.reportHeader}>
            <Ionicons
              name={reportTypes.find(r => r.id === generatedReport.type.id)?.icon || 'document-outline'}
              size={32}
              color="#4a90e2"
            />
            <View style={styles.reportHeaderText}>
              <Text style={styles.reportTitle}>{generatedReport.type.title}</Text>
              <Text style={styles.reportDateRange}>
                {new Date(generatedReport.startDate).toLocaleDateString()} - {new Date(generatedReport.endDate).toLocaleDateString()}
              </Text>
            </View>
          </View>
          
          <View style={styles.reportSummary}>
            <View style={styles.reportSummaryItem}>
              <Text style={styles.reportSummaryLabel}>Total Items</Text>
              <Text style={styles.reportSummaryValue}>{generatedReport.summary.totalItems}</Text>
            </View>
            <View style={styles.reportSummaryItem}>
              <Text style={styles.reportSummaryLabel}>Total Amount</Text>
              <Text style={styles.reportSummaryValue}>${generatedReport.summary.totalAmount.toFixed(2)}</Text>
            </View>
            <View style={styles.reportSummaryItem}>
              <Text style={styles.reportSummaryLabel}>Tax Deductible</Text>
              <Text style={styles.reportSummaryValue}>${generatedReport.summary.taxDeductible.toFixed(2)}</Text>
            </View>
          </View>
          
          <View style={styles.reportActions}>
            <Button
              title="View Report"
              onPress={() => {
                // Handle viewing the report
                console.log('Viewing report:', generatedReport.url);
              }}
              variant="primary"
              style={styles.reportActionButton}
            />
            <Button
              title="Share"
              onPress={() => {
                // Handle sharing the report
                console.log('Sharing report:', generatedReport.url);
              }}
              variant="secondary"
              style={styles.reportActionButton}
            />
          </View>
        </Card>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Header title="Reports" />
      
      <ScrollView style={styles.scrollView}>
        {renderReportTypeSelection()}
        {renderDateRangeSelection()}
        {renderExportFormatSelection()}
        
        <View style={styles.generateButtonContainer}>
          <Button
            title={isGenerating ? 'Generating...' : 'Generate Report'}
            onPress={generateReport}
            variant="primary"
            disabled={!selectedReportType || isGenerating}
            loading={isGenerating}
            style={styles.generateButton}
          />
        </View>
        
        {renderGeneratedReport()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  reportTypesContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  reportTypeCard: {
    width: 160,
    height: 140,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedReportTypeCard: {
    backgroundColor: '#4a90e2',
  },
  reportTypeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 4,
    textAlign: 'center',
    color: '#333',
  },
  reportTypeDescription: {
    fontSize: 12,
    textAlign: 'center',
    color: '#666',
  },
  selectedReportTypeText: {
    color: '#ffffff',
  },
  dateRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  datePickerContainer: {
    flex: 1,
    marginRight: 8,
  },
  datePickerLabel: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  quickDateButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  quickDateButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  exportFormatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  exportFormatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    flex: 1,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedExportFormatButton: {
    backgroundColor: '#4a90e2',
  },
  exportFormatText: {
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  selectedExportFormatText: {
    color: '#ffffff',
  },
  generateButtonContainer: {
    marginBottom: 24,
  },
  generateButton: {
    height: 50,
  },
  reportCard: {
    padding: 16,
    borderRadius: 12,
  },
  reportHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  reportHeaderText: {
    marginLeft: 12,
    flex: 1,
  },
  reportTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  reportDateRange: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  reportSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    padding: 12,
  },
  reportSummaryItem: {
    alignItems: 'center',
  },
  reportSummaryLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  reportSummaryValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  reportActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  reportActionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default ReportsScreen;