{"name": "shared", "version": "1.0.0", "description": "Shared code for NxtAcre mobile apps", "main": "index.js", "scripts": {"test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "1.21.0", "expo": "^53.0.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.2", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@expo/vector-icons": "^14.0.0", "axios": "^1.4.0", "zustand": "^4.3.9", "date-fns": "^2.30.0", "lodash": "^4.17.21", "yup": "^1.2.0", "formik": "^2.4.3"}, "devDependencies": {"@types/react": "~18.2.14", "@types/lodash": "^4.14.196", "typescript": "^5.1.3"}}