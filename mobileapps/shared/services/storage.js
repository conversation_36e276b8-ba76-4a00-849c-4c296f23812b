/**
 * Storage Service
 * 
 * This service provides a unified interface for storing and retrieving data locally
 * using AsyncStorage. It handles data persistence and provides methods for CRUD operations.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from './logger';

// Prefix for all storage keys to avoid conflicts
const KEY_PREFIX = 'nxtacre:';

/**
 * Initialize the storage system
 */
const initDatabase = async () => {
  try {
    // Create sync_queue if it doesn't exist
    const syncQueue = await AsyncStorage.getItem(`${KEY_PREFIX}sync_queue`);
    if (!syncQueue) {
      await AsyncStorage.setItem(`${KEY_PREFIX}sync_queue`, JSON.stringify([]));
    }
    logger.info('Storage initialized successfully');
    return Promise.resolve();
  } catch (error) {
    logger.error('Error initializing storage:', error);
    return Promise.reject(error);
  }
};

/**
 * Get all items from a collection
 * @param {string} collection - Collection name
 * @param {Function} filterFn - Optional filter function
 * @returns {Promise} - Promise that resolves with the items
 */
const getAll = async (collection, filterFn = null) => {
  try {
    const data = await AsyncStorage.getItem(`${KEY_PREFIX}${collection}`);
    const items = data ? JSON.parse(data) : [];

    if (filterFn && typeof filterFn === 'function') {
      return items.filter(filterFn);
    }

    return items;
  } catch (error) {
    logger.error(`Error getting all items from ${collection}:`, error);
    return [];
  }
};

/**
 * Get a single item from a collection by ID
 * @param {string} collection - Collection name
 * @param {string} id - Item ID
 * @returns {Promise} - Promise that resolves with the item or null if not found
 */
const getById = async (collection, id) => {
  try {
    const items = await getAll(collection);
    return items.find(item => item.id === id) || null;
  } catch (error) {
    logger.error(`Error getting item by ID from ${collection}:`, error);
    return null;
  }
};

/**
 * Insert an item into a collection
 * @param {string} collection - Collection name
 * @param {Object} item - Item to insert
 * @returns {Promise} - Promise that resolves with the inserted item
 */
const insert = async (collection, item) => {
  try {
    const items = await getAll(collection);

    // Ensure item has an ID
    if (!item.id) {
      item.id = Date.now().toString();
    }

    items.push(item);
    await AsyncStorage.setItem(`${KEY_PREFIX}${collection}`, JSON.stringify(items));

    return item.id;
  } catch (error) {
    logger.error(`Error inserting item into ${collection}:`, error);
    throw error;
  }
};

/**
 * Update an item in a collection
 * @param {string} collection - Collection name
 * @param {string} id - Item ID
 * @param {Object} updatedItem - Updated item data
 * @returns {Promise} - Promise that resolves with the number of items affected
 */
const update = async (collection, id, updatedItem) => {
  try {
    const items = await getAll(collection);
    const index = items.findIndex(item => item.id === id);

    if (index === -1) {
      return 0;
    }

    // Update the item while preserving its ID
    items[index] = { ...items[index], ...updatedItem, id };

    await AsyncStorage.setItem(`${KEY_PREFIX}${collection}`, JSON.stringify(items));

    return 1;
  } catch (error) {
    logger.error(`Error updating item in ${collection}:`, error);
    throw error;
  }
};

/**
 * Delete an item from a collection
 * @param {string} collection - Collection name
 * @param {string} id - Item ID
 * @returns {Promise} - Promise that resolves with the number of items affected
 */
const remove = async (collection, id) => {
  try {
    const items = await getAll(collection);
    const initialLength = items.length;

    const filteredItems = items.filter(item => item.id !== id);

    if (filteredItems.length === initialLength) {
      return 0;
    }

    await AsyncStorage.setItem(`${KEY_PREFIX}${collection}`, JSON.stringify(filteredItems));

    return 1;
  } catch (error) {
    logger.error(`Error removing item from ${collection}:`, error);
    throw error;
  }
};

/**
 * Create a new collection
 * @param {string} collection - Collection name
 * @returns {Promise} - Promise that resolves when the collection is created
 */
const createTable = async (collection) => {
  try {
    const exists = await AsyncStorage.getItem(`${KEY_PREFIX}${collection}`);

    if (!exists) {
      await AsyncStorage.setItem(`${KEY_PREFIX}${collection}`, JSON.stringify([]));
    }

    return Promise.resolve();
  } catch (error) {
    logger.error(`Error creating collection ${collection}:`, error);
    throw error;
  }
};

/**
 * Drop a collection
 * @param {string} collection - Collection name
 * @returns {Promise} - Promise that resolves when the collection is dropped
 */
const dropTable = async (collection) => {
  try {
    await AsyncStorage.removeItem(`${KEY_PREFIX}${collection}`);
    return Promise.resolve();
  } catch (error) {
    logger.error(`Error dropping collection ${collection}:`, error);
    throw error;
  }
};

/**
 * Clear all data from a collection
 * @param {string} collection - Collection name
 * @returns {Promise} - Promise that resolves when the collection is cleared
 */
const clearTable = async (collection) => {
  try {
    await AsyncStorage.setItem(`${KEY_PREFIX}${collection}`, JSON.stringify([]));
    return Promise.resolve();
  } catch (error) {
    logger.error(`Error clearing collection ${collection}:`, error);
    throw error;
  }
};

export const storage = {
  initDatabase,
  getAll,
  getById,
  insert,
  update,
  remove,
  createTable,
  dropTable,
  clearTable,
};
