/**
 * API Service
 * 
 * This service provides a unified interface for making API requests.
 * It handles authentication, error handling, and request formatting.
 */

import { logger } from './logger';

// Base API URL
const API_BASE_URL = process.env.API_BASE_URL || 'https://api.nxtacre.com/v1';

// Default request timeout in milliseconds
const DEFAULT_TIMEOUT = 30000;

// Default headers
const defaultHeaders = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

// Authentication token
let authToken = null;

/**
 * Set the authentication token
 * @param {string} token - JWT token
 */
const setAuthToken = (token) => {
  authToken = token;
};

/**
 * Get the authentication token
 * @returns {string|null} - JWT token
 */
const getAuthToken = () => {
  return authToken;
};

/**
 * Clear the authentication token
 */
const clearAuthToken = () => {
  authToken = null;
};

/**
 * Get headers for a request
 * @param {Object} additionalHeaders - Additional headers to include
 * @returns {Object} - Headers object
 */
const getHeaders = (additionalHeaders = {}) => {
  const headers = { ...defaultHeaders, ...additionalHeaders };
  
  if (authToken) {
    headers['Authorization'] = `Bearer ${authToken}`;
  }
  
  return headers;
};

/**
 * Make an API request
 * @param {string} endpoint - API endpoint
 * @param {string} method - HTTP method (GET, POST, PUT, DELETE)
 * @param {Object} data - Request data
 * @param {Object} options - Additional options
 * @returns {Promise} - Promise that resolves with the response data
 */
const request = (endpoint, method = 'GET', data = null, options = {}) => {
  const url = endpoint.startsWith('http') ? endpoint : `${API_BASE_URL}${endpoint}`;
  const headers = getHeaders(options.headers);
  const timeout = options.timeout || DEFAULT_TIMEOUT;
  
  const config = {
    method,
    headers,
    ...(data && method !== 'GET' ? { body: JSON.stringify(data) } : {}),
  };
  
  // Log the request
  logger.debug(`API Request: ${method} ${url}`, { headers, data });
  
  // Create a timeout promise
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Request timeout after ${timeout}ms`));
    }, timeout);
  });
  
  // Create the fetch promise
  const fetchPromise = fetch(url, config)
    .then(response => {
      // Log the response
      logger.debug(`API Response: ${response.status} ${response.statusText}`);
      
      // Check if the response is ok
      if (!response.ok) {
        return response.json()
          .then(errorData => {
            throw {
              status: response.status,
              statusText: response.statusText,
              data: errorData,
            };
          })
          .catch(error => {
            // If parsing JSON fails, throw a simpler error
            if (error instanceof SyntaxError) {
              throw {
                status: response.status,
                statusText: response.statusText,
                data: { message: 'Invalid response format' },
              };
            }
            throw error;
          });
      }
      
      // Check if the response is empty
      if (response.status === 204) {
        return null;
      }
      
      // Parse the response as JSON
      return response.json();
    })
    .catch(error => {
      // Log the error
      logger.error('API Error:', error);
      
      // Rethrow the error
      throw error;
    });
  
  // Race the fetch promise against the timeout promise
  return Promise.race([fetchPromise, timeoutPromise]);
};

/**
 * Make a GET request
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @param {Object} options - Additional options
 * @returns {Promise} - Promise that resolves with the response data
 */
const get = (endpoint, params = {}, options = {}) => {
  const queryString = Object.keys(params)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&');
  
  const url = queryString ? `${endpoint}?${queryString}` : endpoint;
  
  return request(url, 'GET', null, options);
};

/**
 * Make a POST request
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request data
 * @param {Object} options - Additional options
 * @returns {Promise} - Promise that resolves with the response data
 */
const post = (endpoint, data = {}, options = {}) => {
  return request(endpoint, 'POST', data, options);
};

/**
 * Make a PUT request
 * @param {string} endpoint - API endpoint
 * @param {Object} data - Request data
 * @param {Object} options - Additional options
 * @returns {Promise} - Promise that resolves with the response data
 */
const put = (endpoint, data = {}, options = {}) => {
  return request(endpoint, 'PUT', data, options);
};

/**
 * Make a DELETE request
 * @param {string} endpoint - API endpoint
 * @param {Object} options - Additional options
 * @returns {Promise} - Promise that resolves with the response data
 */
const del = (endpoint, options = {}) => {
  return request(endpoint, 'DELETE', null, options);
};

export const api = {
  setAuthToken,
  getAuthToken,
  clearAuthToken,
  request,
  get,
  post,
  put,
  delete: del,
};