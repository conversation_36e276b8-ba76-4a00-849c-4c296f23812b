/**
 * Connectivity Service
 * 
 * This service provides functionality to monitor network connectivity status
 * and notify subscribers when the status changes.
 */

import NetInfo from '@react-native-community/netinfo';
import { logger } from './logger';

// Subscribers to connectivity changes
const subscribers = [];

// Current connectivity status
let isConnected = true;

/**
 * Initialize the connectivity monitoring
 */
const init = () => {
  // Subscribe to network info changes
  const unsubscribe = NetInfo.addEventListener(state => {
    const newConnectionStatus = state.isConnected && state.isInternetReachable;
    
    // Only notify if the status has changed
    if (newConnectionStatus !== isConnected) {
      isConnected = newConnectionStatus;
      logger.info(`Connectivity changed: ${isConnected ? 'online' : 'offline'}`);
      
      // Notify all subscribers
      notifySubscribers();
    }
  });
  
  // Initial check
  checkConnection();
  
  return unsubscribe;
};

/**
 * Check the current connection status
 * @returns {Promise<boolean>} - Promise that resolves with the connection status
 */
const checkConnection = async () => {
  try {
    const state = await NetInfo.fetch();
    isConnected = state.isConnected && state.isInternetReachable;
    return isConnected;
  } catch (error) {
    logger.error('Error checking connection:', error);
    return false;
  }
};

/**
 * Get the current connection status
 * @returns {boolean} - Current connection status
 */
const getConnectionStatus = () => {
  return isConnected;
};

/**
 * Subscribe to connectivity changes
 * @param {Function} callback - Function to call when connectivity changes
 * @returns {Function} - Unsubscribe function
 */
const subscribe = (callback) => {
  subscribers.push(callback);
  
  // Return unsubscribe function
  return () => {
    const index = subscribers.indexOf(callback);
    if (index !== -1) {
      subscribers.splice(index, 1);
    }
  };
};

/**
 * Notify all subscribers of connectivity changes
 */
const notifySubscribers = () => {
  subscribers.forEach(callback => {
    try {
      callback(isConnected);
    } catch (error) {
      logger.error('Error in connectivity subscriber callback:', error);
    }
  });
};

export const connectivity = {
  init,
  checkConnection,
  getConnectionStatus,
  subscribe,
};