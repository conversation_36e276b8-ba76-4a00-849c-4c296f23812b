/**
 * Sync Service
 * 
 * This service handles synchronization of data between the local database and the server.
 * It provides functionality for queuing API requests when offline and processing them
 * when the device comes back online.
 */

import { v4 as uuidv4 } from 'uuid';
import { storage } from './storage';
import { connectivity } from './connectivity';
import { api } from './api';
import { logger } from './logger';

// Sync status
let isSyncing = false;
let syncCancelled = false;
let pendingItemsCount = 0;
let processedItemsCount = 0;
let syncProgress = 0;

// Subscribers to sync status changes
const subscribers = [];

/**
 * Initialize the sync service
 */
const init = () => {
  // Subscribe to connectivity changes
  connectivity.subscribe(isConnected => {
    if (isConnected) {
      // When device comes online, process the sync queue
      processSyncQueue();
    }
  });
  
  // Initialize the database
  return storage.initDatabase()
    .then(() => {
      logger.info('Sync service initialized');
      
      // Check for pending items
      return getPendingItemsCount();
    })
    .then(count => {
      pendingItemsCount = count;
      notifySubscribers();
      
      // If there are pending items and we're online, process them
      if (pendingItemsCount > 0 && connectivity.getConnectionStatus()) {
        processSyncQueue();
      }
    })
    .catch(error => {
      logger.error('Error initializing sync service:', error);
    });
};

/**
 * Queue an API request for later processing
 * @param {string} endpoint - API endpoint
 * @param {string} method - HTTP method (GET, POST, PUT, DELETE)
 * @param {Object} data - Request data
 * @returns {Promise} - Promise that resolves with the queued item ID
 */
const queueRequest = (endpoint, method, data = null) => {
  const id = uuidv4();
  const item = {
    id,
    endpoint,
    method,
    data: data ? JSON.stringify(data) : null,
    created_at: Date.now(),
    status: 'pending',
    retry_count: 0
  };
  
  return storage.insert('sync_queue', item)
    .then(() => {
      pendingItemsCount++;
      notifySubscribers();
      
      // If we're online, process the queue immediately
      if (connectivity.getConnectionStatus() && !isSyncing) {
        processSyncQueue();
      }
      
      return id;
    });
};

/**
 * Process the sync queue
 * @returns {Promise} - Promise that resolves when the queue is processed
 */
const processSyncQueue = async () => {
  // If already syncing, don't start another sync process
  if (isSyncing) {
    return;
  }
  
  isSyncing = true;
  syncCancelled = false;
  processedItemsCount = 0;
  syncProgress = 0;
  notifySubscribers();
  
  try {
    // Get all pending items
    const items = await storage.getAll('sync_queue', "status = 'pending'");
    pendingItemsCount = items.length;
    
    if (pendingItemsCount === 0) {
      isSyncing = false;
      notifySubscribers();
      return;
    }
    
    logger.info(`Processing sync queue: ${pendingItemsCount} items`);
    
    // Process each item
    for (const item of items) {
      // If sync was cancelled, stop processing
      if (syncCancelled) {
        logger.info('Sync cancelled');
        break;
      }
      
      // If we're offline, stop processing
      if (!connectivity.getConnectionStatus()) {
        logger.info('Device went offline, pausing sync');
        break;
      }
      
      try {
        // Parse the data if it exists
        const data = item.data ? JSON.parse(item.data) : null;
        
        // Make the API request
        await api.request(item.endpoint, item.method, data);
        
        // Mark the item as processed
        await storage.update('sync_queue', item.id, { status: 'processed' });
        
        processedItemsCount++;
        syncProgress = (processedItemsCount / pendingItemsCount) * 100;
        notifySubscribers();
      } catch (error) {
        logger.error(`Error processing sync item ${item.id}:`, error);
        
        // Increment retry count
        const retryCount = item.retry_count + 1;
        
        // If we've reached the maximum retry count, mark as failed
        if (retryCount >= 3) {
          await storage.update('sync_queue', item.id, { 
            status: 'failed', 
            retry_count: retryCount 
          });
        } else {
          // Otherwise, increment the retry count
          await storage.update('sync_queue', item.id, { retry_count: retryCount });
        }
      }
    }
  } catch (error) {
    logger.error('Error processing sync queue:', error);
  } finally {
    // Update pending items count
    const count = await getPendingItemsCount();
    pendingItemsCount = count;
    
    isSyncing = false;
    notifySubscribers();
  }
};

/**
 * Cancel the current sync process
 */
const cancelSync = () => {
  if (isSyncing) {
    syncCancelled = true;
    notifySubscribers();
  }
};

/**
 * Get the count of pending items in the sync queue
 * @returns {Promise<number>} - Promise that resolves with the count
 */
const getPendingItemsCount = () => {
  return storage.executeQuery("SELECT COUNT(*) as count FROM sync_queue WHERE status = 'pending'")
    .then(result => {
      if (result.rows.length > 0) {
        return result.rows._array[0].count;
      }
      return 0;
    });
};

/**
 * Get the current sync status
 * @returns {Object} - Sync status object
 */
const getSyncStatus = () => {
  return {
    isSyncing,
    pendingItemsCount,
    processedItemsCount,
    syncProgress,
    syncCancelled
  };
};

/**
 * Subscribe to sync status changes
 * @param {Function} callback - Function to call when sync status changes
 * @returns {Function} - Unsubscribe function
 */
const subscribe = (callback) => {
  subscribers.push(callback);
  
  // Return unsubscribe function
  return () => {
    const index = subscribers.indexOf(callback);
    if (index !== -1) {
      subscribers.splice(index, 1);
    }
  };
};

/**
 * Notify all subscribers of sync status changes
 */
const notifySubscribers = () => {
  const status = getSyncStatus();
  
  subscribers.forEach(callback => {
    try {
      callback(status);
    } catch (error) {
      logger.error('Error in sync subscriber callback:', error);
    }
  });
};

/**
 * Clear processed items from the sync queue
 * @returns {Promise} - Promise that resolves when the items are cleared
 */
const clearProcessedItems = () => {
  return storage.executeQuery("DELETE FROM sync_queue WHERE status = 'processed'")
    .then(() => {
      logger.info('Cleared processed items from sync queue');
    });
};

/**
 * Reset failed items to pending
 * @returns {Promise} - Promise that resolves when the items are reset
 */
const resetFailedItems = () => {
  return storage.executeQuery("UPDATE sync_queue SET status = 'pending', retry_count = 0 WHERE status = 'failed'")
    .then(() => {
      logger.info('Reset failed items to pending');
      return getPendingItemsCount();
    })
    .then(count => {
      pendingItemsCount = count;
      notifySubscribers();
      
      // If we're online, process the queue immediately
      if (connectivity.getConnectionStatus() && !isSyncing) {
        processSyncQueue();
      }
    });
};

/**
 * Prioritize a specific item in the sync queue
 * @param {string} id - Item ID
 * @returns {Promise} - Promise that resolves when the item is prioritized
 */
const prioritizeItem = (id) => {
  // Update the created_at timestamp to be the current time
  // This will make it appear first in the queue when sorted by created_at
  return storage.update('sync_queue', id, { created_at: Date.now() })
    .then(() => {
      logger.info(`Prioritized sync item ${id}`);
    });
};

export const sync = {
  init,
  queueRequest,
  processSyncQueue,
  cancelSync,
  getPendingItemsCount,
  getSyncStatus,
  subscribe,
  clearProcessedItems,
  resetFailedItems,
  prioritizeItem
};