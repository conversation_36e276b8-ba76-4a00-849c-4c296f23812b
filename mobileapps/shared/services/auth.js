/**
 * Auth Service
 * 
 * This service provides authentication functionality for the application.
 * It handles user login, logout, registration, and token management.
 */

import * as SecureStore from 'expo-secure-store';
import { api } from './api';
import { logger } from './logger';

// Keys for storing auth data
const AUTH_TOKEN_KEY = 'auth_token';
const USER_DATA_KEY = 'user_data';

// Current user data
let currentUser = null;

/**
 * Initialize the auth service
 * @returns {Promise} - Promise that resolves when initialization is complete
 */
const init = async () => {
  try {
    // Load token from secure storage
    const token = await SecureStore.getItemAsync(AUTH_TOKEN_KEY);
    if (token) {
      api.setAuthToken(token);
      
      // Load user data from secure storage
      const userData = await SecureStore.getItemAsync(USER_DATA_KEY);
      if (userData) {
        currentUser = JSON.parse(userData);
      } else {
        // If we have a token but no user data, fetch the user data
        await fetchCurrentUser();
      }
    }
    
    logger.info('Auth service initialized');
    return true;
  } catch (error) {
    logger.error('Error initializing auth service:', error);
    return false;
  }
};

/**
 * Login a user
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise} - Promise that resolves with the user data
 */
const login = async (email, password) => {
  try {
    const response = await api.post('/auth/login', { email, password });
    
    // Save token and set it in the API service
    await SecureStore.setItemAsync(AUTH_TOKEN_KEY, response.token);
    api.setAuthToken(response.token);
    
    // Save user data
    currentUser = response.user;
    await SecureStore.setItemAsync(USER_DATA_KEY, JSON.stringify(currentUser));
    
    logger.info('User logged in:', currentUser.email);
    return currentUser;
  } catch (error) {
    logger.error('Login error:', error);
    throw error;
  }
};

/**
 * Register a new user
 * @param {Object} userData - User data
 * @returns {Promise} - Promise that resolves with the user data
 */
const register = async (userData) => {
  try {
    const response = await api.post('/auth/register', userData);
    
    // Save token and set it in the API service
    await SecureStore.setItemAsync(AUTH_TOKEN_KEY, response.token);
    api.setAuthToken(response.token);
    
    // Save user data
    currentUser = response.user;
    await SecureStore.setItemAsync(USER_DATA_KEY, JSON.stringify(currentUser));
    
    logger.info('User registered:', currentUser.email);
    return currentUser;
  } catch (error) {
    logger.error('Registration error:', error);
    throw error;
  }
};

/**
 * Logout the current user
 * @returns {Promise} - Promise that resolves when logout is complete
 */
const logout = async () => {
  try {
    // Clear token and user data
    await SecureStore.deleteItemAsync(AUTH_TOKEN_KEY);
    await SecureStore.deleteItemAsync(USER_DATA_KEY);
    api.clearAuthToken();
    currentUser = null;
    
    logger.info('User logged out');
    return true;
  } catch (error) {
    logger.error('Logout error:', error);
    throw error;
  }
};

/**
 * Fetch the current user data
 * @returns {Promise} - Promise that resolves with the user data
 */
const fetchCurrentUser = async () => {
  try {
    const user = await api.get('/auth/me');
    currentUser = user;
    await SecureStore.setItemAsync(USER_DATA_KEY, JSON.stringify(currentUser));
    return currentUser;
  } catch (error) {
    logger.error('Error fetching current user:', error);
    throw error;
  }
};

/**
 * Get the current user data
 * @returns {Object|null} - Current user data
 */
const getCurrentUser = () => {
  return currentUser;
};

/**
 * Check if a user is logged in
 * @returns {boolean} - Whether a user is logged in
 */
const isLoggedIn = () => {
  return !!currentUser;
};

/**
 * Update the current user data
 * @param {Object} userData - Updated user data
 * @returns {Promise} - Promise that resolves with the updated user data
 */
const updateUser = async (userData) => {
  try {
    const updatedUser = await api.put('/auth/me', userData);
    currentUser = updatedUser;
    await SecureStore.setItemAsync(USER_DATA_KEY, JSON.stringify(currentUser));
    
    logger.info('User updated:', currentUser.email);
    return currentUser;
  } catch (error) {
    logger.error('Error updating user:', error);
    throw error;
  }
};

/**
 * Change the current user's password
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @returns {Promise} - Promise that resolves when the password is changed
 */
const changePassword = async (currentPassword, newPassword) => {
  try {
    await api.post('/auth/change-password', {
      currentPassword,
      newPassword,
    });
    
    logger.info('Password changed');
    return true;
  } catch (error) {
    logger.error('Error changing password:', error);
    throw error;
  }
};

/**
 * Request a password reset
 * @param {string} email - User email
 * @returns {Promise} - Promise that resolves when the reset is requested
 */
const requestPasswordReset = async (email) => {
  try {
    await api.post('/auth/forgot-password', { email });
    
    logger.info('Password reset requested for:', email);
    return true;
  } catch (error) {
    logger.error('Error requesting password reset:', error);
    throw error;
  }
};

/**
 * Reset a password with a token
 * @param {string} token - Reset token
 * @param {string} newPassword - New password
 * @returns {Promise} - Promise that resolves when the password is reset
 */
const resetPassword = async (token, newPassword) => {
  try {
    await api.post('/auth/reset-password', {
      token,
      newPassword,
    });
    
    logger.info('Password reset completed');
    return true;
  } catch (error) {
    logger.error('Error resetting password:', error);
    throw error;
  }
};

export const auth = {
  init,
  login,
  register,
  logout,
  fetchCurrentUser,
  getCurrentUser,
  isLoggedIn,
  updateUser,
  changePassword,
  requestPasswordReset,
  resetPassword,
};