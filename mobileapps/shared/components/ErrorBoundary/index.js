import React, { Component } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from '../Text';
import { Button } from '../Button';

/**
 * ErrorBoundary component for catching and displaying errors in the component tree
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Components to be wrapped by the error boundary
 * @param {Function} props.onError - Function to call when an error is caught
 * @param {string} props.fallbackTitle - Title to display in the fallback UI
 * @param {string} props.fallbackMessage - Message to display in the fallback UI
 * @param {string} props.resetButtonText - Text for the reset button
 */
export class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    
    this.setState({ errorInfo });
    
    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  resetError = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
  };

  render() {
    const { 
      children, 
      fallbackTitle = 'Something went wrong', 
      fallbackMessage = 'We encountered an error while trying to display this content.',
      resetButtonText = 'Try Again'
    } = this.props;
    
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <View style={styles.container}>
          <Text variant="h2" style={styles.title}>{fallbackTitle}</Text>
          <Text style={styles.message}>{fallbackMessage}</Text>
          {__DEV__ && this.state.error && (
            <View style={styles.errorContainer}>
              <Text variant="caption" style={styles.errorText}>
                {this.state.error.toString()}
              </Text>
            </View>
          )}
          <Button
            title={resetButtonText}
            onPress={this.resetError}
            style={styles.button}
          />
        </View>
      );
    }

    return children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
    backgroundColor: '#FFFFFF',
  },
  title: {
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    marginBottom: 24,
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#F8F8F8',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    width: '100%',
  },
  errorText: {
    color: '#FF3B30',
  },
  button: {
    minWidth: 150,
  },
});

export default ErrorBoundary;