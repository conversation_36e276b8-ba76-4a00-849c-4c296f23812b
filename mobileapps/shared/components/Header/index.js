import React, { useEffect } from 'react';
import { View, StyleSheet, Animated, useColorScheme } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Text } from '../Text';
import { HeaderButton } from './HeaderButton';

/**
 * Enhanced Header component for screen headers with navigation and action buttons
 * @param {Object} props - Component props
 * @param {string} props.title - Header title
 * @param {string} props.subtitle - Optional subtitle to display below the title
 * @param {Function} props.onBackPress - Function to call when back button is pressed
 * @param {boolean} props.showBack - Whether to show the back button
 * @param {React.ReactNode} props.leftComponent - Component to display on the left side (overrides back button)
 * @param {string} props.leftIcon - Name of Ionicons icon to display on the left (overrides back button)
 * @param {Function} props.onLeftPress - Function to call when left icon is pressed
 * @param {React.ReactNode} props.rightComponent - Component to display on the right side
 * @param {Function} props.onRightPress - Function to call when right button is pressed
 * @param {string} props.rightIcon - Name of Ionicons icon to display on the right
 * @param {string} props.rightText - Text to display on the right side
 * @param {Object} props.style - Additional styles for the header
 * @param {string} props.variant - Header style variant ('primary', 'secondary', 'transparent')
 * @param {boolean} props.elevated - Whether to show elevation shadow
 * @param {boolean} props.animated - Whether to animate the header on mount
 * @param {string} props.backgroundColor - Background color for the header (overrides variant)
 * @param {string} props.textColor - Text color for the header (overrides variant)
 */
export const Header = ({
  title,
  subtitle,
  onBackPress,
  showBack = false,
  leftComponent,
  leftIcon,
  onLeftPress,
  rightComponent,
  onRightPress,
  rightIcon,
  rightText,
  style,
  variant = 'primary',
  elevated = true,
  animated = false,
  backgroundColor,
  textColor,
}) => {
  // Animation value for fade-in effect
  const fadeAnim = new Animated.Value(animated ? 0 : 1);

  // Get device color scheme for dark mode support
  const colorScheme = useColorScheme();
  const isDarkMode = colorScheme === 'dark';

  // Determine colors based on variant and color scheme
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          bg: backgroundColor || (isDarkMode ? '#1C1C1E' : '#FFFFFF'),
          text: textColor || (isDarkMode ? '#FFFFFF' : '#000000'),
          border: isDarkMode ? '#2C2C2E' : '#E1E1E1',
          tint: '#007AFF'
        };
      case 'secondary':
        return {
          bg: backgroundColor || (isDarkMode ? '#2C2C2E' : '#F2F2F7'),
          text: textColor || (isDarkMode ? '#FFFFFF' : '#000000'),
          border: isDarkMode ? '#3C3C3E' : '#D1D1D6',
          tint: '#007AFF'
        };
      case 'transparent':
        return {
          bg: 'transparent',
          text: textColor || (isDarkMode ? '#FFFFFF' : '#000000'),
          border: 'transparent',
          tint: '#007AFF'
        };
      default:
        return {
          bg: backgroundColor || (isDarkMode ? '#1C1C1E' : '#FFFFFF'),
          text: textColor || (isDarkMode ? '#FFFFFF' : '#000000'),
          border: isDarkMode ? '#2C2C2E' : '#E1E1E1',
          tint: '#007AFF'
        };
    }
  };

  const variantStyles = getVariantStyles();

  // Run animation on mount if animated is true
  useEffect(() => {
    if (animated) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [animated, fadeAnim]);

  // Determine shadow styles based on elevated prop and color scheme
  const shadowStyle = elevated ? (
    isDarkMode ? styles.darkShadow : styles.lightShadow
  ) : {};

  return (
    <Animated.View 
      style={[
        styles.header, 
        { 
          backgroundColor: variantStyles.bg,
          borderBottomColor: variantStyles.border,
        },
        shadowStyle,
        { opacity: fadeAnim },
        style
      ]}
    >
      <View style={styles.leftContainer}>
        {leftComponent ? (
          leftComponent
        ) : leftIcon ? (
          <HeaderButton 
            icon={leftIcon} 
            onPress={onLeftPress} 
            color={variantStyles.tint}
          />
        ) : showBack ? (
          <HeaderButton 
            icon="chevron-back" 
            onPress={onBackPress} 
            color={variantStyles.tint}
          />
        ) : null}
      </View>

      <View style={styles.titleContainer}>
        <Text 
          variant="h3" 
          style={[styles.title, { color: variantStyles.text }]} 
          numberOfLines={1}
        >
          {title}
        </Text>
        {subtitle && (
          <Text 
            variant="body2" 
            style={[styles.subtitle, { color: variantStyles.text + '99' }]} 
            numberOfLines={1}
          >
            {subtitle}
          </Text>
        )}
      </View>

      <View style={styles.rightContainer}>
        {rightComponent ? (
          rightComponent
        ) : rightIcon ? (
          <HeaderButton 
            icon={rightIcon} 
            onPress={onRightPress} 
            color={variantStyles.tint}
          />
        ) : rightText ? (
          <HeaderButton 
            text={rightText} 
            onPress={onRightPress} 
            color={variantStyles.tint}
          />
        ) : null}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  header: {
    height: 60,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  leftContainer: {
    width: 60,
    alignItems: 'flex-start',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rightContainer: {
    width: 60,
    alignItems: 'flex-end',
  },
  title: {
    marginBottom: 0,
  },
  subtitle: {
    marginTop: 2,
    fontSize: 12,
  },
  lightShadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  darkShadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
  },
});

export default Header;

// Re-export HeaderButton for use in other components
export { HeaderButton } from './HeaderButton';
