import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, TouchableOpacity, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Text } from '../Text';

const { width } = Dimensions.get('window');

/**
 * Notification component for displaying toast-style notifications
 * @param {Object} props - Component props
 * @param {boolean} props.visible - Whether the notification is visible
 * @param {string} props.type - Notification type ('success', 'error', 'warning', 'info')
 * @param {string} props.title - Notification title
 * @param {string} props.message - Notification message
 * @param {Function} props.onClose - Function to call when the notification is closed
 * @param {number} props.duration - Duration in milliseconds before auto-hiding (0 for no auto-hide)
 * @param {string} props.position - Position of the notification ('top', 'bottom')
 * @param {Object} props.style - Additional styles for the notification
 */
export const Notification = ({
  visible = false,
  type = 'info',
  title,
  message,
  onClose,
  duration = 3000,
  position = 'top',
  style,
}) => {
  const translateY = useRef(new Animated.Value(position === 'top' ? -100 : 100)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const timeout = useRef(null);

  const getIconName = () => {
    switch (type) {
      case 'success':
        return 'checkmark-circle';
      case 'error':
        return 'alert-circle';
      case 'warning':
        return 'warning';
      case 'info':
        return 'information-circle';
      default:
        return 'information-circle';
    }
  };

  const getIconColor = () => {
    switch (type) {
      case 'success':
        return '#4CD964';
      case 'error':
        return '#FF3B30';
      case 'warning':
        return '#FFCC00';
      case 'info':
        return '#5AC8FA';
      default:
        return '#5AC8FA';
    }
  };

  const showNotification = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    if (duration > 0) {
      timeout.current = setTimeout(() => {
        hideNotification();
      }, duration);
    }
  };

  const hideNotification = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: position === 'top' ? -100 : 100,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      if (onClose) {
        onClose();
      }
    });

    if (timeout.current) {
      clearTimeout(timeout.current);
    }
  };

  useEffect(() => {
    if (visible) {
      showNotification();
    } else {
      hideNotification();
    }

    return () => {
      if (timeout.current) {
        clearTimeout(timeout.current);
      }
    };
  }, [visible]);

  if (!visible && opacity._value === 0) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        position === 'top' ? styles.topPosition : styles.bottomPosition,
        {
          transform: [{ translateY }],
          opacity,
        },
        style,
      ]}
    >
      <View style={styles.iconContainer}>
        <Ionicons name={getIconName()} size={24} color={getIconColor()} />
      </View>
      <View style={styles.textContainer}>
        {title && (
          <Text variant="label" style={styles.title}>
            {title}
          </Text>
        )}
        {message && (
          <Text variant="caption" style={styles.message}>
            {message}
          </Text>
        )}
      </View>
      <TouchableOpacity onPress={hideNotification} style={styles.closeButton}>
        <Ionicons name="close" size={20} color="#999999" />
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    width: width - 32,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1000,
    left: 16,
  },
  topPosition: {
    top: 50,
  },
  bottomPosition: {
    bottom: 50,
  },
  iconContainer: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    marginBottom: 2,
  },
  message: {
    color: '#666666',
  },
  closeButton: {
    padding: 4,
  },
});

export default Notification;