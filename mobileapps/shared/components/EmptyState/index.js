import React from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { Text } from '../Text';
import { Button } from '../Button';

/**
 * EmptyState component for displaying when there is no data to show
 * @param {Object} props - Component props
 * @param {string} props.title - Title text for the empty state
 * @param {string} props.message - Message text for the empty state
 * @param {string} props.buttonText - Text for the action button
 * @param {Function} props.onButtonPress - Function to call when the button is pressed
 * @param {Object} props.image - Image source for the empty state illustration
 * @param {Object} props.style - Additional styles for the container
 * @param {boolean} props.showButton - Whether to show the action button
 */
export const EmptyState = ({
  title,
  message,
  buttonText,
  onButtonPress,
  image,
  style,
  showButton = true,
}) => {
  return (
    <View style={[styles.container, style]}>
      {image && (
        <Image
          source={image}
          style={styles.image}
          resizeMode="contain"
        />
      )}
      {title && (
        <Text variant="h3" style={styles.title}>
          {title}
        </Text>
      )}
      {message && (
        <Text variant="body" style={styles.message}>
          {message}
        </Text>
      )}
      {showButton && buttonText && onButtonPress && (
        <Button
          title={buttonText}
          onPress={onButtonPress}
          style={styles.button}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24,
  },
  image: {
    width: 150,
    height: 150,
    marginBottom: 24,
  },
  title: {
    marginBottom: 8,
    textAlign: 'center',
  },
  message: {
    marginBottom: 24,
    textAlign: 'center',
    color: '#666666',
  },
  button: {
    minWidth: 150,
  },
});

export default EmptyState;