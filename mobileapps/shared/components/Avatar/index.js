import React from 'react';
import { View, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from '../Text';
import { Ionicons } from '@expo/vector-icons';

/**
 * Avatar component for displaying user avatars
 * @param {Object} props - Component props
 * @param {string} props.source - Image source for the avatar
 * @param {string} props.name - User name for generating initials when no image is available
 * @param {string} props.size - Avatar size ('small', 'medium', 'large')
 * @param {string} props.backgroundColor - Background color for the avatar when no image is available
 * @param {Function} props.onPress - Function to call when avatar is pressed
 * @param {boolean} props.showBadge - Whether to show a badge on the avatar
 * @param {string} props.badgeColor - Color of the badge
 * @param {Object} props.style - Additional styles for the avatar
 */
export const Avatar = ({
  source,
  name,
  size = 'medium',
  backgroundColor = '#007AFF',
  onPress,
  showBadge = false,
  badgeColor = '#4CD964',
  style,
  ...rest
}) => {
  const getSize = () => {
    switch (size) {
      case 'small':
        return 32;
      case 'medium':
        return 48;
      case 'large':
        return 64;
      default:
        return 48;
    }
  };

  const avatarSize = getSize();
  const fontSize = avatarSize / 2.5;
  
  const getInitials = (name) => {
    if (!name) return '';
    
    const names = name.split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  };

  const AvatarComponent = onPress ? TouchableOpacity : View;

  return (
    <AvatarComponent
      style={[
        styles.container,
        { width: avatarSize, height: avatarSize },
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.7}
      {...rest}
    >
      {source ? (
        <Image
          source={source}
          style={[
            styles.image,
            { width: avatarSize, height: avatarSize, borderRadius: avatarSize / 2 },
          ]}
        />
      ) : name ? (
        <View
          style={[
            styles.initialsContainer,
            {
              width: avatarSize,
              height: avatarSize,
              borderRadius: avatarSize / 2,
              backgroundColor,
            },
          ]}
        >
          <Text style={[styles.initials, { fontSize }]} color="#FFFFFF">
            {getInitials(name)}
          </Text>
        </View>
      ) : (
        <View
          style={[
            styles.iconContainer,
            {
              width: avatarSize,
              height: avatarSize,
              borderRadius: avatarSize / 2,
              backgroundColor: '#E1E1E1',
            },
          ]}
        >
          <Ionicons name="person" size={avatarSize / 2} color="#A1A1A1" />
        </View>
      )}
      
      {showBadge && (
        <View
          style={[
            styles.badge,
            { backgroundColor: badgeColor },
            size === 'small' ? styles.badgeSmall : {},
          ]}
        />
      )}
    </AvatarComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  initialsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  initials: {
    fontWeight: 'bold',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  badge: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: '#FFFFFF',
    bottom: 0,
    right: 0,
  },
  badgeSmall: {
    width: 8,
    height: 8,
    borderRadius: 4,
    borderWidth: 1,
  },
});

export default Avatar;