# NxtAcre Arduino Modules

This directory contains Arduino code for various hardware modules used with the NxtAcre Farm Management Platform.

## Available Modules

### [Heltec Wireless Tracker (ESP32S3)](./heltec_wireless_tracker/)

A wireless tracker module using the Heltec ESP32S3 board with BLE and LoRaWAN capabilities. This module:
- Connects to the NxtAcre mobile app via BLE
- Communicates with other trackers via LoRaWAN
- Provides GNSS location data
- Enables device-to-device messaging between farmers

### [Weather Station (ESP32)](./weather_station/)

A weather station module using an ESP32 board with WiFi capabilities. This module:
- Collects environmental data (temperature, humidity, pressure)
- Measures rainfall and wind (speed and direction)
- Monitors power status (battery level and solar voltage)
- Sends data to the NxtAcre server via WiFi

## Adding New Modules

When adding new Arduino modules to this directory, please follow these guidelines:

1. Create a new subdirectory with a descriptive name
2. Include a main sketch file (.ino) with the same name as the directory
3. Separate functionality into header files for better organization
4. Include a README.md with documentation
5. Use a consistent coding style with the existing modules

## Development Guidelines

- Use meaningful variable and function names
- Add comments to explain complex logic
- Keep functions small and focused on a single task
- Use configuration files to make modules easily customizable
- Include debug output options for troubleshooting
- Follow Arduino best practices for memory management

## Testing

Before submitting new modules or changes:

1. Test the code on the actual hardware
2. Verify all features work as expected
3. Check for memory leaks or performance issues
4. Ensure the code compiles without warnings

## Contributing

Contributions to improve existing modules or add new ones are welcome. Please submit a pull request with your changes.
