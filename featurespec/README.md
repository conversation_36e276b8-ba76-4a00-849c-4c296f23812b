# NxtAcre Farm Management Platform - Deployment Guide

This document provides instructions for deploying the NxtAcre Farm Management Platform to Digital Ocean App Platform.

## Project Structure

The application consists of:
- A React frontend built with Vite
- An Express.js backend server
- PostgreSQL database

## Deployment Files

The following files have been added/modified to support deployment to Digital Ocean App Platform:

1. `Dockerfile` - Defines how to build and run the application
2. `.dockerignore` - Specifies files to exclude from the Docker build
3. `.do/app.yaml` - Digital Ocean App Platform configuration
4. `vite.config.ts` - Updated with production build settings
5. `server/index.js` - Modified to serve static files in production

## Deployment Steps

### 1. Set Up Digital Ocean App Platform

1. Create a Digital Ocean account if you don't have one
2. Install the Digital Ocean CLI (doctl) and authenticate:
   ```
   doctl auth init
   ```

### 2. Create Required Secrets

Before deploying, you need to create secrets for sensitive environment variables:

```bash
doctl apps create-secret --app-name nxtacre-webapp --key JWT_SECRET --value "your-jwt-secret"
doctl apps create-secret --app-name nxtacre-webapp --key JWT_REFRESH_SECRET --value "your-refresh-secret"
doctl apps create-secret --app-name nxtacre-webapp --key PLAID_CLIENT_ID --value "your-plaid-client-id"
doctl apps create-secret --app-name nxtacre-webapp --key PLAID_SECRET --value "your-plaid-secret"
doctl apps create-secret --app-name nxtacre-webapp --key EMAIL_USER --value "your-email-user"
doctl apps create-secret --app-name nxtacre-webapp --key EMAIL_PASSWORD --value "your-email-password"
doctl apps create-secret --app-name nxtacre-webapp --key VITE_GOOGLE_MAPS_API_KEY --value "your-google-maps-api-key"
doctl apps create-secret --app-name nxtacre-webapp --key GOOGLE_CLIENT_ID --value "your-google-client-id"
doctl apps create-secret --app-name nxtacre-webapp --key GOOGLE_CLIENT_SECRET --value "your-google-client-secret"
doctl apps create-secret --app-name nxtacre-webapp --key DROPBOX_APP_KEY --value "your-dropbox-app-key"
doctl apps create-secret --app-name nxtacre-webapp --key DROPBOX_APP_SECRET --value "your-dropbox-app-secret"
doctl apps create-secret --app-name nxtacre-webapp --key CRON_SECRET_KEY --value "your-cron-secret-key"
```

### 3. Deploy the Application

You can deploy the application using the Digital Ocean CLI:

```bash
doctl apps create --spec .do/app.yaml
```

Or you can deploy directly from the Digital Ocean App Platform dashboard:

1. Go to the Digital Ocean App Platform dashboard
2. Click "Create App"
3. Select your GitHub repository
4. Configure the app according to the settings in `.do/app.yaml`
5. Deploy the app

### 4. Configure Custom Domains

After deployment, you can configure custom domains for your application:

1. Go to the Digital Ocean App Platform dashboard
2. Select your app
3. Go to the "Domains" tab
4. Add your custom domains (e.g., app.nxtacre.com, api.nxtacre.com)

## Environment Variables

The application uses the following environment variables:

- `NODE_ENV` - Set to "production" for production deployment
- `PORT` - The port the server will run on (default: 3002)
- `DB_*` - Database connection settings
- `JWT_*` - JWT authentication settings
- `PLAID_*` - Plaid API settings
- `EMAIL_*` - Email service settings
- `FRONTEND_URL` - URL of the frontend application
- `API_URL` - URL of the API server
- `VITE_*` - Frontend environment variables
- `GOOGLE_*` - Google API settings
- `DROPBOX_*` - Dropbox API settings
- `CRON_SECRET_KEY` - Secret key for cron jobs

## Replacing Mock Data with Real Data

The application uses mock data as a fallback when API calls fail or when API keys are not configured. To use real data instead of mock data, you need to obtain and configure the following API keys in the `.env` file:

1. **Agricultural Grants and Loans APIs**:
   - GRANTS_GOV_API_KEY - For Grants.gov data
   - FARMERS_GOV_API_KEY - For Farmers.gov data
   - USDA_ARMS_API_KEY - For USDA ARMS data
   - FARM_SERVICE_AGENCY_API_KEY - For FSA data
   - RURAL_DEVELOPMENT_API_KEY - For Rural Development data
   - NRCS_API_KEY - For NRCS soil data
   - NIFA_API_KEY - For NIFA data
   - RMA_API_KEY - For RMA data
   - AMS_API_KEY - For AMS market data

2. **Market Price APIs**:
   - DATA_GOV_API_KEY - For Data.gov USDA AMS market price data

3. **AI Assistant**:
   - OPENAI_API_KEY - For OpenAI API integration

You can obtain these API keys from the respective websites. Once you have the API keys, update the `.env` file by replacing the placeholder values with the real API keys.

The application will automatically use the real data from the APIs when the API keys are properly configured. If an API call fails or if an API key is not configured, the application will fall back to using mock data.

## Troubleshooting

If you encounter issues with the deployment:

1. Check the application logs in the Digital Ocean App Platform dashboard
2. Verify that all required environment variables are set
3. Ensure the database is properly configured and accessible
4. Check that the health check endpoint (/api/health) is responding correctly

### Common Deployment Issues and Solutions

#### Health Check Failures
- The app.yaml file now includes enhanced health check settings with an initial delay of 30 seconds to give the application time to start up before health checks begin.
- If health checks still fail, you may need to increase the `initial_delay_seconds` value in the app.yaml file.

#### Database Connection Issues
- Ensure the database cluster is running and accessible
- Verify that the database name, user, and schema match what's configured in app.yaml
- Check that the database connection variables (${db.HOSTNAME}, etc.) are being properly resolved

#### Missing Environment Variables
- All required environment variables are now included in the app.yaml file
- If you add new features that require additional environment variables, make sure to add them to app.yaml as well
- For sensitive values, use the `type: SECRET` setting and create the secrets using the Digital Ocean CLI or dashboard

#### GitHub Repository Configuration
- Ensure the GitHub repository is correctly configured in app.yaml with the proper repository name and branch
- Verify that Digital Ocean has access to the repository

#### Start Command Issues
- The application uses a dedicated "start:prod" script for production deployment
- The Dockerfile and app.yaml are configured to use this script
- If you see errors like "concurrently not found", ensure that the run_command in app.yaml is set to "npm run start:prod"
- Development dependencies like concurrently are not installed in production, so the regular "start" script won't work

#### Test Data Issues
- The application requires test data for certain functionality
- The Dockerfile is configured to create test data during the build process
- The .dockerignore file is configured to include the test/data directory
- If you see errors like "ENOENT: no such file or directory, open './test/data/05-versions-space.pdf'", ensure that:
  1. The test/data directory is not excluded in .dockerignore
  2. The setup-test-data script is run during the Docker build process
  3. The test data files are created successfully

## Local Development

For local development:

1. Install dependencies:
   ```
   npm install
   ```

2. Start the development server:
   ```
   npm run start
   ```

This will start both the frontend and backend servers in development mode.
