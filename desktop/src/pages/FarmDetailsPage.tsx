import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import dbService from '../services/dbService';
import syncService from '../services/syncService';

interface Farm {
  id: number;
  name: string;
  location: string;
  size: number;
  sizeUnit: 'acres' | 'hectares';
  owner: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

const FarmDetailsPage: React.FC = () => {
  const { farmId } = useParams<{ farmId: string }>();
  const navigate = useNavigate();
  const [farm, setFarm] = useState<Farm | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [formData, setFormData] = useState<Partial<Farm>>({});

  useEffect(() => {
    const loadFarm = async () => {
      if (!farmId) {
        setError('Farm ID is required');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        // Get farm from local database
        const farmData = await dbService.getInstance().getById<Farm>('farms', parseInt(farmId, 10));
        
        if (!farmData) {
          setError('Farm not found');
          setIsLoading(false);
          return;
        }
        
        setFarm(farmData);
        setFormData(farmData);
      } catch (err) {
        console.error('Failed to load farm:', err);
        setError('Failed to load farm details');
      } finally {
        setIsLoading(false);
      }
    };

    loadFarm();
  }, [farmId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!farmId) {
      setError('Farm ID is required');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      const updatedFarm: Farm = {
        ...(farm as Farm),
        ...formData,
        updatedAt: new Date().toISOString()
      };
      
      // Save to local database
      await dbService.getInstance().put<Farm>('farms', updatedFarm);
      
      // Add to sync queue
      syncService.getInstance().addToSyncQueue({
        type: 'update',
        entity: 'farms',
        id: updatedFarm.id,
        data: updatedFarm
      });
      
      setFarm(updatedFarm);
      setIsEditing(false);
    } catch (err) {
      console.error('Failed to update farm:', err);
      setError('Failed to update farm details');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading farm details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-6">
          <p>{error}</p>
        </div>
        <button
          onClick={() => navigate(-1)}
          className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
        >
          Go Back
        </button>
      </div>
    );
  }

  if (!farm) {
    return (
      <div className="p-6">
        <div className="bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-200 p-4 rounded-md mb-6">
          <p>Farm not found</p>
        </div>
        <button
          onClick={() => navigate(-1)}
          className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{isEditing ? 'Edit Farm' : 'Farm Details'}</h1>
        <div>
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded mr-2"
            >
              Edit Farm
            </button>
          ) : (
            <button
              onClick={() => setIsEditing(false)}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded mr-2"
            >
              Cancel
            </button>
          )}
          <button
            onClick={() => navigate(-1)}
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
          >
            Go Back
          </button>
        </div>
      </div>

      {isEditing ? (
        <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Farm Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            
            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Location
              </label>
              <input
                type="text"
                id="location"
                name="location"
                value={formData.location || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            
            <div>
              <label htmlFor="size" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Size
              </label>
              <input
                type="number"
                id="size"
                name="size"
                value={formData.size || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            
            <div>
              <label htmlFor="sizeUnit" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Size Unit
              </label>
              <select
                id="sizeUnit"
                name="sizeUnit"
                value={formData.sizeUnit || 'acres'}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                required
              >
                <option value="acres">Acres</option>
                <option value="hectares">Hectares</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="owner" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Owner
              </label>
              <input
                type="text"
                id="owner"
                name="owner"
                value={formData.owner || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
          </div>
          
          <div className="mt-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description || ''}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white"
            ></textarea>
          </div>
          
          <div className="mt-6 flex justify-end">
            <button
              type="button"
              onClick={() => setIsEditing(false)}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded mr-2"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded"
              disabled={isLoading}
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Farm Information</h3>
              <div className="space-y-3">
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Name:</span>
                  <span className="ml-2 font-medium">{farm.name}</span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Location:</span>
                  <span className="ml-2">{farm.location}</span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Size:</span>
                  <span className="ml-2">{farm.size} {farm.sizeUnit}</span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Owner:</span>
                  <span className="ml-2">{farm.owner}</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-2">Additional Information</h3>
              <div className="space-y-3">
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Created:</span>
                  <span className="ml-2">{new Date(farm.createdAt).toLocaleDateString()}</span>
                </div>
                <div>
                  <span className="text-gray-500 dark:text-gray-400">Last Updated:</span>
                  <span className="ml-2">{new Date(farm.updatedAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">Description</h3>
            <p className="text-gray-700 dark:text-gray-300">{farm.description || 'No description available.'}</p>
          </div>
          
          <div className="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
            <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
            <div className="flex flex-wrap gap-3">
              <button
                onClick={() => navigate(`/farms/${farm.id}/fields`)}
                className="bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded"
              >
                Manage Fields
              </button>
              <button
                onClick={() => navigate(`/farms/${farm.id}/tasks`)}
                className="bg-purple-600 hover:bg-purple-500 text-white px-4 py-2 rounded"
              >
                View Tasks
              </button>
              <button
                onClick={() => navigate(`/farms/${farm.id}/equipment`)}
                className="bg-yellow-600 hover:bg-yellow-500 text-white px-4 py-2 rounded"
              >
                Equipment
              </button>
              <button
                onClick={() => navigate(`/farms/${farm.id}/inventory`)}
                className="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded"
              >
                Inventory
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FarmDetailsPage;